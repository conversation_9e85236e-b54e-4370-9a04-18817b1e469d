/**
 * Tutorial Context
 * 
 * To disable the tutorial completely, set TUTORIAL_DISABLED = true below.
 * To re-enable the tutorial, set TUTORIAL_DISABLED = false.
 */
import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { useAuth } from './AuthContext';
import { userApi } from '../utils/api';
import { 
  TutorialState, 
  TutorialStep, 
  getInitialTutorialState, 
  getTutorialStep, 
  getNextTutorialStep,
  TUTORIAL_STEPS,
  updateTutorialProgress,
} from '../utils/tutorialSteps';

// Element measurement type
interface ElementMeasurement {
  x: number;
  y: number;
  width: number;
  height: number;
  pageX: number;
  pageY: number;
}

interface TutorialContextType {
  tutorialState: TutorialState;
  currentStep: TutorialStep | null;
  startTutorial: () => void;
  nextStep: () => void;
  completeTutorial: () => void;
  updateTutorialState: (updates: Partial<TutorialState>) => void;
  markTaskCreated: () => void;
  markRewardCreated: () => void;
  setCurrentScreen: (screen: string) => void;
  registerElement: (stepId: string, measurement: ElementMeasurement) => void;
  getCurrentHighlightMeasurement: () => ElementMeasurement | null;
  isPersisted: boolean;
  setPersisted: (value: boolean) => void;
}

const TutorialContext = createContext<TutorialContextType | undefined>(undefined);

export const useTutorial = () => {
  const context = useContext(TutorialContext);
  if (!context) {
    throw new Error('useTutorial must be used within a TutorialProvider');
  }
  return context;
};

interface TutorialProviderProps {
  children: ReactNode;
}

export const TutorialProvider: React.FC<TutorialProviderProps> = ({ children }) => {
  const { user, userProfile } = useAuth();
  const [tutorialState, setTutorialState] = useState<TutorialState>(getInitialTutorialState());
  const [isPersisted, setPersisted] = useState(false);
  
  // Tutorial disabled flag - set to true to completely disable the tutorial
  const TUTORIAL_DISABLED = true;
  
  // Store measurements of registered elements
  const elementMeasurements = useRef<Record<string, ElementMeasurement>>({});

  // Initialize tutorial state based on user profile
  useEffect(() => {
    if (TUTORIAL_DISABLED) {
      // If tutorial is disabled, never start it
      return;
    }
    
    if (user && userProfile) {
      // Always start from the first step (0) for better user experience
      const newStepIndex = 0;
      const newStepId = TUTORIAL_STEPS[0].id;
      
      const shouldStartTutorial = userProfile.onboarding_completed && !userProfile.tutorial_completed;
      
      if (shouldStartTutorial && !tutorialState.isActive) {
        // Only update if needed to prevent loops
        if (!tutorialState.isActive || 
            tutorialState.currentStepIndex !== newStepIndex || 
            tutorialState.currentStepId !== newStepId) {
          setTutorialState(prev => ({
            ...prev,
            isActive: true,
            currentStepIndex: newStepIndex,
            currentStepId: newStepId,
          }));
        }
      }
    }
  }, [user, userProfile]);

  const getCurrentStep = (): TutorialStep | null => {
    if (TUTORIAL_DISABLED) {
      return null; // Return null if tutorial is disabled
    }
    return getTutorialStep(tutorialState.currentStepId);
  };

  const updateTutorialState = (updates: Partial<TutorialState>) => {
    setTutorialState(prev => updateTutorialProgress(prev, updates));
  };

  const saveTutorialProgress = async (completed: boolean = false) => {
    try {
      await userApi.updateTutorialProgress({
        completed,
        currentStep: tutorialState.currentStepIndex,
      });
    } catch (error) {
      console.error('Error saving tutorial progress:', error);
    }
  };

  const startTutorial = () => {
    if (TUTORIAL_DISABLED) {
      return; // Don't start tutorial if disabled
    }
    
    setTutorialState(prev => ({
      ...prev,
      isActive: true,
      currentStepIndex: 0,
      currentStepId: TUTORIAL_STEPS[0].id,
    }));
  };

  const nextStep = () => {
    const nextStep = getNextTutorialStep(tutorialState.currentStepId);
    
    if (nextStep) {
      const nextIndex = TUTORIAL_STEPS.findIndex(step => step.id === nextStep.id);
      
      setTutorialState(prev => ({
        ...prev,
        currentStepIndex: nextIndex,
        currentStepId: nextStep.id,
      }));
      
      saveTutorialProgress(false);
    } else {
      completeTutorial();
    }
  };

  // The skip tutorial functionality has been removed as tutorial completion is mandatory

  const completeTutorial = () => {
    setTutorialState(prev => ({
      ...prev,
      isActive: false,
    }));
    
    saveTutorialProgress(true);
  };

  const markTaskCreated = () => {
    // First update the hasCreatedTask flag
    setTutorialState(prev => ({
      ...prev,
      hasCreatedTask: true,
    }));
    
    // Use a separate timeout to update step to avoid render loops
    // This avoids setting state twice in the same render cycle
    setTimeout(() => {
      // If we're in a task-related step, advance to the success step
      if (tutorialState.currentStepId.includes('task') && tutorialState.currentStepId !== 'task_created_success') {
        const successStepIndex = TUTORIAL_STEPS.findIndex(step => step.id === 'task_created_success');
        if (successStepIndex !== -1) {
          setTutorialState(prev => ({
            ...prev,
            currentStepIndex: successStepIndex,
            currentStepId: 'task_created_success',
          }));
        }
      }
    }, 0);
  };

  const markRewardCreated = () => {
    // First update the hasCreatedReward flag
    setTutorialState(prev => ({
      ...prev,
      hasCreatedReward: true,
    }));
    
    // Use a separate timeout to update step to avoid render loops
    // This avoids setting state twice in the same render cycle
    setTimeout(() => {
      // If we're in a reward-related step, advance to completion
      if (tutorialState.currentStepId.includes('reward')) {
        const completionStepIndex = TUTORIAL_STEPS.findIndex(step => step.id === 'tutorial_complete');
        if (completionStepIndex !== -1) {
          setTutorialState(prev => ({
            ...prev,
            currentStepIndex: completionStepIndex,
            currentStepId: 'tutorial_complete',
          }));
        }
      }
    }, 0);
  };

  const setCurrentScreen = (screen: string) => {
    // Only update if the screen actually changed to prevent loops
    if (tutorialState.currentScreen !== screen) {
      setTutorialState(prev => ({
        ...prev,
        currentScreen: screen,
      }));
    }
  };

  // New function to register elements with their measurements
  const registerElement = (stepId: string, measurement: ElementMeasurement) => {
    elementMeasurements.current[stepId] = measurement;
  };

  // Get current highlight measurements
  const getCurrentHighlightMeasurement = (): ElementMeasurement | null => {
    if (!tutorialState.isActive || !tutorialState.currentStepId) return null;
    return elementMeasurements.current[tutorialState.currentStepId] || null;
  };

  const value: TutorialContextType = React.useMemo(() => ({
    tutorialState,
    currentStep: getCurrentStep(),
    startTutorial,
    nextStep,
    completeTutorial,
    updateTutorialState,
    markTaskCreated,
    markRewardCreated,
    setCurrentScreen,
    registerElement,
    getCurrentHighlightMeasurement,
    isPersisted,
    setPersisted,
  }), [tutorialState]);

  return (
    <TutorialContext.Provider value={value}>
      {children}
    </TutorialContext.Provider>
  );
};

export default TutorialContext; 