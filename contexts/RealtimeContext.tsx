import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { RealtimeChannel } from '@supabase/supabase-js';
import { supabase } from '../utils/supabase';
import { useAuth } from './AuthContext';
// Removed old API imports - using direct Supabase calls instead
import * as Haptics from 'expo-haptics';

interface RealtimeContextType {
  isConnected: boolean;
  hasRealtimeAccess: boolean;
  taskUpdates: TaskUpdate[];
  childUpdates: ChildUpdate[];
  familyUpdates: FamilyUpdate[];
  markTaskUpdateSeen: (updateId: string) => void;
  markChildUpdateSeen: (updateId: string) => void;
  // New React Query integration stats
  syncStats: {
    channelCount: number;
    lastSyncTime: Date | null;
  };
}

interface TaskUpdate {
  id: string;
  type: 'task_completed' | 'task_created' | 'task_penalty';
  taskId: string;
  childId: string;
  childName: string;
  taskTitle: string;
  coinValue: number;
  timestamp: Date;
  seen: boolean;
  celebration?: boolean; // For child-facing celebrations
}

interface ChildUpdate {
  id: string;
  type: 'coins_updated' | 'level_up' | 'achievement_unlocked';
  childId: string;
  childName: string;
  newValue: number;
  oldValue?: number;
  timestamp: Date;
  seen: boolean;
}

interface FamilyUpdate {
  id: string;
  type: 'child_added' | 'settings_changed';
  message: string;
  timestamp: Date;
  seen: boolean;
}

const RealtimeContext = createContext<RealtimeContextType | undefined>(undefined);

export const useRealtime = () => {
  const context = useContext(RealtimeContext);
  if (!context) {
    throw new Error('useRealtime must be used within a RealtimeProvider');
  }
  return context;
};

interface RealtimeProviderProps {
  children: ReactNode;
}

export const RealtimeProvider: React.FC<RealtimeProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [hasRealtimeAccess, setHasRealtimeAccess] = useState(false);
  const [channel, setChannel] = useState<RealtimeChannel | null>(null);
  const [taskUpdates, setTaskUpdates] = useState<TaskUpdate[]>([]);
  const [childUpdates, setChildUpdates] = useState<ChildUpdate[]>([]);
  const [familyUpdates, setFamilyUpdates] = useState<FamilyUpdate[]>([]);
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null);

  // Integrate React Query real-time sync
  // Removed useRealtimeSync import and usage

  useEffect(() => {
    if (!user) {
      // Clean up when user logs out
      if (channel) {
        channel.unsubscribe();
        setChannel(null);
      }
      setIsConnected(false);
      setHasRealtimeAccess(false);
      setTaskUpdates([]);
      setChildUpdates([]);
      setFamilyUpdates([]);
      return;
    }

    // Check real-time access for this user using direct Supabase call
    const checkRealtimeAccess = async () => {
      try {
        // Use direct Supabase call to get subscription tier
        const { data: subscription, error } = await supabase
          .from('user_subscriptions')
          .select('tier')
          .eq('parent_id', user.id)
          .eq('status', 'active')
          .single();
        
        if (error) {
          console.error('Error fetching subscription tier:', error);
          setHasRealtimeAccess(false);
          return;
        }
        
        // Calculate real-time access based on subscription tier
        const hasRealtimeAccess = subscription?.tier !== 'free';
        
        setHasRealtimeAccess(hasRealtimeAccess);

        // If no access, don't set up real-time subscriptions
        if (!hasRealtimeAccess) {
          console.log('Real-time sync not available on current plan');
          return;
        }

        // Create a family-specific channel only if access is granted
        try {
          const familyChannel = supabase.channel(`family:${user.id}`, {
            config: {
              broadcast: { self: true },
              presence: { key: user.id },
            },
          });

          // Listen for task table changes
          familyChannel
            .on('postgres_changes', {
              event: '*',
              schema: 'public',
              table: 'tasks',
              filter: `parent_id=eq.${user.id}`,
            }, (payload) => {
              handleTaskChange(payload);
            })
            // Listen for children table changes
            .on('postgres_changes', {
              event: '*',
              schema: 'public',
              table: 'children',
              filter: `parent_id=eq.${user.id}`,
            }, (payload) => {
              handleChildChange(payload);
            })
            // Listen for family settings changes
            .on('postgres_changes', {
              event: '*',
              schema: 'public',
              table: 'family_settings',
              filter: `parent_id=eq.${user.id}`,
            }, (payload) => {
              handleFamilySettingsChange(payload);
            })
            // Listen for custom celebration broadcasts
            .on('broadcast', { event: 'task_celebration' }, (payload) => {
              handleTaskCelebration(payload);
            })
            .subscribe((status) => {
              setIsConnected(status === 'SUBSCRIBED');
              console.log('Realtime connection status:', status);
            });

          setChannel(familyChannel);
        } catch (error) {
          console.error('Error setting up realtime channel:', error);
          setHasRealtimeAccess(false);
        }
      } catch (error) {
        console.error('Error checking real-time access:', error);
        setHasRealtimeAccess(false);
        return;
      }
    };

    checkRealtimeAccess();

    return () => {
      if (channel) {
        channel.unsubscribe();
      }
    };
  }, [user]);

  const handleTaskChange = async (payload: any) => {
    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      // Validate payload structure
      if (!payload || !eventType) {
        console.warn('Invalid task change payload:', payload);
        return;
      }
      
      // Get child name for the update
      const childId = newRecord?.child_id || oldRecord?.child_id;
      if (!childId) {
        console.warn('No child ID in task change payload');
        return;
      }
      
      // Use direct Supabase call to get child data
      let childName = 'Child';
      try {
        const { data: child, error } = await supabase
          .from('children')
          .select('name')
          .eq('id', childId)
          .single();
        
        if (error) {
          console.error('Error fetching child data:', error);
        } else {
          childName = child?.name || 'Child';
        }
      } catch (error) {
        console.error('Error fetching child data:', error);
        // Continue with fallback name
      }

          if (eventType === 'UPDATE' && newRecord.status === 'completed' && oldRecord.status !== 'completed') {
        // Task completed
        const update: TaskUpdate = {
          id: `task_${newRecord.id}_${Date.now()}`,
          type: 'task_completed',
          taskId: newRecord.id,
          childId: newRecord.child_id,
          childName,
          taskTitle: newRecord.title,
          coinValue: newRecord.value,
          timestamp: new Date(),
          seen: false,
          celebration: true,
        };

        setTaskUpdates(prev => [update, ...prev.slice(0, 9)]); // Keep last 10
        setLastSyncTime(new Date()); // Track sync activity
        
        // Trigger haptic for celebration
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        console.log('🎉 REAL-TIME: Task completed notification triggered!');
      } else if (eventType === 'INSERT') {
        // New task created
        const update: TaskUpdate = {
          id: `task_${newRecord.id}_${Date.now()}`,
          type: 'task_created',
          taskId: newRecord.id,
          childId: newRecord.child_id,
          childName,
          taskTitle: newRecord.title,
          coinValue: newRecord.value,
          timestamp: new Date(),
          seen: false,
        };

        setTaskUpdates(prev => [update, ...prev.slice(0, 9)]);
        setLastSyncTime(new Date());
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        
        console.log('🔥 REAL-TIME: New task created notification!');
      }
    } catch (error) {
      console.error('Error handling task change:', error);
    }
  };

  const handleChildChange = (payload: any) => {
    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      // Validate payload structure
      if (!payload || !eventType) {
        console.warn('Invalid child change payload:', payload);
        return;
      }
    
    if (eventType === 'UPDATE') {
      // Check for coin balance changes
      if (newRecord.coin_balance !== oldRecord.coin_balance) {
        const update: ChildUpdate = {
          id: `child_${newRecord.id}_coins_${Date.now()}`,
          type: 'coins_updated',
          childId: newRecord.id,
          childName: newRecord.name,
          newValue: newRecord.coin_balance,
          oldValue: oldRecord.coin_balance,
          timestamp: new Date(),
          seen: false,
        };

        setChildUpdates(prev => [update, ...prev.slice(0, 9)]);
        setLastSyncTime(new Date());
        
        console.log('🪙 REAL-TIME: Child coins updated!', { childName: newRecord.name, change: newRecord.coin_balance - oldRecord.coin_balance });
      }

      // Check for level changes
      if (newRecord.level > oldRecord.level) {
        const update: ChildUpdate = {
          id: `child_${newRecord.id}_level_${Date.now()}`,
          type: 'level_up',
          childId: newRecord.id,
          childName: newRecord.name,
          newValue: newRecord.level,
          oldValue: oldRecord.level,
          timestamp: new Date(),
          seen: false,
        };

        setChildUpdates(prev => [update, ...prev.slice(0, 9)]);
        setLastSyncTime(new Date());
        
        console.log('🪙 REAL-TIME: Child coins updated!', { childName: newRecord.name, change: newRecord.coin_balance - oldRecord.coin_balance });
        setLastSyncTime(new Date());
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        
        console.log('🆙 REAL-TIME: Child leveled up!', { childName: newRecord.name, newLevel: newRecord.level });
      }
    }
    } catch (error) {
      console.error('Error handling child change:', error);
    }
  };

  const handleFamilySettingsChange = (payload: any) => {
    try {
      const update: FamilyUpdate = {
        id: `family_settings_${Date.now()}`,
        type: 'settings_changed',
        message: 'Family settings updated',
        timestamp: new Date(),
        seen: false,
      };

      setFamilyUpdates(prev => [update, ...prev.slice(0, 4)]);
    } catch (error) {
      console.error('Error handling family settings change:', error);
    }
  };

  const handleTaskCelebration = (payload: any) => {
    try {
      const { childName, taskTitle, coins } = payload.payload;
      
      const update: TaskUpdate = {
        id: `celebration_${Date.now()}`,
        type: 'task_completed',
        taskId: '',
        childId: '',
        childName,
        taskTitle,
        coinValue: coins,
        timestamp: new Date(),
        seen: false,
        celebration: true,
      };

      setTaskUpdates(prev => [update, ...prev.slice(0, 9)]);
      
      // Big celebration haptic
      setTimeout(() => Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success), 100);
      setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy), 300);
    } catch (error) {
      console.error('Error handling task celebration:', error);
    }
  };

  const markTaskUpdateSeen = (updateId: string) => {
    setTaskUpdates(prev => 
      prev.map(update => 
        update.id === updateId ? { ...update, seen: true } : update
      )
    );
  };

  const markChildUpdateSeen = (updateId: string) => {
    setChildUpdates(prev => 
      prev.map(update => 
        update.id === updateId ? { ...update, seen: true } : update
      )
    );
  };

  const value: RealtimeContextType = {
    isConnected: isConnected, // Combined connection status
    hasRealtimeAccess,
    taskUpdates,
    childUpdates,
    familyUpdates,
    markTaskUpdateSeen,
    markChildUpdateSeen,
    syncStats: {
      channelCount: 0, // No longer tracking channel count
      lastSyncTime,
    },
  };

  return (
    <RealtimeContext.Provider value={value}>
      {children}
    </RealtimeContext.Provider>
  );
}; 