import React, { createContext, useContext, useState, useCallback } from 'react';
import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';
import { AUDIO_CONFIG, SoundType, InteractionType, INTERACTION_SOUND_MAP } from '../utils/audioConfig';

interface AudioContextType {
  // Background music controls
  playBackgroundMusic: (track?: 'onboarding' | 'main' | 'celebration', forcePlay?: boolean) => Promise<void>;
  pauseBackgroundMusic: () => Promise<void>;
  stopBackgroundMusic: () => Promise<void>;
  setBackgroundVolume: (volume: number) => Promise<void>;
  
  // Sound effects
  playSound: (soundType: SoundType) => Promise<void>;
  setSoundEffectVolume: (volume: number) => void;
  
  // Interactive sounds with haptics
  playInteraction: (type: InteractionType, hapticType?: 'light' | 'medium' | 'heavy') => Promise<void>;
  
  // Settings
  backgroundMusicEnabled: boolean;
  soundEffectsEnabled: boolean;
  masterVolume: number;
  setBackgroundMusicEnabled: (enabled: boolean) => void;
  setSoundEffectsEnabled: (enabled: boolean) => void;
  setMasterVolume: (volume: number) => void;
}

const AudioContext = createContext<AudioContextType | undefined>(undefined);

interface AudioProviderProps {
  children: React.ReactNode;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {
  // Settings state - initialized from AUDIO_CONFIG
  const [backgroundMusicEnabled, setBackgroundMusicEnabledState] = useState(true);
  const [soundEffectsEnabled, setSoundEffectsEnabled] = useState(true);
  const [masterVolume, setMasterVolume] = useState(AUDIO_CONFIG.masterVolume);
  const [backgroundVolume, setBackgroundVolumeState] = useState(AUDIO_CONFIG.backgroundMusic.defaultVolume);
  const [soundEffectVolume, setSoundEffectVolumeState] = useState(AUDIO_CONFIG.soundEffects.defaultVolume);
  const [currentBackgroundTrack, setCurrentBackgroundTrack] = useState<string | null>(null);
  const [lastPlayedTrack, setLastPlayedTrack] = useState<'onboarding' | 'main' | 'celebration'>('main');

  // Background music functions (now just console logs)
  const playBackgroundMusic = useCallback(async (track: 'onboarding' | 'main' | 'celebration' = 'main', forcePlay: boolean = false) => {
    if (!backgroundMusicEnabled && !forcePlay) {
      console.log('🎵 Background music disabled, skipping playback');
      return;
    }

    console.log(`🎼 Playing background music: ${track}`);
    setCurrentBackgroundTrack(track);
    setLastPlayedTrack(track);
  }, [backgroundMusicEnabled]);

  const pauseBackgroundMusic = useCallback(async () => {
    console.log('🎵 Background music paused');
  }, []);

  const stopBackgroundMusic = async () => {
    console.log('🎵 Background music stopped');
    setCurrentBackgroundTrack(null);
  };

  const setBackgroundVolume = async (volume: number) => {
    setBackgroundVolumeState(volume);
    console.log(`🔊 Background volume set to: ${volume * masterVolume}`);
  };

  // Sound effect functions (now just console logs)
  const playSound = async (soundType: SoundType) => {
    if (!soundEffectsEnabled) {
      console.log(`🔇 Sound effects disabled, skipping: ${soundType}`);
      return;
    }

    console.log(`🔊 Playing sound effect: ${soundType}`);
  };

  const setSoundEffectVolume = (volume: number) => {
    setSoundEffectVolumeState(volume);
    console.log(`🔊 Sound effect volume set to: ${volume * masterVolume}`);
  };

  // Interactive sounds with haptics (haptics still work)
  const playInteraction = async (type: InteractionType, hapticType?: 'light' | 'medium' | 'heavy') => {
    const mapping = INTERACTION_SOUND_MAP[type];
    await playSound(mapping.sound); // This will just log
    
    // Haptics still work on iOS
    if (Platform.OS === 'ios') {
      const hapticIntensity = hapticType || mapping.haptic;
      try {
        switch (hapticIntensity) {
          case 'light':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            break;
        }
      } catch (error) {
        console.error('Error playing haptic feedback:', error);
      }
    }
  };

  // Settings functions
  const setBackgroundMusicEnabled = useCallback((enabled: boolean) => {
    setBackgroundMusicEnabledState(enabled);
    console.log(`Background music enabled: ${enabled}`);
    
    if (!enabled && currentBackgroundTrack) {
      stopBackgroundMusic();
    } else if (enabled && !currentBackgroundTrack) {
      playBackgroundMusic(lastPlayedTrack);
    }
  }, [currentBackgroundTrack, lastPlayedTrack, playBackgroundMusic]);

  const setSoundEffectsEnabledHandler = useCallback((enabled: boolean) => {
    setSoundEffectsEnabled(enabled);
    console.log(`Sound effects enabled: ${enabled}`);
  }, []);

  const setMasterVolumeHandler = useCallback((volume: number) => {
    setMasterVolume(volume);
    console.log(`Master volume set to: ${volume}`);
  }, []);

  const value: AudioContextType = {
    playBackgroundMusic,
    pauseBackgroundMusic,
    stopBackgroundMusic,
    setBackgroundVolume,
    playSound,
    setSoundEffectVolume,
    playInteraction,
    backgroundMusicEnabled,
    soundEffectsEnabled,
    masterVolume,
    setBackgroundMusicEnabled,
    setSoundEffectsEnabled: setSoundEffectsEnabledHandler,
    setMasterVolume: setMasterVolumeHandler,
  };

  return (
    <AudioContext.Provider value={value}>
      {children}
    </AudioContext.Provider>
  );
};

export const useAudio = (): AudioContextType => {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

export default AudioContext;