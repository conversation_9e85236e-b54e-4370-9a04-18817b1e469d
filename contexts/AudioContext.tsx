import React, { createContext, useContext, useEffect, useState, useRef, useCallback } from 'react';
import { createAudioPlayer, setAudioModeAsync } from 'expo-audio';
import { Platform } from 'react-native';
import * as Haptics from 'expo-haptics';
import { AUDIO_CONFIG, SoundType, InteractionType, INTERACTION_SOUND_MAP } from '../utils/audioConfig';

interface AudioContextType {
  // Background music controls
  playBackgroundMusic: (track?: 'onboarding' | 'main' | 'celebration', forcePlay?: boolean) => Promise<void>;
  pauseBackgroundMusic: () => Promise<void>;
  stopBackgroundMusic: () => Promise<void>;
  setBackgroundVolume: (volume: number) => Promise<void>;
  
  // Sound effects
  playSound: (soundType: SoundType) => Promise<void>;
  setSoundEffectVolume: (volume: number) => void;
  
  // Combined audio + haptic feedback
  playInteraction: (type: InteractionType, hapticType?: 'light' | 'medium' | 'heavy') => Promise<void>;
  
  // Settings
  backgroundMusicEnabled: boolean;
  soundEffectsEnabled: boolean;
  masterVolume: number;
  setBackgroundMusicEnabled: (enabled: boolean) => void;
  setSoundEffectsEnabled: (enabled: boolean) => void;
  setMasterVolume: (volume: number) => void;
}



const AudioContext = createContext<AudioContextType | undefined>(undefined);

interface AudioProviderProps {
  children: React.ReactNode;
}

export const AudioProvider: React.FC<AudioProviderProps> = ({ children }) => {
  // Audio players - using refs to prevent useInsertionEffect errors
  const backgroundMusicPlayer = useRef<AudioPlayer | null>(null);
  const [backgroundMusicStatus, setBackgroundMusicStatus] = useState({ isLoaded: false, playing: false });
  const soundEffectPlayers = useRef<{ [key in SoundType]?: AudioPlayer }>({});

  // Settings state - initialized from AUDIO_CONFIG
  const [backgroundMusicEnabled, setBackgroundMusicEnabledState] = useState(true);
  const [soundEffectsEnabled, setSoundEffectsEnabled] = useState(true);
  const [masterVolume, setMasterVolume] = useState(AUDIO_CONFIG.masterVolume);
  const [backgroundVolume, setBackgroundVolumeState] = useState(AUDIO_CONFIG.backgroundMusic.defaultVolume);
  const [soundEffectVolume, setSoundEffectVolumeState] = useState(AUDIO_CONFIG.soundEffects.defaultVolume);

  // Loading states
  const [isAudioInitialized, setIsAudioInitialized] = useState(false);
  const [currentBackgroundTrack, setCurrentBackgroundTrack] = useState<string | null>(null);
  const [lastPlayedTrack, setLastPlayedTrack] = useState<'onboarding' | 'main' | 'celebration'>('main');
  const prevBackgroundMusicEnabled = useRef(backgroundMusicEnabled);
  const isInitialized = useRef(false);

  useEffect(() => {
    // Defer audio initialization to avoid useInsertionEffect warnings
    setTimeout(() => {
      initializeAudio();
    }, 0);
    return () => {
      cleanup();
    };
  }, []);

  const initializeAudio = async () => {
    try {
      // Configure audio mode to play in silent mode like Spotify/games
      await setAudioModeAsync({
        playsInSilentMode: true,        // 🔑 Key setting: Play audio even in silent mode
        allowsRecording: false,         // We don't need recording
        shouldPlayInBackground: true,   // Allow background playbook
      });

      console.log('✅ Audio mode configured for silent mode playback');

      // Create background music player without using hooks
      const bgPlayer = createAudioPlayer();
      backgroundMusicPlayer.current = bgPlayer;

      // Initialize sound effect players
      await initializeSoundEffectPlayers();
      setIsAudioInitialized(true);
      isInitialized.current = true;
      console.log('Audio system initialized with expo-audio');
    } catch (error) {
      console.error('Error initializing audio:', error);
    }
  };

    const initializeSoundEffectPlayers = async () => {
    // Initialize sound effect players using centralized config
    try {
      console.log('Audio system initialized - loading sound files from config');
      
      // Create audio players for each sound type from config
      Object.entries(AUDIO_CONFIG.soundEffects.files).forEach(([soundType, soundConfig]) => {
        try {
          // Create an audio player for this sound effect
          const player = createAudioPlayer(soundConfig.file);
          soundEffectPlayers.current[soundType as SoundType] = player;
          console.log(`✅ Loaded sound: ${soundType} (volume: ${soundConfig.volume})`);
        } catch (error) {
          console.warn(`❌ Could not create player for ${soundType}:`, error);
        }
      });
      
    } catch (error) {
      console.error('Error initializing sound effect players:', error);
    }
  };

  const playBackgroundMusic = useCallback(async (track: 'onboarding' | 'main' | 'celebration' = 'main', forcePlay: boolean = false) => {
    if ((!backgroundMusicEnabled && !forcePlay) || !isAudioInitialized || !backgroundMusicPlayer.current) return;

    try {
      // Stop current background music if playing
      if (backgroundMusicStatus.isLoaded) {
        backgroundMusicPlayer.current.pause();
      }

      // Load and play background music from config
      console.log(`🎼 Playing background music: ${track}`);
      setCurrentBackgroundTrack(track);
      setLastPlayedTrack(track);

      const musicFile = AUDIO_CONFIG.backgroundMusic.tracks[track];
      if (musicFile) {
        backgroundMusicPlayer.current.replace(musicFile);
        backgroundMusicPlayer.current.volume = backgroundVolume * masterVolume;
        backgroundMusicPlayer.current.loop = true;
        backgroundMusicPlayer.current.play();
        setBackgroundMusicStatus({ isLoaded: true, playing: true });
        console.log(`🎵 Background music started: ${track} (volume: ${backgroundVolume * masterVolume})`);
      } else {
        console.warn(`❌ No music file found for track: ${track}`);
      }

    } catch (error) {
      console.error('Error playing background music:', error);
    }
  }, [backgroundMusicEnabled, isAudioInitialized, backgroundMusicStatus.isLoaded, backgroundVolume, masterVolume]);

  const pauseBackgroundMusic = useCallback(async () => {
    try {
      if (backgroundMusicStatus.isLoaded && backgroundMusicPlayer.current) {
        backgroundMusicPlayer.current.pause();
        setBackgroundMusicStatus({ isLoaded: true, playing: false });
        // Keep currentBackgroundTrack for resuming
      }
    } catch (error) {
      console.error('Error pausing background music:', error);
    }
  }, [backgroundMusicStatus.isLoaded]);

  const stopBackgroundMusic = async () => {
    try {
      if (backgroundMusicStatus.isLoaded && backgroundMusicPlayer.current) {
        backgroundMusicPlayer.current.pause();
        setBackgroundMusicStatus({ isLoaded: false, playing: false });
        setCurrentBackgroundTrack(null);
      }
    } catch (error) {
      console.error('Error stopping background music:', error);
    }
  };

  const setBackgroundVolume = async (volume: number) => {
    setBackgroundVolumeState(volume);
    if (backgroundMusicStatus.isLoaded && backgroundMusicPlayer.current) {
      try {
        backgroundMusicPlayer.current.volume = volume * masterVolume;
      } catch (error) {
        console.error('Error setting background volume:', error);
      }
    }
  };

  const playSound = async (soundType: SoundType) => {
    if (!soundEffectsEnabled || !isAudioInitialized) return;

    const player = soundEffectPlayers.current[soundType];
    const soundConfig = AUDIO_CONFIG.soundEffects.files[soundType];
    
    if (player && soundConfig) {
      try {
        // In expo-audio, we can replay sounds by seeking to start and playing
        player.seekTo(0);
        // Use individual sound volume * sound effects volume * master volume
        const finalVolume = soundConfig.volume * soundEffectVolume * masterVolume;
        player.volume = finalVolume;
        player.play();
        console.log(`🔊 Playing sound: ${soundType} (volume: ${finalVolume.toFixed(2)})`);
      } catch (error) {
        console.error(`❌ Error playing sound effect: ${soundType}`, error);
      }
    } else {
      console.warn(`❌ No audio player or config found for sound type: ${soundType}`);
    }
  };

  const setSoundEffectVolume = (volume: number) => {
    setSoundEffectVolumeState(volume);
    // Update volume for all loaded sound effects using their individual config
    Object.entries(soundEffectPlayers.current).forEach(([soundType, player]) => {
      if (player) {
        const soundConfig = AUDIO_CONFIG.soundEffects.files[soundType as SoundType];
        if (soundConfig) {
          try {
            // Apply individual sound volume * new sound effects volume * master volume
            const finalVolume = soundConfig.volume * volume * masterVolume;
            player.volume = finalVolume;
          } catch (error) {
            console.error(`Error setting volume for ${soundType}:`, error);
          }
        }
      }
    });
  };

  const playInteraction = async (type: InteractionType, hapticType?: 'light' | 'medium' | 'heavy') => {
    const mapping = INTERACTION_SOUND_MAP[type];
    
    // Play sound effect
    await playSound(mapping.sound);
    
    // Trigger haptic feedback (iOS only)
    if (Platform.OS === 'ios') {
      const hapticIntensity = hapticType || mapping.haptic;
      try {
        switch (hapticIntensity) {
          case 'light':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            break;
          case 'medium':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
            break;
          case 'heavy':
            await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
            break;
        }
      } catch (error) {
        console.error('Error triggering haptic feedback:', error);
      }
    }
  };

  const cleanup = async () => {
    // Stop and clean up background music
    try {
      if (backgroundMusicStatus.isLoaded && backgroundMusicStatus.playing && backgroundMusicPlayer.current) {
        backgroundMusicPlayer.current.pause();
      }
      // Only remove if it's loaded
      if (backgroundMusicStatus.isLoaded && backgroundMusicPlayer.current) {
        backgroundMusicPlayer.current.remove();
      }
    } catch (error) {
      console.error('Error cleaning up background music:', error);
    }

    // Clean up sound effect players
    Object.values(soundEffectPlayers.current).forEach((player) => {
      if (player) {
        try {
          player.pause();
          player.remove(); // Properly cleanup the audio player
        } catch (error) {
          console.error('Error cleaning up sound effect player:', error);
        }
      }
    });
  };

  // Update volumes when master volume changes
  useEffect(() => {
    // Defer volume updates to avoid useInsertionEffect warnings
    setTimeout(() => {
      if (backgroundMusicStatus.isLoaded) {
        setBackgroundVolume(backgroundVolume);
      }
      setSoundEffectVolume(soundEffectVolume);
    }, 0);
  }, [masterVolume]);

  // Custom setter for background music that handles audio logic
  const setBackgroundMusicEnabled = useCallback((enabled: boolean) => {
    setBackgroundMusicEnabledState(enabled);

    // Defer audio operations to avoid useInsertionEffect warnings
    setTimeout(() => {
      if (!enabled) {
        // User disabled background music - pause it (but keep track info for resuming)
        if (backgroundMusicStatus.isLoaded && backgroundMusicStatus.playing) {
          console.log('🔇 Background music disabled - pausing playback');
          pauseBackgroundMusic();
        }
      } else {
        // User enabled background music - resume playing
        if (isAudioInitialized) {
          const trackToPlay = currentBackgroundTrack || lastPlayedTrack;
          console.log(`🔊 Background music re-enabled - resuming ${trackToPlay} track`);
          playBackgroundMusic(trackToPlay as 'onboarding' | 'main' | 'celebration', true);
        }
      }
    }, 0);
  }, [backgroundMusicStatus.isLoaded, backgroundMusicStatus.playing, pauseBackgroundMusic, isAudioInitialized, currentBackgroundTrack, lastPlayedTrack, playBackgroundMusic]);

  const value: AudioContextType = {
    playBackgroundMusic,
    pauseBackgroundMusic,
    stopBackgroundMusic,
    setBackgroundVolume,
    playSound,
    setSoundEffectVolume,
    playInteraction,
    backgroundMusicEnabled,
    soundEffectsEnabled,
    masterVolume,
    setBackgroundMusicEnabled,
    setSoundEffectsEnabled,
    setMasterVolume,
  };

  return (
    <AudioContext.Provider value={value}>
      {children}
    </AudioContext.Provider>
  );
};

export const useAudio = (): AudioContextType => {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error('useAudio must be used within an AudioProvider');
  }
  return context;
};

export default AudioContext; 