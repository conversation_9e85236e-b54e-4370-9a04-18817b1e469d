import { Alert } from 'react-native';

// OpenAI API Configuration
const OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY || '';
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

// Age group definitions
export const AGE_GROUPS = {
  TODDLER: { min: 3, max: 5, label: '<PERSON><PERSON> (3-5)' },
  PRESCHOOL: { min: 6, max: 8, label: 'Preschool (6-8)' },
  ELEMENTARY: { min: 9, max: 12, label: 'Elementary (9-12)' },
  MIDDLE_SCHOOL: { min: 13, max: 15, label: 'Middle School (13-15)' },
  HIGH_SCHOOL: { min: 16, max: 18, label: 'High School (16-18)' },
  YOUNG_ADULT: { min: 19, max: 25, label: 'Young Adult (19-25)' },
};

// Task categories with emojis
export const TASK_CATEGORIES = {
  HOUSEHOLD: { name: 'Household', emoji: '🏠', description: 'Chores and home maintenance' },
  PERSONAL: { name: 'Personal', emoji: '🧘', description: 'Self-care and personal hygiene' },
  ACADEMIC: { name: 'Academic', emoji: '📚', description: 'Learning and study tasks' },
  CREATIVE: { name: 'Creative', emoji: '🎨', description: 'Art, music, and creative projects' },
  PHYSICAL: { name: 'Physical', emoji: '🏃', description: 'Exercise and outdoor activities' },
  SOCIAL: { name: 'Social', emoji: '👥', description: 'Family and social interactions' },
  LIFE_SKILLS: { name: 'Life Skills', emoji: '🛠️', description: 'Practical life skills' },
  KINDNESS: { name: 'Kindness', emoji: '❤️', description: 'Acts of kindness and helping others' },
  RESPONSIBILITY: { name: 'Responsibility', emoji: '⚖️', description: 'Taking care of belongings and duties' },
  LEARNING: { name: 'Learning', emoji: '🧠', description: 'New skills and knowledge' },
};

// Reward categories
export const REWARD_CATEGORIES = {
  TREATS: { name: 'Treats', emoji: '🍭', description: 'Food treats and snacks' },
  ACTIVITIES: { name: 'Activities', emoji: '🎯', description: 'Fun activities and experiences' },
  PRIVILEGES: { name: 'Privileges', emoji: '⭐', description: 'Special privileges and freedoms' },
  TOYS: { name: 'Toys', emoji: '🧸', description: 'Toys and games' },
  OUTINGS: { name: 'Outings', emoji: '🎪', description: 'Trips and outings' },
  SCREEN_TIME: { name: 'Screen Time', emoji: '📱', description: 'Device and screen time' },
  MONEY: { name: 'Money', emoji: '💰', description: 'Cash and savings' },
  EXPERIENCES: { name: 'Experiences', emoji: '🎭', description: 'Special experiences and events' },
};

// Interface definitions
export interface GeneratedTask {
  title: string;
  description: string;
  category: string;
  age_group: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimated_time: number; // in minutes
  coin_value: number;
  instructions: string[];
  tips?: string[];
  safety_notes?: string[];
}

export interface GeneratedReward {
  title: string;
  description: string;
  category: string;
  age_group: string;
  cost: number;
  availability: 'always' | 'weekends' | 'special_occasions';
  notes?: string;
}

export interface TaskGenerationRequest {
  age: number;
  interests?: string[];
  categories?: string[];
  difficulty?: 'easy' | 'medium' | 'hard';
  count?: number;
  family_context?: string;
  child_name?: string;
  completed_tasks?: string[];
}

export interface RewardGenerationRequest {
  age: number;
  interests?: string[];
  categories?: string[];
  budget_range?: { min: number; max: number };
  count?: number;
  family_context?: string;
  child_name?: string;
}

// Utility function to get age group from age
export const getAgeGroup = (age: number): string => {
  for (const [key, group] of Object.entries(AGE_GROUPS)) {
    if (age >= group.min && age <= group.max) {
      return key;
    }
  }
  return 'ELEMENTARY'; // Default fallback
};

// Utility function to get age group label
export const getAgeGroupLabel = (age: number): string => {
  const ageGroup = getAgeGroup(age);
  return AGE_GROUPS[ageGroup as keyof typeof AGE_GROUPS]?.label || 'Elementary (9-12)';
};

// OpenAI API call function
const callOpenAI = async (messages: any[], model: string = 'gpt-4o-mini'): Promise<any> => {
  if (!OPENAI_API_KEY) {
    throw new Error('OpenAI API key not configured. Please set EXPO_PUBLIC_OPENAI_API_KEY in your environment.');
  }

  try {
    const response = await fetch(OPENAI_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${OPENAI_API_KEY}`,
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: 0.8,
        max_tokens: 2000,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`OpenAI API error: ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('OpenAI API call failed:', error);
    throw error;
  }
};

// Generate age-appropriate tasks using AI
export const generateAITasks = async (request: TaskGenerationRequest): Promise<GeneratedTask[]> => {
  const ageGroup = getAgeGroup(request.age);
  const ageGroupLabel = getAgeGroupLabel(request.age);
  
  const systemPrompt = `You are a family task management expert specializing in age-appropriate chores and activities. 
  Generate creative, engaging, and developmentally appropriate tasks for children.
  
  IMPORTANT: Return ONLY valid JSON array format. No additional text or explanation.
  
  Consider these guidelines:
  - Tasks should be safe and appropriate for the child's age
  - Include clear, simple instructions
  - Suggest reasonable coin values (1-100 coins)
  - Estimated time should be realistic
  - Include helpful tips for parents
  - Consider the child's developmental stage
  
  Categories available: ${Object.keys(TASK_CATEGORIES).join(', ')}
  
  Return an array of task objects with this exact structure:
  {
    "title": "Task name",
    "description": "Brief description",
    "category": "CATEGORY_NAME",
    "age_group": "${ageGroup}",
    "difficulty": "easy|medium|hard",
    "estimated_time": 15,
    "coin_value": 25,
    "instructions": ["Step 1", "Step 2", "Step 3"],
    "tips": ["Helpful tip for parents"],
    "safety_notes": ["Safety consideration if needed"]
  }`;

  const userPrompt = `Generate ${request.count || 5} tasks for a ${request.age}-year-old child (${ageGroupLabel}).
  
  ${request.child_name ? `Child's name: ${request.child_name}` : ''}
  ${request.interests?.length ? `Interests: ${request.interests.join(', ')}` : ''}
  ${request.categories?.length ? `Focus on categories: ${request.categories.join(', ')}` : ''}
  ${request.difficulty ? `Difficulty level: ${request.difficulty}` : ''}
  ${request.family_context ? `Family context: ${request.family_context}` : ''}
  ${request.completed_tasks?.length ? `Already completed: ${request.completed_tasks.join(', ')}` : ''}
  
  Make tasks engaging, age-appropriate, and fun while building important life skills.`;

  try {
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await callOpenAI(messages);
    const content = response.choices[0].message.content;
    
    // Parse JSON response
    const tasks = JSON.parse(content);
    
    // Validate and sanitize tasks
    return tasks.filter((task: any) => 
      task.title && 
      task.description && 
      task.category && 
      task.coin_value > 0 && 
      task.estimated_time > 0
    );
    
  } catch (error) {
    console.error('Error generating AI tasks:', error);
    throw new Error('Failed to generate AI tasks. Please try again.');
  }
};

// Generate age-appropriate rewards using AI
export const generateAIRewards = async (request: RewardGenerationRequest): Promise<GeneratedReward[]> => {
  const ageGroup = getAgeGroup(request.age);
  const ageGroupLabel = getAgeGroupLabel(request.age);
  
  const systemPrompt = `You are a family reward system expert specializing in age-appropriate incentives and rewards.
  Generate creative, engaging, and developmentally appropriate rewards for children.
  
  IMPORTANT: Return ONLY valid JSON array format. No additional text or explanation.
  
  Consider these guidelines:
  - Rewards should be safe and appropriate for the child's age
  - Include both tangible and experience-based rewards
  - Suggest reasonable coin costs (5-1000 coins)
  - Balance instant gratification with longer-term goals
  - Consider family budget and practicality
  
  Categories available: ${Object.keys(REWARD_CATEGORIES).join(', ')}
  
  Return an array of reward objects with this exact structure:
  {
    "title": "Reward name",
    "description": "Brief description",
    "category": "CATEGORY_NAME",
    "age_group": "${ageGroup}",
    "cost": 50,
    "availability": "always|weekends|special_occasions",
    "notes": "Additional context or requirements"
  }`;

  const userPrompt = `Generate ${request.count || 5} rewards for a ${request.age}-year-old child (${ageGroupLabel}).
  
  ${request.child_name ? `Child's name: ${request.child_name}` : ''}
  ${request.interests?.length ? `Interests: ${request.interests.join(', ')}` : ''}
  ${request.categories?.length ? `Focus on categories: ${request.categories.join(', ')}` : ''}
  ${request.budget_range ? `Budget range: ${request.budget_range.min}-${request.budget_range.max} coins` : ''}
  ${request.family_context ? `Family context: ${request.family_context}` : ''}
  
  Make rewards motivating, age-appropriate, and achievable while encouraging positive behavior.`;

  try {
    const messages = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ];

    const response = await callOpenAI(messages);
    const content = response.choices[0].message.content;
    
    // Parse JSON response
    const rewards = JSON.parse(content);
    
    // Validate and sanitize rewards
    return rewards.filter((reward: any) => 
      reward.title && 
      reward.description && 
      reward.category && 
      reward.cost > 0
    );
    
  } catch (error) {
    console.error('Error generating AI rewards:', error);
    throw new Error('Failed to generate AI rewards. Please try again.');
  }
};

// Generate task suggestions based on child's profile and history
export const generatePersonalizedTasks = async (
  childProfile: any,
  taskHistory: any[],
  preferences: any = {}
): Promise<GeneratedTask[]> => {
  const completedCategories = taskHistory.reduce((acc, task) => {
    acc[task.category] = (acc[task.category] || 0) + 1;
    return acc;
  }, {});

  const leastUsedCategories = Object.entries(TASK_CATEGORIES)
    .filter(([key]) => !completedCategories[key] || completedCategories[key] < 3)
    .map(([key]) => key);

  const request: TaskGenerationRequest = {
    age: childProfile.age || 10,
    child_name: childProfile.name,
    categories: leastUsedCategories.slice(0, 3),
    count: 3,
    completed_tasks: taskHistory.slice(0, 10).map(t => t.title),
    family_context: preferences.family_context || '',
    interests: preferences.interests || [],
  };

  return generateAITasks(request);
};

// Generate reward suggestions based on child's profile and coin balance
export const generatePersonalizedRewards = async (
  childProfile: any,
  preferences: any = {}
): Promise<GeneratedReward[]> => {
  const request: RewardGenerationRequest = {
    age: childProfile.age || 10,
    child_name: childProfile.name,
    budget_range: {
      min: Math.max(5, Math.floor(childProfile.coin_balance * 0.1)),
      max: Math.min(1000, childProfile.coin_balance * 2),
    },
    count: 5,
    family_context: preferences.family_context || '',
    interests: preferences.interests || [],
  };

  return generateAIRewards(request);
};

// Error handling wrapper for AI functions
export const withAIErrorHandling = async <T>(
  aiFunction: () => Promise<T>,
  fallback?: T
): Promise<T | null> => {
  try {
    return await aiFunction();
  } catch (error) {
    console.error('AI function error:', error);
    
    // Show user-friendly error message
    Alert.alert(
      'AI Generation Error',
      'Unable to generate suggestions right now. Please try again or use the manual creation options.',
      [{ text: 'OK' }]
    );
    
    return fallback || null;
  }
};

// Check if OpenAI is configured
export const isOpenAIConfigured = (): boolean => {
  return Boolean(OPENAI_API_KEY);
};

// Get OpenAI status message
export const getOpenAIStatusMessage = (): string => {
  if (!isOpenAIConfigured()) {
    return 'OpenAI API not configured. Add your API key to enable AI features.';
  }
  return 'AI features are ready to use!';
}; 