// === ADVANCED PERFORMANCE MONITORING SYSTEM ===
// Tracks API performance, cache hits, and user experience metrics

interface PerformanceMetric {
  operation: string;
  duration: number;
  timestamp: Date;
  cacheHit?: boolean;
  optimized?: boolean;
  userId?: string;
  metadata?: Record<string, any>;
}

interface PerformanceStats {
  averageResponseTime: number;
  cacheHitRate: number;
  totalOperations: number;
  optimizedOperations: number;
  slowestOperations: PerformanceMetric[];
  fastestOperations: PerformanceMetric[];
}

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 metrics
  private readonly slowOperationThreshold = 1000; // 1 second

  // Track API call performance
  trackApiCall(operation: string, startTime: number, metadata?: Record<string, any>) {
    const duration = Date.now() - startTime;
    const metric: PerformanceMetric = {
      operation,
      duration,
      timestamp: new Date(),
      cacheHit: metadata?.cacheHit || false,
      optimized: metadata?.optimized || false,
      userId: metadata?.userId,
      metadata,
    };

    this.addMetric(metric);

    // Log performance insights
    const emoji = this.getPerformanceEmoji(duration, metadata?.optimized);
    console.log(`${emoji} ${operation}: ${duration}ms${metadata?.cacheHit ? ' (cached)' : ''}${metadata?.optimized ? ' (optimized)' : ''}`);

    // Alert for slow operations
    if (duration > this.slowOperationThreshold) {
      console.warn(`🐌 SLOW OPERATION: ${operation} took ${duration}ms`);
    }

    return metric;
  }

  // Track React Query cache performance
  trackCacheOperation(operation: string, hit: boolean, queryKey: string[]) {
    this.trackApiCall(`cache.${operation}`, Date.now(), {
      cacheHit: hit,
      queryKey: queryKey.join('.'),
      optimized: true,
    });
  }

  // Track real-time event processing
  trackRealtimeEvent(eventType: string, processingTime: number) {
    this.trackApiCall(`realtime.${eventType}`, Date.now() - processingTime, {
      optimized: true,
    });
  }

  // Track user interaction performance
  trackUserInteraction(action: string, startTime: number) {
    const duration = Date.now() - startTime;
    this.trackApiCall(`user.${action}`, startTime, {
      optimized: duration < 100, // Sub-100ms is considered optimized
    });
  }

  // Get comprehensive performance statistics
  getPerformanceStats(): PerformanceStats {
    const now = Date.now();
    const recentMetrics = this.metrics.filter(
      m => now - m.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );

    const totalOperations = recentMetrics.length;
    const cacheHits = recentMetrics.filter(m => m.cacheHit).length;
    const optimizedOperations = recentMetrics.filter(m => m.optimized).length;

    const durations = recentMetrics.map(m => m.duration);
    const averageResponseTime = durations.length > 0 
      ? durations.reduce((sum, d) => sum + d, 0) / durations.length 
      : 0;

    const sortedByDuration = [...recentMetrics].sort((a, b) => a.duration - b.duration);

    return {
      averageResponseTime: Math.round(averageResponseTime),
      cacheHitRate: totalOperations > 0 ? Math.round((cacheHits / totalOperations) * 100) : 0,
      totalOperations,
      optimizedOperations,
      slowestOperations: sortedByDuration.slice(-5).reverse(), // 5 slowest
      fastestOperations: sortedByDuration.slice(0, 5), // 5 fastest
    };
  }

  // Get performance insights and recommendations
  getPerformanceInsights(): string[] {
    const stats = this.getPerformanceStats();
    const insights: string[] = [];

    if (stats.averageResponseTime > 500) {
      insights.push('🐌 Average response time is high. Consider implementing more caching.');
    } else if (stats.averageResponseTime < 200) {
      insights.push('⚡ Excellent response times! Your optimizations are working.');
    }

    if (stats.cacheHitRate > 80) {
      insights.push('🎯 Great cache hit rate! Data is being reused efficiently.');
    } else if (stats.cacheHitRate < 50) {
      insights.push('📊 Low cache hit rate. Review staleTime and cache strategies.');
    }

    const optimizedRate = stats.totalOperations > 0 
      ? (stats.optimizedOperations / stats.totalOperations) * 100 
      : 0;

    if (optimizedRate > 80) {
      insights.push('🚀 Most operations are optimized! Excellent performance.');
    } else if (optimizedRate < 50) {
      insights.push('⚡ Many operations could benefit from optimization.');
    }

    if (stats.totalOperations > 100) {
      insights.push(`📈 High activity: ${stats.totalOperations} operations in last 5 minutes.`);
    }

    return insights;
  }

  // Export performance data for analysis
  exportPerformanceData() {
    return {
      metrics: this.metrics,
      stats: this.getPerformanceStats(),
      insights: this.getPerformanceInsights(),
      timestamp: new Date(),
    };
  }

  // Clear old metrics to prevent memory leaks
  clearOldMetrics() {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics = this.metrics.filter(
      m => m.timestamp.getTime() > oneHourAgo
    );
  }

  // Private methods
  private addMetric(metric: PerformanceMetric) {
    this.metrics.push(metric);
    
    // Keep only recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }
  }

  private getPerformanceEmoji(duration: number, optimized?: boolean): string {
    if (optimized) return '🚀';
    if (duration < 100) return '⚡';
    if (duration < 300) return '🔥';
    if (duration < 500) return '🟡';
    if (duration < 1000) return '🟠';
    return '🔴';
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for easy tracking
export const trackApiPerformance = (operation: string, startTime: number, metadata?: Record<string, any>) => {
  return performanceMonitor.trackApiCall(operation, startTime, metadata);
};

export const trackCachePerformance = (operation: string, hit: boolean, queryKey: string[]) => {
  return performanceMonitor.trackCacheOperation(operation, hit, queryKey);
};

export const trackUserInteraction = (action: string, startTime: number) => {
  return performanceMonitor.trackUserInteraction(action, startTime);
};

export const getPerformanceReport = () => {
  const stats = performanceMonitor.getPerformanceStats();
  const insights = performanceMonitor.getPerformanceInsights();
  
  console.group('📊 Performance Report');
  console.log(`⚡ Average Response Time: ${stats.averageResponseTime}ms`);
  console.log(`🎯 Cache Hit Rate: ${stats.cacheHitRate}%`);
  console.log(`🔄 Total Operations: ${stats.totalOperations}`);
  console.log(`🚀 Optimized Operations: ${stats.optimizedOperations}`);
  console.log('💡 Insights:');
  insights.forEach(insight => console.log(`  ${insight}`));
  console.groupEnd();
  
  return { stats, insights };
};

// Auto-cleanup every 30 minutes
setInterval(() => {
  performanceMonitor.clearOldMetrics();
}, 30 * 60 * 1000);

// Performance monitoring hook for React components
export const usePerformanceMonitoring = () => {
  return {
    trackStart: (operation: string) => Date.now(),
    trackEnd: (operation: string, startTime: number, metadata?: Record<string, any>) => 
      trackApiPerformance(operation, startTime, metadata),
    getStats: () => performanceMonitor.getPerformanceStats(),
    getInsights: () => performanceMonitor.getPerformanceInsights(),
    getReport: getPerformanceReport,
  };
};