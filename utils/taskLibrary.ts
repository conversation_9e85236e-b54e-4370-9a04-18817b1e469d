import { TASK_CATEGORIES, AGE_GROUPS } from './openaiConfig';

// Task template interface
export interface TaskTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  age_group: string;
  difficulty: 'easy' | 'medium' | 'hard';
  estimated_time: number; // in minutes
  coin_value: number;
  instructions: string[];
  tips?: string[];
  safety_notes?: string[];
  tags?: string[];
}

// Comprehensive task library organized by age group
export const TASK_LIBRARY: { [key: string]: TaskTemplate[] } = {
  
  // TODDLER (3-5 years)
  TODDLER: [
    // Household Tasks
    {
      id: 'toddler_001',
      title: 'Put Toys Away',
      description: 'Help clean up toys and put them in their special place',
      category: 'HOUSEHOLD',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 10,
      coin_value: 5,
      instructions: [
        'Pick up one toy at a time',
        'Put each toy in its home (toy box or shelf)',
        'Ask grown-up for help if needed'
      ],
      tips: ['Make it fun with a cleanup song', 'Count toys together as you put them away'],
      tags: ['cleanup', 'organization', 'independence']
    },
    {
      id: 'toddler_002',
      title: 'Feed Pet Helper',
      description: 'Help feed the family pet with grown-up supervision',
      category: 'RESPONSIBILITY',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 5,
      coin_value: 3,
      instructions: [
        'Hold the pet food scoop with grown-up',
        'Pour food into pet bowl together',
        'Watch pet eat and be happy'
      ],
      tips: ['Always have adult supervision', 'Talk about why pets need food'],
      safety_notes: ['Adult must supervise all pet interactions'],
      tags: ['pets', 'responsibility', 'caring']
    },
    {
      id: 'toddler_003',
      title: 'Match Socks',
      description: 'Find matching socks and put them together',
      category: 'HOUSEHOLD',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 15,
      coin_value: 8,
      instructions: [
        'Look at sock colors and patterns',
        'Find the sock that looks the same',
        'Put matching socks together'
      ],
      tips: ['Start with just a few pairs', 'Celebrate each match found'],
      tags: ['matching', 'laundry', 'colors']
    },
    
    // Personal Care Tasks
    {
      id: 'toddler_004',
      title: 'Brush Teeth Time',
      description: 'Brush teeth with grown-up help for 2 minutes',
      category: 'PERSONAL',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 5,
      coin_value: 4,
      instructions: [
        'Put toothpaste on brush with grown-up',
        'Brush teeth while grown-up counts to 120',
        'Rinse mouth with water'
      ],
      tips: ['Use a fun toothbrush', 'Sing a brushing song'],
      tags: ['hygiene', 'health', 'routine']
    },
    {
      id: 'toddler_005',
      title: 'Pick Out Clothes',
      description: 'Choose clothes for the day from two options',
      category: 'PERSONAL',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 5,
      coin_value: 3,
      instructions: [
        'Look at two outfit choices',
        'Point to the one you like',
        'Tell grown-up why you chose it'
      ],
      tips: ['Give simple either/or choices', 'Talk about weather-appropriate clothing'],
      tags: ['independence', 'decision-making', 'clothing']
    },
    
    // Creative Tasks
    {
      id: 'toddler_006',
      title: 'Color a Picture',
      description: 'Color a simple picture with crayons',
      category: 'CREATIVE',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 20,
      coin_value: 6,
      instructions: [
        'Choose your favorite colors',
        'Stay inside the lines as best you can',
        'Show grown-up your beautiful picture'
      ],
      tips: ['Use thick crayons for easier grip', 'Praise effort over perfection'],
      tags: ['art', 'creativity', 'fine motor']
    },
    
    // Kindness Tasks
    {
      id: 'toddler_007',
      title: 'Give Hugs',
      description: 'Give three family members a hug',
      category: 'KINDNESS',
      age_group: 'TODDLER',
      difficulty: 'easy',
      estimated_time: 5,
      coin_value: 5,
      instructions: [
        'Find a family member',
        'Ask for a hug nicely',
        'Give a big, warm hug'
      ],
      tips: ['Model asking for consent', 'Talk about how hugs make people feel good'],
      tags: ['affection', 'family', 'kindness']
    },
  ],
  
  // PRESCHOOL (6-8 years)
  PRESCHOOL: [
    // Household Tasks
    {
      id: 'preschool_001',
      title: 'Set the Table',
      description: 'Put plates, cups, and napkins on the table for dinner',
      category: 'HOUSEHOLD',
      age_group: 'PRESCHOOL',
      difficulty: 'easy',
      estimated_time: 10,
      coin_value: 8,
      instructions: [
        'Count how many people are eating',
        'Put one plate at each spot',
        'Add cups and napkins for everyone'
      ],
      tips: ['Use plastic dishes for safety', 'Let them count and match'],
      tags: ['table setting', 'counting', 'responsibility']
    },
    {
      id: 'preschool_002',
      title: 'Sort Laundry',
      description: 'Sort clothes by color: lights, darks, and whites',
      category: 'HOUSEHOLD',
      age_group: 'PRESCHOOL',
      difficulty: 'medium',
      estimated_time: 15,
      coin_value: 10,
      instructions: [
        'Make three piles: light, dark, and white',
        'Look at each piece of clothing',
        'Put it in the right color pile'
      ],
      tips: ['Start with obvious colors', 'Explain why we sort laundry'],
      tags: ['laundry', 'sorting', 'colors']
    },
    {
      id: 'preschool_003',
      title: 'Water Plants',
      description: 'Help water the household plants with a small watering can',
      category: 'RESPONSIBILITY',
      age_group: 'PRESCHOOL',
      difficulty: 'easy',
      estimated_time: 10,
      coin_value: 7,
      instructions: [
        'Fill small watering can with water',
        'Pour a little water on soil, not leaves',
        'Check if soil feels dry first'
      ],
      tips: ['Use a child-sized watering can', 'Teach about plant care'],
      safety_notes: ['Supervise around water'],
      tags: ['plants', 'nature', 'responsibility']
    },
    
    // Personal Care Tasks
    {
      id: 'preschool_004',
      title: 'Make Bed',
      description: 'Pull covers up and arrange pillow nicely',
      category: 'PERSONAL',
      age_group: 'PRESCHOOL',
      difficulty: 'medium',
      estimated_time: 10,
      coin_value: 12,
      instructions: [
        'Pull blanket up to top of bed',
        'Smooth out any big wrinkles',
        'Put pillow at the head of bed'
      ],
      tips: ['Focus on effort, not perfection', 'Make it a morning routine'],
      tags: ['bedroom', 'routine', 'independence']
    },
    {
      id: 'preschool_005',
      title: 'Pack Backpack',
      description: 'Pack school backpack with needed items',
      category: 'RESPONSIBILITY',
      age_group: 'PRESCHOOL',
      difficulty: 'medium',
      estimated_time: 10,
      coin_value: 10,
      instructions: [
        'Check list of needed items',
        'Put each item in backpack',
        'Zip up backpack when done'
      ],
      tips: ['Use a visual checklist', 'Make it part of bedtime routine'],
      tags: ['school', 'organization', 'planning']
    },
    
    // Academic Tasks
    {
      id: 'preschool_006',
      title: 'Read 3 Books',
      description: 'Read three picture books (or have them read to you)',
      category: 'ACADEMIC',
      age_group: 'PRESCHOOL',
      difficulty: 'medium',
      estimated_time: 30,
      coin_value: 15,
      instructions: [
        'Choose three books you like',
        'Read or listen to each story',
        'Talk about what happened in the story'
      ],
      tips: ['Mix reading levels', 'Ask questions about the story'],
      tags: ['reading', 'literacy', 'learning']
    },
    
    // Physical Tasks
    {
      id: 'preschool_007',
      title: 'Dance Party',
      description: 'Dance to 4 songs in a row',
      category: 'PHYSICAL',
      age_group: 'PRESCHOOL',
      difficulty: 'easy',
      estimated_time: 15,
      coin_value: 8,
      instructions: [
        'Choose 4 fun songs',
        'Dance and move to each song',
        'Try different dance moves'
      ],
      tips: ['Let them pick music', 'Dance together for fun'],
      tags: ['exercise', 'music', 'movement']
    },
    
    // Kindness Tasks
    {
      id: 'preschool_008',
      title: 'Help a Friend',
      description: 'Do something nice for a sibling or friend',
      category: 'KINDNESS',
      age_group: 'PRESCHOOL',
      difficulty: 'medium',
      estimated_time: 15,
      coin_value: 12,
      instructions: [
        'Think of someone who might need help',
        'Ask how you can help them',
        'Do something kind without being asked'
      ],
      tips: ['Give examples of helpful actions', 'Praise specific kindness'],
      tags: ['helping', 'empathy', 'relationships']
    },
  ],
  
  // ELEMENTARY (9-12 years)
  ELEMENTARY: [
    // Household Tasks
    {
      id: 'elementary_001',
      title: 'Load Dishwasher',
      description: 'Load dirty dishes into dishwasher properly',
      category: 'HOUSEHOLD',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 15,
      coin_value: 15,
      instructions: [
        'Scrape food off plates into trash',
        'Place dishes in dishwasher without overcrowding',
        'Add detergent and start the cycle'
      ],
      tips: ['Show proper loading technique', 'Emphasize water and energy savings'],
      tags: ['dishes', 'kitchen', 'responsibility']
    },
    {
      id: 'elementary_002',
      title: 'Vacuum Living Room',
      description: 'Vacuum the living room carpet and furniture',
      category: 'HOUSEHOLD',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 20,
      coin_value: 18,
      instructions: [
        'Pick up toys and items from floor',
        'Vacuum in overlapping strokes',
        'Move light furniture to vacuum underneath'
      ],
      tips: ['Show proper vacuum technique', 'Ensure vacuum is appropriate size'],
      safety_notes: ['Check cord placement to avoid tripping'],
      tags: ['cleaning', 'floors', 'thoroughness']
    },
    {
      id: 'elementary_003',
      title: 'Take Out Trash',
      description: 'Empty all trash cans and take bins to curb',
      category: 'HOUSEHOLD',
      age_group: 'ELEMENTARY',
      difficulty: 'easy',
      estimated_time: 15,
      coin_value: 12,
      instructions: [
        'Empty all indoor trash cans',
        'Tie up full bags and take outside',
        'Put bins at curb for pickup'
      ],
      tips: ['Create a weekly schedule', 'Explain recycling vs trash'],
      tags: ['trash', 'routine', 'environmental']
    },
    
    // Personal Care Tasks
    {
      id: 'elementary_004',
      title: 'Organize Bedroom',
      description: 'Clean and organize entire bedroom',
      category: 'PERSONAL',
      age_group: 'ELEMENTARY',
      difficulty: 'hard',
      estimated_time: 45,
      coin_value: 25,
      instructions: [
        'Put all clothes in dresser or closet',
        'Organize books and school supplies',
        'Make bed and dust surfaces'
      ],
      tips: ['Break into smaller tasks', 'Create designated homes for items'],
      tags: ['organization', 'cleaning', 'independence']
    },
    {
      id: 'elementary_005',
      title: 'Plan Weekly Outfits',
      description: 'Choose and lay out clothes for the entire week',
      category: 'PERSONAL',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 30,
      coin_value: 20,
      instructions: [
        'Check weather forecast for the week',
        'Choose appropriate outfits for each day',
        'Lay out clothes in order'
      ],
      tips: ['Consider weekly activities', 'Teach weather-appropriate dressing'],
      tags: ['planning', 'clothing', 'independence']
    },
    
    // Academic Tasks
    {
      id: 'elementary_006',
      title: 'Research Project',
      description: 'Research a topic and create a short presentation',
      category: 'ACADEMIC',
      age_group: 'ELEMENTARY',
      difficulty: 'hard',
      estimated_time: 60,
      coin_value: 30,
      instructions: [
        'Choose an interesting topic',
        'Find 3-5 reliable sources',
        'Create presentation with key facts'
      ],
      tips: ['Guide source evaluation', 'Encourage creativity in presentation'],
      tags: ['research', 'presentation', 'learning']
    },
    
    // Life Skills Tasks
    {
      id: 'elementary_007',
      title: 'Prepare Simple Meal',
      description: 'Make a simple meal like sandwiches or salad',
      category: 'LIFE_SKILLS',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 30,
      coin_value: 22,
      instructions: [
        'Plan what you want to make',
        'Gather all ingredients',
        'Follow recipe or instructions carefully'
      ],
      tips: ['Start with no-cook meals', 'Emphasize kitchen safety'],
      safety_notes: ['Adult supervision for sharp tools'],
      tags: ['cooking', 'nutrition', 'independence']
    },
    
    // Social Tasks
    {
      id: 'elementary_008',
      title: 'Organize Playdate',
      description: 'Plan and organize a playdate with friends',
      category: 'SOCIAL',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 30,
      coin_value: 18,
      instructions: [
        'Choose friends to invite',
        'Plan activities and snacks',
        'Make sure everyone has fun'
      ],
      tips: ['Help with planning but let them lead', 'Discuss inclusion and kindness'],
      tags: ['friendship', 'planning', 'social skills']
    },
    
    // Kindness Tasks
    {
      id: 'elementary_009',
      title: 'Community Service',
      description: 'Do something helpful for the community',
      category: 'KINDNESS',
      age_group: 'ELEMENTARY',
      difficulty: 'medium',
      estimated_time: 45,
      coin_value: 25,
      instructions: [
        'Choose a community need',
        'Plan how to help',
        'Follow through with the action'
      ],
      tips: ['Suggest options like litter pickup or card making', 'Discuss impact of kindness'],
      tags: ['community', 'service', 'empathy']
    },
  ],
  
  // MIDDLE_SCHOOL (13-15 years)
  MIDDLE_SCHOOL: [
    // Household Tasks
    {
      id: 'middle_001',
      title: 'Deep Clean Bathroom',
      description: 'Thoroughly clean bathroom including toilet, shower, and sink',
      category: 'HOUSEHOLD',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'hard',
      estimated_time: 45,
      coin_value: 35,
      instructions: [
        'Gather cleaning supplies',
        'Clean toilet, sink, and shower/tub',
        'Mop floor and clean mirrors'
      ],
      tips: ['Provide proper cleaning supplies', 'Show technique for best results'],
      safety_notes: ['Ensure good ventilation', 'Never mix cleaning chemicals'],
      tags: ['cleaning', 'bathroom', 'responsibility']
    },
    {
      id: 'middle_002',
      title: 'Meal Planning',
      description: 'Plan meals for the week including grocery list',
      category: 'LIFE_SKILLS',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'hard',
      estimated_time: 60,
      coin_value: 40,
      instructions: [
        'Plan 7 dinners considering family preferences',
        'Create detailed grocery list',
        'Consider nutrition and budget'
      ],
      tips: ['Use online resources for meal ideas', 'Involve family in preferences'],
      tags: ['planning', 'nutrition', 'budgeting']
    },
    {
      id: 'middle_003',
      title: 'Yard Work',
      description: 'Complete outdoor chores like mowing, weeding, or raking',
      category: 'HOUSEHOLD',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'medium',
      estimated_time: 90,
      coin_value: 45,
      instructions: [
        'Assess what yard work needs doing',
        'Use appropriate tools safely',
        'Complete task thoroughly'
      ],
      tips: ['Provide safety equipment', 'Show proper tool use'],
      safety_notes: ['Wear appropriate clothing and eye protection'],
      tags: ['outdoors', 'physical', 'maintenance']
    },
    
    // Academic Tasks
    {
      id: 'middle_004',
      title: 'Study Skills Workshop',
      description: 'Create and present a study method to family',
      category: 'ACADEMIC',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'hard',
      estimated_time: 90,
      coin_value: 50,
      instructions: [
        'Research effective study techniques',
        'Try methods with your own schoolwork',
        'Teach the method to family members'
      ],
      tips: ['Encourage experimentation with different methods', 'Connect to their actual schoolwork'],
      tags: ['study skills', 'teaching', 'learning']
    },
    
    // Life Skills Tasks
    {
      id: 'middle_005',
      title: 'Budget Management',
      description: 'Create and manage a personal budget for a month',
      category: 'LIFE_SKILLS',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'hard',
      estimated_time: 120,
      coin_value: 60,
      instructions: [
        'Track income from allowance and tasks',
        'List expenses and savings goals',
        'Monitor spending throughout the month'
      ],
      tips: ['Use budgeting apps or spreadsheets', 'Set realistic goals'],
      tags: ['money management', 'responsibility', 'math']
    },
    
    // Social Tasks
    {
      id: 'middle_006',
      title: 'Conflict Resolution',
      description: 'Help resolve a family disagreement peacefully',
      category: 'SOCIAL',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'hard',
      estimated_time: 45,
      coin_value: 35,
      instructions: [
        'Listen to all sides of the disagreement',
        'Suggest fair solutions',
        'Help family members communicate better'
      ],
      tips: ['Model good listening skills', 'Praise mature problem-solving'],
      tags: ['communication', 'empathy', 'problem-solving']
    },
    
    // Responsibility Tasks
    {
      id: 'middle_007',
      title: 'Babysitting Helper',
      description: 'Help care for younger siblings for an afternoon',
      category: 'RESPONSIBILITY',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'medium',
      estimated_time: 180,
      coin_value: 55,
      instructions: [
        'Plan activities for younger siblings',
        'Prepare snacks and handle basic needs',
        'Keep siblings safe and entertained'
      ],
      tips: ['Always have adult backup available', 'Prepare emergency contact info'],
      safety_notes: ['Adult must be available for emergencies'],
      tags: ['childcare', 'responsibility', 'leadership']
    },
    
    // Creative Tasks
    {
      id: 'middle_008',
      title: 'Family Newsletter',
      description: 'Create a monthly family newsletter with photos and stories',
      category: 'CREATIVE',
      age_group: 'MIDDLE_SCHOOL',
      difficulty: 'medium',
      estimated_time: 120,
      coin_value: 45,
      instructions: [
        'Interview family members about their month',
        'Include photos and stories',
        'Design and distribute newsletter'
      ],
      tips: ['Use digital tools for design', 'Include everyone in the family'],
      tags: ['writing', 'design', 'family bonding']
    },
  ],
  
  // HIGH_SCHOOL (16-18 years)
  HIGH_SCHOOL: [
    // Life Skills Tasks
    {
      id: 'high_001',
      title: 'File Tax Return',
      description: 'Complete tax return with adult guidance',
      category: 'LIFE_SKILLS',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'hard',
      estimated_time: 180,
      coin_value: 80,
      instructions: [
        'Gather all tax documents',
        'Use tax software or forms',
        'Review and submit return'
      ],
      tips: ['Start with simple returns', 'Explain tax concepts'],
      tags: ['taxes', 'financial literacy', 'adult skills']
    },
    {
      id: 'high_002',
      title: 'Car Maintenance',
      description: 'Learn basic car maintenance tasks',
      category: 'LIFE_SKILLS',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'medium',
      estimated_time: 90,
      coin_value: 50,
      instructions: [
        'Check oil, tire pressure, and fluid levels',
        'Learn to change a tire',
        'Understand basic car care schedule'
      ],
      tips: ['Supervise all activities', 'Explain importance of maintenance'],
      safety_notes: ['Adult supervision required for all car work'],
      tags: ['automotive', 'responsibility', 'independence']
    },
    {
      id: 'high_003',
      title: 'College Research',
      description: 'Research colleges and create comparison chart',
      category: 'ACADEMIC',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'hard',
      estimated_time: 240,
      coin_value: 100,
      instructions: [
        'Research 10 colleges of interest',
        'Compare costs, programs, and requirements',
        'Create detailed comparison chart'
      ],
      tips: ['Use multiple sources', 'Consider various factors beyond rankings'],
      tags: ['college planning', 'research', 'future planning']
    },
    
    // Responsibility Tasks
    {
      id: 'high_004',
      title: 'Volunteer Coordinator',
      description: 'Organize family volunteer activity',
      category: 'RESPONSIBILITY',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'hard',
      estimated_time: 180,
      coin_value: 75,
      instructions: [
        'Research volunteer opportunities',
        'Coordinate schedules with family',
        'Lead the volunteer activity'
      ],
      tips: ['Choose meaningful activities', 'Reflect on impact together'],
      tags: ['leadership', 'community service', 'organization']
    },
    
    // Academic Tasks
    {
      id: 'high_005',
      title: 'Tutor Younger Student',
      description: 'Tutor a younger sibling or neighbor in a subject',
      category: 'ACADEMIC',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'medium',
      estimated_time: 120,
      coin_value: 60,
      instructions: [
        'Assess student\'s needs and skill level',
        'Create lesson plan and materials',
        'Conduct tutoring sessions with patience'
      ],
      tips: ['Focus on encouragement', 'Adapt teaching style to student'],
      tags: ['teaching', 'patience', 'academic skills']
    },
    
    // Financial Tasks
    {
      id: 'high_006',
      title: 'Investment Research',
      description: 'Research and present investment options',
      category: 'LIFE_SKILLS',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'hard',
      estimated_time: 150,
      coin_value: 85,
      instructions: [
        'Research different investment types',
        'Analyze risk and return profiles',
        'Present findings to family'
      ],
      tips: ['Use reputable financial sources', 'Focus on long-term thinking'],
      tags: ['investing', 'financial planning', 'research']
    },
    
    // Social Tasks
    {
      id: 'high_007',
      title: 'Mentor Program',
      description: 'Mentor younger students in school or community',
      category: 'SOCIAL',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'medium',
      estimated_time: 240,
      coin_value: 90,
      instructions: [
        'Apply to mentor program',
        'Attend training sessions',
        'Provide ongoing support to mentee'
      ],
      tips: ['Choose program that matches interests', 'Be consistent and reliable'],
      tags: ['mentoring', 'leadership', 'community']
    },
    
    // Creative Tasks
    {
      id: 'high_008',
      title: 'Documentary Project',
      description: 'Create documentary about family history or local issue',
      category: 'CREATIVE',
      age_group: 'HIGH_SCHOOL',
      difficulty: 'hard',
      estimated_time: 300,
      coin_value: 120,
      instructions: [
        'Choose compelling topic',
        'Conduct interviews and research',
        'Edit and produce final documentary'
      ],
      tips: ['Use available technology', 'Focus on storytelling'],
      tags: ['filmmaking', 'storytelling', 'research']
    },
  ],
  
  // YOUNG_ADULT (19-25 years)
  YOUNG_ADULT: [
    // Life Skills Tasks
    {
      id: 'young_001',
      title: 'Apartment Hunting',
      description: 'Research and visit potential apartments',
      category: 'LIFE_SKILLS',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 360,
      coin_value: 150,
      instructions: [
        'Research neighborhoods and rental prices',
        'Schedule and attend apartment viewings',
        'Compare options and make decision'
      ],
      tips: ['Consider commute, safety, and amenities', 'Read lease terms carefully'],
      tags: ['housing', 'independence', 'decision-making']
    },
    {
      id: 'young_002',
      title: 'Professional Network',
      description: 'Build professional network through events and online',
      category: 'LIFE_SKILLS',
      age_group: 'YOUNG_ADULT',
      difficulty: 'medium',
      estimated_time: 180,
      coin_value: 100,
      instructions: [
        'Attend professional networking events',
        'Update LinkedIn profile',
        'Follow up with new connections'
      ],
      tips: ['Be genuine in connections', 'Offer value to others'],
      tags: ['networking', 'career', 'professional development']
    },
    {
      id: 'young_003',
      title: 'Insurance Research',
      description: 'Research and compare insurance options',
      category: 'LIFE_SKILLS',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 240,
      coin_value: 120,
      instructions: [
        'Research health, auto, and renters insurance',
        'Compare coverage options and costs',
        'Make informed insurance decisions'
      ],
      tips: ['Understand deductibles and coverage limits', 'Read policy terms'],
      tags: ['insurance', 'financial planning', 'adult responsibilities']
    },
    
    // Career Tasks
    {
      id: 'young_004',
      title: 'Career Development Plan',
      description: 'Create 5-year career development plan',
      category: 'ACADEMIC',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 300,
      coin_value: 140,
      instructions: [
        'Assess current skills and interests',
        'Research career paths and requirements',
        'Create actionable development plan'
      ],
      tips: ['Include skill development and networking', 'Set measurable goals'],
      tags: ['career planning', 'professional development', 'goal setting']
    },
    
    // Financial Tasks
    {
      id: 'young_005',
      title: 'Investment Portfolio',
      description: 'Start investment portfolio and track performance',
      category: 'LIFE_SKILLS',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 200,
      coin_value: 110,
      instructions: [
        'Open investment account',
        'Research and select investments',
        'Monitor and adjust portfolio monthly'
      ],
      tips: ['Start with low-cost index funds', 'Focus on long-term growth'],
      tags: ['investing', 'financial planning', 'wealth building']
    },
    
    // Social Tasks
    {
      id: 'young_006',
      title: 'Community Leadership',
      description: 'Take leadership role in community organization',
      category: 'SOCIAL',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 480,
      coin_value: 180,
      instructions: [
        'Join community organization',
        'Volunteer for leadership position',
        'Lead projects and initiatives'
      ],
      tips: ['Choose causes you care about', 'Develop leadership skills'],
      tags: ['leadership', 'community involvement', 'civic engagement']
    },
    
    // Personal Development Tasks
    {
      id: 'young_007',
      title: 'Skill Certification',
      description: 'Earn professional certification in chosen field',
      category: 'ACADEMIC',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 600,
      coin_value: 200,
      instructions: [
        'Research relevant certifications',
        'Prepare for certification exam',
        'Take exam and earn certification'
      ],
      tips: ['Choose certifications valued by employers', 'Use study groups'],
      tags: ['certification', 'professional development', 'skills']
    },
    
    // Creative Tasks
    {
      id: 'young_008',
      title: 'Creative Side Business',
      description: 'Start small creative business or freelance work',
      category: 'CREATIVE',
      age_group: 'YOUNG_ADULT',
      difficulty: 'hard',
      estimated_time: 720,
      coin_value: 250,
      instructions: [
        'Identify creative skill to monetize',
        'Set up business structure and marketing',
        'Complete first paid projects'
      ],
      tips: ['Start small and grow gradually', 'Focus on quality and customer service'],
      tags: ['entrepreneurship', 'creativity', 'business skills']
    },
  ],
};

// Utility functions for task library
export const getTasksByAgeGroup = (ageGroup: string): TaskTemplate[] => {
  return TASK_LIBRARY[ageGroup] || [];
};

export const getTasksByCategory = (category: string): TaskTemplate[] => {
  const allTasks = Object.values(TASK_LIBRARY).flat();
  return allTasks.filter(task => task.category === category);
};

export const getTasksByAge = (age: number): TaskTemplate[] => {
  // Determine age group
  let ageGroup = 'ELEMENTARY';
  if (age >= 3 && age <= 5) ageGroup = 'TODDLER';
  else if (age >= 6 && age <= 8) ageGroup = 'PRESCHOOL';
  else if (age >= 9 && age <= 12) ageGroup = 'ELEMENTARY';
  else if (age >= 13 && age <= 15) ageGroup = 'MIDDLE_SCHOOL';
  else if (age >= 16 && age <= 18) ageGroup = 'HIGH_SCHOOL';
  else if (age >= 19 && age <= 25) ageGroup = 'YOUNG_ADULT';
  
  return getTasksByAgeGroup(ageGroup);
};

export const getTasksByDifficulty = (difficulty: 'easy' | 'medium' | 'hard'): TaskTemplate[] => {
  const allTasks = Object.values(TASK_LIBRARY).flat();
  return allTasks.filter(task => task.difficulty === difficulty);
};

export const getRandomTasks = (count: number, filters?: {
  ageGroup?: string;
  category?: string;
  difficulty?: 'easy' | 'medium' | 'hard';
}): TaskTemplate[] => {
  let tasks = Object.values(TASK_LIBRARY).flat();
  
  // Apply filters
  if (filters?.ageGroup) {
    tasks = tasks.filter(task => task.age_group === filters.ageGroup);
  }
  if (filters?.category) {
    tasks = tasks.filter(task => task.category === filters.category);
  }
  if (filters?.difficulty) {
    tasks = tasks.filter(task => task.difficulty === filters.difficulty);
  }
  
  // Shuffle and return requested count
  const shuffled = tasks.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const searchTasks = (query: string): TaskTemplate[] => {
  const allTasks = Object.values(TASK_LIBRARY).flat();
  const searchTerms = query.toLowerCase().split(' ');
  
  return allTasks.filter(task => {
    const searchText = [
      task.title,
      task.description,
      task.category,
      ...(task.tags || []),
      ...(task.instructions || [])
    ].join(' ').toLowerCase();
    
    return searchTerms.every(term => searchText.includes(term));
  });
};

export const getTaskStats = () => {
  const allTasks = Object.values(TASK_LIBRARY).flat();
  
  return {
    totalTasks: allTasks.length,
    byAgeGroup: Object.keys(TASK_LIBRARY).reduce((acc, ageGroup) => {
      acc[ageGroup] = TASK_LIBRARY[ageGroup].length;
      return acc;
    }, {} as { [key: string]: number }),
    byCategory: Object.keys(TASK_CATEGORIES).reduce((acc, category) => {
      acc[category] = allTasks.filter(task => task.category === category).length;
      return acc;
    }, {} as { [key: string]: number }),
    byDifficulty: {
      easy: allTasks.filter(task => task.difficulty === 'easy').length,
      medium: allTasks.filter(task => task.difficulty === 'medium').length,
      hard: allTasks.filter(task => task.difficulty === 'hard').length,
    }
  };
}; 