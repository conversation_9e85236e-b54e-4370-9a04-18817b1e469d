import { REWARD_CATEGORIES, AGE_GROUPS } from './openaiConfig';

// Reward template interface
export interface RewardTemplate {
  id: string;
  title: string;
  description: string;
  category: string;
  age_group: string;
  cost: number;
  availability: 'always' | 'weekends' | 'special_occasions';
  notes?: string;
  tags?: string[];
}

// Comprehensive reward library organized by age group
export const REWARD_LIBRARY: { [key: string]: RewardTemplate[] } = {
  
  // TODDLER (3-5 years)
  TODDLER: [
    // Treats
    {
      id: 'toddler_reward_001',
      title: 'Special Snack',
      description: 'Choose a favorite healthy snack',
      category: 'TREATS',
      age_group: 'TODDLER',
      cost: 5,
      availability: 'always',
      notes: 'Focus on healthy options like fruit or crackers',
      tags: ['food', 'healthy', 'choice']
    },
    {
      id: 'toddler_reward_002',
      title: 'Sticker Collection',
      description: 'Pick 3 special stickers for sticker book',
      category: 'TOYS',
      age_group: 'TODDLER',
      cost: 3,
      availability: 'always',
      notes: 'Great for positive reinforcement',
      tags: ['stickers', 'collection', 'achievement']
    },
    {
      id: 'toddler_reward_003',
      title: 'Extra Bedtime Story',
      description: 'Get an extra story before bed',
      category: 'PRIVILEGES',
      age_group: 'TODDLER',
      cost: 8,
      availability: 'always',
      notes: 'Promotes bonding and reading',
      tags: ['reading', 'bedtime', 'bonding']
    },
    
    // Activities
    {
      id: 'toddler_reward_004',
      title: 'Dance Party',
      description: '15-minute dance party with favorite music',
      category: 'ACTIVITIES',
      age_group: 'TODDLER',
      cost: 6,
      availability: 'always',
      notes: 'Great for physical activity and fun',
      tags: ['music', 'movement', 'energy']
    },
    {
      id: 'toddler_reward_005',
      title: 'Bubble Bath',
      description: 'Special bubble bath with toys',
      category: 'ACTIVITIES',
      age_group: 'TODDLER',
      cost: 10,
      availability: 'always',
      notes: 'Relaxing and fun activity',
      tags: ['bath', 'relaxation', 'play']
    },
    {
      id: 'toddler_reward_006',
      title: 'Park Visit',
      description: 'Special trip to the playground',
      category: 'OUTINGS',
      age_group: 'TODDLER',
      cost: 15,
      availability: 'weekends',
      notes: 'Weather permitting',
      tags: ['outdoor', 'playground', 'exercise']
    },
    
    // Screen Time
    {
      id: 'toddler_reward_007',
      title: 'Cartoon Time',
      description: 'Watch favorite cartoon for 20 minutes',
      category: 'SCREEN_TIME',
      age_group: 'TODDLER',
      cost: 12,
      availability: 'always',
      notes: 'Educational shows preferred',
      tags: ['screen time', 'entertainment', 'learning']
    },
    
    // Experiences
    {
      id: 'toddler_reward_008',
      title: 'Choose Tonight\'s Dinner',
      description: 'Pick what the family has for dinner',
      category: 'PRIVILEGES',
      age_group: 'TODDLER',
      cost: 18,
      availability: 'always',
      notes: 'With healthy options provided',
      tags: ['choice', 'family', 'meals']
    },
  ],
  
  // PRESCHOOL (6-8 years)
  PRESCHOOL: [
    // Treats
    {
      id: 'preschool_reward_001',
      title: 'Ice Cream Cone',
      description: 'Small ice cream cone with favorite flavor',
      category: 'TREATS',
      age_group: 'PRESCHOOL',
      cost: 12,
      availability: 'weekends',
      notes: 'Occasional treat, not daily',
      tags: ['dessert', 'special treat', 'cold']
    },
    {
      id: 'preschool_reward_002',
      title: 'Baking Together',
      description: 'Help make cookies or cupcakes',
      category: 'ACTIVITIES',
      age_group: 'PRESCHOOL',
      cost: 20,
      availability: 'weekends',
      notes: 'Great for learning and bonding',
      tags: ['baking', 'learning', 'family time']
    },
    
    // Toys
    {
      id: 'preschool_reward_003',
      title: 'Small Toy',
      description: 'Choose a small toy from the store',
      category: 'TOYS',
      age_group: 'PRESCHOOL',
      cost: 25,
      availability: 'special_occasions',
      notes: 'Under $5 value',
      tags: ['toy', 'shopping', 'choice']
    },
    {
      id: 'preschool_reward_004',
      title: 'Art Supplies',
      description: 'New crayons, markers, or stickers',
      category: 'TOYS',
      age_group: 'PRESCHOOL',
      cost: 15,
      availability: 'always',
      notes: 'Encourages creativity',
      tags: ['art', 'creativity', 'supplies']
    },
    
    // Activities
    {
      id: 'preschool_reward_005',
      title: 'Movie Night',
      description: 'Choose family movie with popcorn',
      category: 'ACTIVITIES',
      age_group: 'PRESCHOOL',
      cost: 22,
      availability: 'weekends',
      notes: 'Age-appropriate movies only',
      tags: ['movie', 'family time', 'entertainment']
    },
    {
      id: 'preschool_reward_006',
      title: 'Friend Playdate',
      description: 'Invite a friend over to play',
      category: 'ACTIVITIES',
      age_group: 'PRESCHOOL',
      cost: 30,
      availability: 'weekends',
      notes: 'Coordinate with other parents',
      tags: ['friends', 'social', 'play']
    },
    
    // Privileges
    {
      id: 'preschool_reward_007',
      title: 'Stay Up Late',
      description: 'Stay up 30 minutes past bedtime',
      category: 'PRIVILEGES',
      age_group: 'PRESCHOOL',
      cost: 18,
      availability: 'weekends',
      notes: 'Only on non-school nights',
      tags: ['bedtime', 'special privilege', 'late']
    },
    {
      id: 'preschool_reward_008',
      title: 'Special Outing',
      description: 'Visit zoo, museum, or special place',
      category: 'OUTINGS',
      age_group: 'PRESCHOOL',
      cost: 40,
      availability: 'special_occasions',
      notes: 'Plan ahead for special trips',
      tags: ['outing', 'learning', 'adventure']
    },
    
    // Screen Time
    {
      id: 'preschool_reward_009',
      title: 'Tablet Time',
      description: '45 minutes of educational games',
      category: 'SCREEN_TIME',
      age_group: 'PRESCHOOL',
      cost: 16,
      availability: 'always',
      notes: 'Educational apps preferred',
      tags: ['tablet', 'games', 'learning']
    },
    
    // Money
    {
      id: 'preschool_reward_010',
      title: 'Piggy Bank Money',
      description: '$2 for the piggy bank',
      category: 'MONEY',
      age_group: 'PRESCHOOL',
      cost: 20,
      availability: 'always',
      notes: 'Teaching savings habits',
      tags: ['money', 'savings', 'future']
    },
  ],
  
  // ELEMENTARY (9-12 years)
  ELEMENTARY: [
    // Treats
    {
      id: 'elementary_reward_001',
      title: 'Restaurant Choice',
      description: 'Pick where family goes for dinner',
      category: 'TREATS',
      age_group: 'ELEMENTARY',
      cost: 35,
      availability: 'weekends',
      notes: 'Within family budget',
      tags: ['restaurant', 'choice', 'family dinner']
    },
    {
      id: 'elementary_reward_002',
      title: 'Favorite Meal',
      description: 'Request favorite homemade meal',
      category: 'TREATS',
      age_group: 'ELEMENTARY',
      cost: 25,
      availability: 'always',
      notes: 'Home-cooked favorite dish',
      tags: ['cooking', 'favorite food', 'family']
    },
    
    // Activities
    {
      id: 'elementary_reward_003',
      title: 'Sleepover',
      description: 'Have a friend sleep over',
      category: 'ACTIVITIES',
      age_group: 'ELEMENTARY',
      cost: 45,
      availability: 'weekends',
      notes: 'Coordinate with other parents',
      tags: ['sleepover', 'friends', 'social']
    },
    {
      id: 'elementary_reward_004',
      title: 'Hobby Supplies',
      description: 'Supplies for favorite hobby or craft',
      category: 'TOYS',
      age_group: 'ELEMENTARY',
      cost: 30,
      availability: 'always',
      notes: 'Support their interests',
      tags: ['hobby', 'craft', 'interest']
    },
    
    // Privileges
    {
      id: 'elementary_reward_005',
      title: 'Skip One Chore',
      description: 'Skip one regular chore this week',
      category: 'PRIVILEGES',
      age_group: 'ELEMENTARY',
      cost: 20,
      availability: 'always',
      notes: 'Choose from regular chores',
      tags: ['chore', 'break', 'privilege']
    },
    {
      id: 'elementary_reward_006',
      title: 'Control TV Remote',
      description: 'Control TV for family time tonight',
      category: 'PRIVILEGES',
      age_group: 'ELEMENTARY',
      cost: 15,
      availability: 'always',
      notes: 'Family-friendly choices only',
      tags: ['TV', 'control', 'family time']
    },
    
    // Screen Time
    {
      id: 'elementary_reward_007',
      title: 'Video Game Time',
      description: 'Extra 2 hours of video games',
      category: 'SCREEN_TIME',
      age_group: 'ELEMENTARY',
      cost: 28,
      availability: 'weekends',
      notes: 'Age-appropriate games only',
      tags: ['video games', 'entertainment', 'extra time']
    },
    {
      id: 'elementary_reward_008',
      title: 'YouTube Time',
      description: '1 hour of supervised YouTube',
      category: 'SCREEN_TIME',
      age_group: 'ELEMENTARY',
      cost: 18,
      availability: 'always',
      notes: 'Parent-approved content only',
      tags: ['YouTube', 'videos', 'entertainment']
    },
    
    // Outings
    {
      id: 'elementary_reward_009',
      title: 'Movie Theater',
      description: 'Go to movies with family',
      category: 'OUTINGS',
      age_group: 'ELEMENTARY',
      cost: 50,
      availability: 'special_occasions',
      notes: 'Choose age-appropriate movie',
      tags: ['movie theater', 'family outing', 'entertainment']
    },
    {
      id: 'elementary_reward_010',
      title: 'Amusement Park',
      description: 'Day trip to amusement park',
      category: 'OUTINGS',
      age_group: 'ELEMENTARY',
      cost: 100,
      availability: 'special_occasions',
      notes: 'Major reward for big achievements',
      tags: ['amusement park', 'adventure', 'special trip']
    },
    
    // Money
    {
      id: 'elementary_reward_011',
      title: 'Cash Allowance',
      description: '$10 spending money',
      category: 'MONEY',
      age_group: 'ELEMENTARY',
      cost: 40,
      availability: 'always',
      notes: 'Teaching money management',
      tags: ['cash', 'spending', 'money management']
    },
    {
      id: 'elementary_reward_012',
      title: 'Savings Bonus',
      description: '$5 added to savings account',
      category: 'MONEY',
      age_group: 'ELEMENTARY',
      cost: 25,
      availability: 'always',
      notes: 'Building future wealth',
      tags: ['savings', 'bank account', 'future']
    },
  ],
  
  // MIDDLE_SCHOOL (13-15 years)
  MIDDLE_SCHOOL: [
    // Treats
    {
      id: 'middle_reward_001',
      title: 'Favorite Restaurant',
      description: 'Dinner at favorite restaurant',
      category: 'TREATS',
      age_group: 'MIDDLE_SCHOOL',
      cost: 50,
      availability: 'weekends',
      notes: 'Within reasonable budget',
      tags: ['restaurant', 'dining out', 'favorite food']
    },
    {
      id: 'middle_reward_002',
      title: 'Cooking Class',
      description: 'Take a cooking class or lesson',
      category: 'EXPERIENCES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 75,
      availability: 'special_occasions',
      notes: 'Learn new skills',
      tags: ['cooking', 'learning', 'life skills']
    },
    
    // Activities
    {
      id: 'middle_reward_003',
      title: 'Concert Tickets',
      description: 'Tickets to age-appropriate concert',
      category: 'EXPERIENCES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 120,
      availability: 'special_occasions',
      notes: 'With adult supervision',
      tags: ['music', 'concert', 'entertainment']
    },
    {
      id: 'middle_reward_004',
      title: 'Shopping Trip',
      description: 'Shopping trip with $50 budget',
      category: 'OUTINGS',
      age_group: 'MIDDLE_SCHOOL',
      cost: 60,
      availability: 'weekends',
      notes: 'For clothes or personal items',
      tags: ['shopping', 'clothes', 'personal choice']
    },
    
    // Privileges
    {
      id: 'middle_reward_005',
      title: 'Later Bedtime',
      description: 'Stay up 1 hour later on weekends',
      category: 'PRIVILEGES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 30,
      availability: 'weekends',
      notes: 'Weekend nights only',
      tags: ['bedtime', 'late night', 'freedom']
    },
    {
      id: 'middle_reward_006',
      title: 'Friend Outing',
      description: 'Go out with friends (supervised)',
      category: 'ACTIVITIES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 45,
      availability: 'weekends',
      notes: 'With parent approval and supervision',
      tags: ['friends', 'social', 'independence']
    },
    
    // Screen Time
    {
      id: 'middle_reward_007',
      title: 'Gaming Marathon',
      description: 'All-day gaming session',
      category: 'SCREEN_TIME',
      age_group: 'MIDDLE_SCHOOL',
      cost: 40,
      availability: 'weekends',
      notes: 'With breaks for meals and exercise',
      tags: ['gaming', 'entertainment', 'marathon']
    },
    {
      id: 'middle_reward_008',
      title: 'Phone Upgrade',
      description: 'New phone case or accessory',
      category: 'TOYS',
      age_group: 'MIDDLE_SCHOOL',
      cost: 55,
      availability: 'always',
      notes: 'Within reasonable cost',
      tags: ['phone', 'accessories', 'technology']
    },
    
    // Money
    {
      id: 'middle_reward_009',
      title: 'Shopping Money',
      description: '$25 spending money',
      category: 'MONEY',
      age_group: 'MIDDLE_SCHOOL',
      cost: 50,
      availability: 'always',
      notes: 'For personal purchases',
      tags: ['spending money', 'independence', 'choice']
    },
    {
      id: 'middle_reward_010',
      title: 'Savings Match',
      description: 'Parents match savings contribution',
      category: 'MONEY',
      age_group: 'MIDDLE_SCHOOL',
      cost: 60,
      availability: 'always',
      notes: 'Encouraging saving habits',
      tags: ['savings', 'matching', 'future planning']
    },
    
    // Experiences
    {
      id: 'middle_reward_011',
      title: 'Sports Event',
      description: 'Tickets to professional sports game',
      category: 'EXPERIENCES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 80,
      availability: 'special_occasions',
      notes: 'Family outing to sports event',
      tags: ['sports', 'live event', 'family time']
    },
    {
      id: 'middle_reward_012',
      title: 'Art Class',
      description: 'Enroll in art or music class',
      category: 'EXPERIENCES',
      age_group: 'MIDDLE_SCHOOL',
      cost: 90,
      availability: 'special_occasions',
      notes: 'Develop creative skills',
      tags: ['art', 'music', 'creativity', 'learning']
    },
  ],
  
  // HIGH_SCHOOL (16-18 years)
  HIGH_SCHOOL: [
    // Treats
    {
      id: 'high_reward_001',
      title: 'Dinner Date Fund',
      description: 'Money for dinner with friends',
      category: 'MONEY',
      age_group: 'HIGH_SCHOOL',
      cost: 60,
      availability: 'weekends',
      notes: 'For social dining experiences',
      tags: ['social', 'dining', 'independence']
    },
    {
      id: 'high_reward_002',
      title: 'Specialty Coffee',
      description: 'Gift card to favorite coffee shop',
      category: 'TREATS',
      age_group: 'HIGH_SCHOOL',
      cost: 35,
      availability: 'always',
      notes: 'For study sessions or social time',
      tags: ['coffee', 'study', 'social']
    },
    
    // Activities
    {
      id: 'high_reward_003',
      title: 'Concert VIP',
      description: 'VIP concert tickets',
      category: 'EXPERIENCES',
      age_group: 'HIGH_SCHOOL',
      cost: 200,
      availability: 'special_occasions',
      notes: 'Major reward for significant achievements',
      tags: ['music', 'VIP', 'special experience']
    },
    {
      id: 'high_reward_004',
      title: 'Driver\'s Ed',
      description: 'Driving lessons or driver\'s education',
      category: 'EXPERIENCES',
      age_group: 'HIGH_SCHOOL',
      cost: 150,
      availability: 'special_occasions',
      notes: 'Important life skill',
      tags: ['driving', 'independence', 'life skills']
    },
    
    // Privileges
    {
      id: 'high_reward_005',
      title: 'Car Privileges',
      description: 'Use family car for weekend',
      category: 'PRIVILEGES',
      age_group: 'HIGH_SCHOOL',
      cost: 80,
      availability: 'weekends',
      notes: 'With responsible driving record',
      tags: ['car', 'independence', 'transportation']
    },
    {
      id: 'high_reward_006',
      title: 'Extended Curfew',
      description: 'Later curfew for special events',
      category: 'PRIVILEGES',
      age_group: 'HIGH_SCHOOL',
      cost: 45,
      availability: 'special_occasions',
      notes: 'With advance notice and approval',
      tags: ['curfew', 'freedom', 'trust']
    },
    
    // Technology
    {
      id: 'high_reward_007',
      title: 'Phone Upgrade',
      description: 'Contribution toward new phone',
      category: 'TOYS',
      age_group: 'HIGH_SCHOOL',
      cost: 250,
      availability: 'special_occasions',
      notes: 'Major purchase reward',
      tags: ['phone', 'technology', 'communication']
    },
    {
      id: 'high_reward_008',
      title: 'Gaming Setup',
      description: 'Gaming equipment or accessories',
      category: 'TOYS',
      age_group: 'HIGH_SCHOOL',
      cost: 180,
      availability: 'special_occasions',
      notes: 'For gaming enthusiasts',
      tags: ['gaming', 'equipment', 'hobby']
    },
    
    // Money
    {
      id: 'high_reward_009',
      title: 'College Fund',
      description: '$100 toward college savings',
      category: 'MONEY',
      age_group: 'HIGH_SCHOOL',
      cost: 120,
      availability: 'always',
      notes: 'Investment in future education',
      tags: ['college', 'education', 'future']
    },
    {
      id: 'high_reward_010',
      title: 'Investment Account',
      description: 'Start investment account with $50',
      category: 'MONEY',
      age_group: 'HIGH_SCHOOL',
      cost: 75,
      availability: 'always',
      notes: 'Teaching investment principles',
      tags: ['investing', 'financial literacy', 'future']
    },
    
    // Experiences
    {
      id: 'high_reward_011',
      title: 'College Visit',
      description: 'Trip to visit college campus',
      category: 'EXPERIENCES',
      age_group: 'HIGH_SCHOOL',
      cost: 300,
      availability: 'special_occasions',
      notes: 'Educational and planning experience',
      tags: ['college', 'education', 'future planning']
    },
    {
      id: 'high_reward_012',
      title: 'Internship Support',
      description: 'Transportation/supplies for internship',
      category: 'EXPERIENCES',
      age_group: 'HIGH_SCHOOL',
      cost: 100,
      availability: 'always',
      notes: 'Supporting career development',
      tags: ['internship', 'career', 'professional development']
    },
  ],
  
  // YOUNG_ADULT (19-25 years)
  YOUNG_ADULT: [
    // Money
    {
      id: 'young_reward_001',
      title: 'Emergency Fund',
      description: '$200 toward emergency savings',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 200,
      availability: 'always',
      notes: 'Building financial security',
      tags: ['emergency fund', 'financial security', 'adult responsibility']
    },
    {
      id: 'young_reward_002',
      title: 'Investment Contribution',
      description: '$150 toward investment portfolio',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 150,
      availability: 'always',
      notes: 'Long-term wealth building',
      tags: ['investing', 'portfolio', 'wealth building']
    },
    
    // Experiences
    {
      id: 'young_reward_003',
      title: 'Professional Development',
      description: 'Conference or training course',
      category: 'EXPERIENCES',
      age_group: 'YOUNG_ADULT',
      cost: 300,
      availability: 'special_occasions',
      notes: 'Career advancement opportunity',
      tags: ['professional development', 'career', 'skills']
    },
    {
      id: 'young_reward_004',
      title: 'Travel Fund',
      description: '$250 toward travel savings',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 250,
      availability: 'special_occasions',
      notes: 'Supporting travel and experiences',
      tags: ['travel', 'experiences', 'adventure']
    },
    
    // Independence
    {
      id: 'young_reward_005',
      title: 'Apartment Deposit',
      description: 'Help with apartment security deposit',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 400,
      availability: 'special_occasions',
      notes: 'Major life milestone support',
      tags: ['apartment', 'independence', 'adult milestone']
    },
    {
      id: 'young_reward_006',
      title: 'Professional Wardrobe',
      description: 'Contribution toward work clothes',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 180,
      availability: 'always',
      notes: 'Career preparation support',
      tags: ['professional', 'career', 'wardrobe']
    },
    
    // Technology
    {
      id: 'young_reward_007',
      title: 'Professional Equipment',
      description: 'Work-related technology or equipment',
      category: 'TOYS',
      age_group: 'YOUNG_ADULT',
      cost: 350,
      availability: 'special_occasions',
      notes: 'Supporting career development',
      tags: ['technology', 'professional', 'career tools']
    },
    {
      id: 'young_reward_008',
      title: 'Subscription Services',
      description: 'Year of professional subscriptions',
      category: 'EXPERIENCES',
      age_group: 'YOUNG_ADULT',
      cost: 120,
      availability: 'always',
      notes: 'Professional development resources',
      tags: ['subscriptions', 'professional', 'resources']
    },
    
    // Health & Wellness
    {
      id: 'young_reward_009',
      title: 'Gym Membership',
      description: '6-month gym membership',
      category: 'EXPERIENCES',
      age_group: 'YOUNG_ADULT',
      cost: 280,
      availability: 'always',
      notes: 'Supporting health and wellness',
      tags: ['fitness', 'health', 'wellness']
    },
    {
      id: 'young_reward_010',
      title: 'Wellness Package',
      description: 'Spa day or wellness retreat',
      category: 'EXPERIENCES',
      age_group: 'YOUNG_ADULT',
      cost: 200,
      availability: 'special_occasions',
      notes: 'Self-care and stress management',
      tags: ['wellness', 'self-care', 'stress relief']
    },
    
    // Education
    {
      id: 'young_reward_011',
      title: 'Course Enrollment',
      description: 'Online course or certification',
      category: 'EXPERIENCES',
      age_group: 'YOUNG_ADULT',
      cost: 220,
      availability: 'always',
      notes: 'Continuing education support',
      tags: ['education', 'certification', 'skills development']
    },
    {
      id: 'young_reward_012',
      title: 'Book Budget',
      description: '$50 for professional books',
      category: 'MONEY',
      age_group: 'YOUNG_ADULT',
      cost: 60,
      availability: 'always',
      notes: 'Supporting continuous learning',
      tags: ['books', 'learning', 'professional development']
    },
  ],
};

// Utility functions for reward library
export const getRewardsByAgeGroup = (ageGroup: string): RewardTemplate[] => {
  return REWARD_LIBRARY[ageGroup] || [];
};

export const getRewardsByCategory = (category: string): RewardTemplate[] => {
  const allRewards = Object.values(REWARD_LIBRARY).flat();
  return allRewards.filter(reward => reward.category === category);
};

export const getRewardsByAge = (age: number): RewardTemplate[] => {
  // Determine age group
  let ageGroup = 'ELEMENTARY';
  if (age >= 3 && age <= 5) ageGroup = 'TODDLER';
  else if (age >= 6 && age <= 8) ageGroup = 'PRESCHOOL';
  else if (age >= 9 && age <= 12) ageGroup = 'ELEMENTARY';
  else if (age >= 13 && age <= 15) ageGroup = 'MIDDLE_SCHOOL';
  else if (age >= 16 && age <= 18) ageGroup = 'HIGH_SCHOOL';
  else if (age >= 19 && age <= 25) ageGroup = 'YOUNG_ADULT';
  
  return getRewardsByAgeGroup(ageGroup);
};

export const getRewardsByBudget = (minCost: number, maxCost: number): RewardTemplate[] => {
  const allRewards = Object.values(REWARD_LIBRARY).flat();
  return allRewards.filter(reward => reward.cost >= minCost && reward.cost <= maxCost);
};

export const getRewardsByAvailability = (availability: 'always' | 'weekends' | 'special_occasions'): RewardTemplate[] => {
  const allRewards = Object.values(REWARD_LIBRARY).flat();
  return allRewards.filter(reward => reward.availability === availability);
};

export const getRandomRewards = (count: number, filters?: {
  ageGroup?: string;
  category?: string;
  maxCost?: number;
  availability?: 'always' | 'weekends' | 'special_occasions';
}): RewardTemplate[] => {
  let rewards = Object.values(REWARD_LIBRARY).flat();
  
  // Apply filters
  if (filters?.ageGroup) {
    rewards = rewards.filter(reward => reward.age_group === filters.ageGroup);
  }
  if (filters?.category) {
    rewards = rewards.filter(reward => reward.category === filters.category);
  }
  if (filters?.maxCost) {
    rewards = rewards.filter(reward => reward.cost <= filters.maxCost);
  }
  if (filters?.availability) {
    rewards = rewards.filter(reward => reward.availability === filters.availability);
  }
  
  // Shuffle and return requested count
  const shuffled = rewards.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const searchRewards = (query: string): RewardTemplate[] => {
  const allRewards = Object.values(REWARD_LIBRARY).flat();
  const searchTerms = query.toLowerCase().split(' ');
  
  return allRewards.filter(reward => {
    const searchText = [
      reward.title,
      reward.description,
      reward.category,
      reward.notes || '',
      ...(reward.tags || [])
    ].join(' ').toLowerCase();
    
    return searchTerms.every(term => searchText.includes(term));
  });
};

export const getRewardStats = () => {
  const allRewards = Object.values(REWARD_LIBRARY).flat();
  
  return {
    totalRewards: allRewards.length,
    byAgeGroup: Object.keys(REWARD_LIBRARY).reduce((acc, ageGroup) => {
      acc[ageGroup] = REWARD_LIBRARY[ageGroup].length;
      return acc;
    }, {} as { [key: string]: number }),
    byCategory: Object.keys(REWARD_CATEGORIES).reduce((acc, category) => {
      acc[category] = allRewards.filter(reward => reward.category === category).length;
      return acc;
    }, {} as { [key: string]: number }),
    byAvailability: {
      always: allRewards.filter(reward => reward.availability === 'always').length,
      weekends: allRewards.filter(reward => reward.availability === 'weekends').length,
      special_occasions: allRewards.filter(reward => reward.availability === 'special_occasions').length,
    },
    costRange: {
      min: Math.min(...allRewards.map(reward => reward.cost)),
      max: Math.max(...allRewards.map(reward => reward.cost)),
      average: Math.round(allRewards.reduce((sum, reward) => sum + reward.cost, 0) / allRewards.length),
    }
  };
}; 