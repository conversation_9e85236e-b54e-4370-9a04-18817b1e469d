import { supabase } from './supabase';

export interface TaskValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface TaskTemplateData {
  title: string;
  description?: string;
  value: number;
  category?: string;
  frequency?: string;
  parent_id: string;
}

export interface TaskAssignmentData {
  child_id: string;
  parent_id: string;
  template_id: string;
  title: string;
  description?: string;
  value: number;
}

export interface TaskCompletionData {
  task_id: string;
  verification_code?: string;
  child_id: string;
  coin_value: number;
}

// Task Template Validation
export const validateTaskTemplate = (data: TaskTemplateData): TaskValidationResult => {
  const errors: string[] = [];

  // Required fields
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Task title is required');
  }

  if (data.title && data.title.trim().length > 100) {
    errors.push('Task title must be 100 characters or less');
  }

  if (!data.parent_id) {
    errors.push('Parent ID is required');
  }

  // Value validation
  if (data.value == null || data.value < 1 || data.value > 100) {
    errors.push('Coin value must be between 1 and 100');
  }

  if (data.value && !Number.isInteger(data.value)) {
    errors.push('Coin value must be a whole number');
  }

  // Optional field validation
  if (data.description && data.description.length > 500) {
    errors.push('Task description must be 500 characters or less');
  }

  if (data.category && data.category.length > 50) {
    errors.push('Category must be 50 characters or less');
  }

  if (data.frequency && !['Daily', 'Weekly', 'Monthly', 'One-time'].includes(data.frequency)) {
    errors.push('Frequency must be Daily, Weekly, Monthly, or One-time');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Task Assignment Validation
export const validateTaskAssignment = async (data: TaskAssignmentData): Promise<TaskValidationResult> => {
  const errors: string[] = [];

  // Required fields
  if (!data.child_id) {
    errors.push('Child ID is required');
  }

  if (!data.parent_id) {
    errors.push('Parent ID is required');
  }

  if (!data.template_id) {
    errors.push('Template ID is required');
  }

  if (!data.title || data.title.trim().length === 0) {
    errors.push('Task title is required');
  }

  // Value validation
  if (data.value == null || data.value < 1 || data.value > 100) {
    errors.push('Coin value must be between 1 and 100');
  }

  // Check if child exists and belongs to parent
  if (data.child_id && data.parent_id) {
    try {
      const { data: childData, error } = await supabase
        .from('children')
        .select('id')
        .eq('id', data.child_id)
        .eq('parent_id', data.parent_id)
        .single();

      if (error || !childData) {
        errors.push('Child does not exist or does not belong to this parent');
      }
    } catch (error) {
      errors.push('Error validating child relationship');
    }
  }

  // Check if template exists and belongs to parent
  if (data.template_id && data.parent_id) {
    try {
      const { data: templateData, error } = await supabase
        .from('task_templates')
        .select('id')
        .eq('id', data.template_id)
        .eq('parent_id', data.parent_id)
        .single();

      if (error || !templateData) {
        errors.push('Task template does not exist or does not belong to this parent');
      }
    } catch (error) {
      errors.push('Error validating template relationship');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Task Completion Validation
export const validateTaskCompletion = async (data: TaskCompletionData): Promise<TaskValidationResult> => {
  const errors: string[] = [];

  // Required fields
  if (!data.task_id) {
    errors.push('Task ID is required');
  }

  if (!data.child_id) {
    errors.push('Child ID is required');
  }

  if (data.coin_value == null || data.coin_value < 1 || data.coin_value > 100) {
    errors.push('Coin value must be between 1 and 100');
  }

  // Check if task exists and is in correct status
  if (data.task_id) {
    try {
      const { data: taskData, error } = await supabase
        .from('tasks')
        .select('id, status, child_id')
        .eq('id', data.task_id)
        .single();

      if (error || !taskData) {
        errors.push('Task does not exist');
      } else {
        if (taskData.status !== 'pending') {
          errors.push('Task must be in pending status to be completed');
        }

        if (taskData.child_id !== data.child_id) {
          errors.push('Task does not belong to this child');
        }
      }
    } catch (error) {
      errors.push('Error validating task status');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Verification Code Validation
export const validateVerificationCode = async (taskId: string, code: string): Promise<TaskValidationResult> => {
  const errors: string[] = [];

  if (!taskId) {
    errors.push('Task ID is required');
  }

  if (!code || code.length !== 4) {
    errors.push('Verification code must be 4 digits');
  }

  if (!/^\d{4}$/.test(code)) {
    errors.push('Verification code must contain only numbers');
  }

  // Check if verification code exists and is valid
  if (taskId && code) {
    try {
      const { data: codeData, error } = await supabase
        .from('task_completion_codes')
        .select('id, expires_at')
        .eq('task_id', taskId)
        .eq('code', code)
        .single();

      if (error || !codeData) {
        errors.push('Invalid or expired verification code');
      } else {
        const now = new Date();
        const expiresAt = new Date(codeData.expires_at);
        
        if (now > expiresAt) {
          errors.push('Verification code has expired');
        }
      }
    } catch (error) {
      errors.push('Error validating verification code');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Generate Verification Code
export const generateVerificationCode = async (taskId: string): Promise<string> => {
  const code = Math.floor(1000 + Math.random() * 9000).toString();
  
  // Delete any existing codes for this task
  await supabase
    .from('task_completion_codes')
    .delete()
    .eq('task_id', taskId);

  // Insert new code
  const { error } = await supabase
    .from('task_completion_codes')
    .insert({
      task_id: taskId,
      code: code,
      expires_at: new Date(Date.now() + 5 * 60 * 1000).toISOString() // 5 minutes
    });

  if (error) {
    throw new Error('Failed to generate verification code');
  }

  return code;
};

// Update Child XP and Level
export const updateChildProgress = async (childId: string, xpGained: number): Promise<void> => {
  const { data: childData, error: fetchError } = await supabase
    .from('children')
    .select('xp, level')
    .eq('id', childId)
    .single();

  if (fetchError || !childData) {
    throw new Error('Failed to fetch child data');
  }

  const newXp = childData.xp + xpGained;
  const newLevel = Math.floor(newXp / 100) + 1; // Level up every 100 XP

  const { error: updateError } = await supabase
    .from('children')
    .update({
      xp: newXp,
      level: newLevel
    })
    .eq('id', childId);

  if (updateError) {
    throw new Error('Failed to update child progress');
  }
};

// Database Schema Constants
export const TASK_STATUS_VALUES = ['pending', 'pending_approval', 'completed'] as const;
export const VERIFICATION_MODES = ['strict', 'trusting'] as const;
export const TASK_FREQUENCIES = ['Daily', 'Weekly', 'Monthly', 'One-time'] as const;
export const TASK_CATEGORIES = [
  'Cleaning', 'Hygiene', 'Bedroom', 'Kitchen', 'Education', 'Pet Care', 
  'Garden', 'Organization', 'Life Skills', 'Vehicle', 'Finance', 'Career', 
  'Health', 'Creative', 'Social', 'Technology'
] as const; 