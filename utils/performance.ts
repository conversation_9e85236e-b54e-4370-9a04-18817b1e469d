export const PerformanceMonitor = {
  track: (operation: string, startTime: number) => {
    const duration = Date.now() - startTime;
    
    // Log to console in development
    if (__DEV__) {
      console.log(`⚡ ${operation}: ${duration}ms`);
    }
    
    // Send to analytics in production
    if (!__DEV__) {
      // Analytics.track('performance', { operation, duration });
    }
    
    return duration;
  },
  
  markSlow: (operation: string, duration: number, threshold = 1000) => {
    if (duration > threshold) {
      console.warn(`⚠️ Slow operation: ${operation} took ${duration}ms`);
    }
  },
  
  trackApiCall: (endpoint: string, startTime: number) => {
    const duration = Date.now() - startTime;
    
    if (__DEV__) {
      console.log(`🌐 API ${endpoint}: ${duration}ms`);
    }
    
    if (duration > 2000) {
      console.warn(`🐌 Slow API call: ${endpoint} took ${duration}ms`);
    }
    
    return duration;
  },
  
  trackGraphQL: (query: string, startTime: number) => {
    const duration = Date.now() - startTime;
    
    if (__DEV__) {
      console.log(`📊 GraphQL ${query}: ${duration}ms`);
    }
    
    if (duration > 1000) {
      console.warn(`🐌 Slow GraphQL query: ${query} took ${duration}ms`);
    }
    
    return duration;
  }
}; 