import { supabase } from './supabase';

// === OPTIMIZED API LAYER ===
// Only complex business logic that requires server-side processing
// All simple CRUD operations use direct Supabase calls

// Helper function for Edge Functions (only for complex operations)
const apiCall = async (endpoint: string, options: RequestInit = {}) => {
  const { data: { session } } = await supabase.auth.getSession();
  
  if (!session?.access_token) {
    throw new Error('No authentication token available');
  }

  const url = `${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1${endpoint}`;

  const response = await fetch(url, {
    ...options,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${session.access_token}`,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `API call failed: ${response.status}`);
  }

  const data = await response.json();
  return data;
};

// === COMPLEX BUSINESS LOGIC ONLY ===
// These operations require server-side processing and validation

// Task completion uses direct Supabase RPC calls (simple database operations)
// No Edge Function needed for basic task completion

// Reward redemption (complex business logic)
export const rewardRedemptionApi = {
  redeem: (rewardId: string) =>
    apiCall(`/rewards/${rewardId}/redeem`, {
      method: 'POST',
    }),
};

// Achievement unlocking (complex business logic)
export const achievementApi = {
  checkAndUnlock: (childId: string) =>
    apiCall(`/achievements/check-unlock/${childId}`, {
      method: 'POST',
    }),
};

// Analytics (complex aggregations)
export const analyticsApi = {
  getChildAnalytics: (childId: string) =>
    apiCall(`/analytics/child/${childId}`),
  
  getFamilyAnalytics: () =>
    apiCall('/analytics/family'),
};

// Monetization (complex business logic)
export const monetizationApi = {
  checkChildLimit: () =>
    apiCall('/monetization/check-child-limit', { method: 'POST' }),
  
  upgrade: (tier: string, purchaseType: string = 'one_time', purchaseToken?: string) =>
    apiCall('/monetization/upgrade', {
      method: 'POST',
      body: JSON.stringify({ tier, purchase_type: purchaseType, purchase_token: purchaseToken }),
    }),
};

// User onboarding (complex business logic)
export const userApi = {
  completeOnboarding: (onboardingData: any) =>
    apiCall('/user/onboarding', {
      method: 'POST',
      body: JSON.stringify(onboardingData),
    }),
  
  updateTutorialProgress: (tutorialData: { completed: boolean; currentStep: number }) =>
    apiCall('/user/tutorial', {
      method: 'POST',
      body: JSON.stringify(tutorialData),
    }),
  
  resetDevData: () =>
    apiCall('/user/reset-dev', { method: 'POST' }),
}; 