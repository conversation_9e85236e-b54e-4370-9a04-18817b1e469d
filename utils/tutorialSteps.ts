import { Dimensions } from 'react-native';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

export interface TutorialStep {
  id: string;
  title: string;
  description: string;
  highlightArea: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  buttonText: string;
  glitterEffect?: boolean;
  targetScreen?: string;
  action?: 'navigate' | 'highlight' | 'complete';
  noHighlight?: boolean; // Flag to indicate no area should be highlighted
}

// Improved coordinates for UI elements in FamilyDashboard
// Using larger areas to ensure buttons are covered regardless of device differences
const DASHBOARD_ELEMENTS = {
  create_task_button: {
    x: 10,
    y: 550, // Position for "Create Task" button in Quick Actions section
    width: SCREEN_WIDTH / 2 - 20,
    height: 100,
  },
  create_reward_button: {
    x: 10, 
    y: 950, // Position for "Reward Store" button
    width: SCREEN_WIDTH / 2 - 20,
    height: 100,
  },
};

// Task Creation Screen Elements - More accurate positions
const TASK_CREATION_ELEMENTS = {
  task_name_field: {
    x: 20,
    y: 170,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
  task_value_field: {
    x: 20,
    y: 250,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
  assign_children_section: {
    x: 20,
    y: 420,
    width: SCREEN_WIDTH - 40,
    height: 150,
  },
  create_task_button: {
    x: 20,
    y: SCREEN_HEIGHT - 120,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
};

// Reward Creation Screen Elements - More accurate positions
const REWARD_CREATION_ELEMENTS = {
  reward_name_field: {
    x: 20,
    y: 170,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
  reward_value_field: {
    x: 20,
    y: 250,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
  create_reward_button: {
    x: 20,
    y: SCREEN_HEIGHT - 120,
    width: SCREEN_WIDTH - 40,
    height: 60,
  },
};

export const TUTORIAL_STEPS: TutorialStep[] = [
  // Welcome step with no highlight
  {
    id: 'welcome_dashboard',
    title: 'Welcome to KidsCoin! 🎉',
    description: 'Let\'s get you set up with your first task and reward for your child. We\'ll guide you through the process step by step.',
    highlightArea: {
      x: 0,
      y: 0,
      width: 0,
      height: 0,
    },
    buttonText: 'Let\'s Get Started!',
    glitterEffect: false,
    action: 'highlight',
    noHighlight: true, // This flag indicates no area should be highlighted
  },
  
  // Create Task Button
  {
    id: 'create_task_button',
    title: 'Create Your First Task 📋',
    description: 'Let\'s start by creating a task. Tap the "Create Task" button to begin.',
    highlightArea: DASHBOARD_ELEMENTS.create_task_button,
    buttonText: 'Tap Create Task',
    glitterEffect: true,
    action: 'navigate',
    targetScreen: 'TaskCreation',
  },
  
  // Task Creation Form
  {
    id: 'task_creation_form',
    title: 'Task Details 📝',
    description: 'Give your task a name, like "Clean your room" or "Complete homework". Be clear so your child understands what to do.',
    highlightArea: TASK_CREATION_ELEMENTS.task_name_field,
    buttonText: 'Continue',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Task Value
  {
    id: 'task_value',
    title: 'Task Value 🪙',
    description: 'Set how many coins this task is worth. Consider the effort required - bigger tasks should be worth more coins!',
    highlightArea: TASK_CREATION_ELEMENTS.task_value_field,
    buttonText: 'Continue',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Task Assignment
  {
    id: 'task_assign_children',
    title: 'Assign Children 👨‍👩‍👧‍👦',
    description: 'Select which children should receive this task. You can assign the same task to multiple children.',
    highlightArea: TASK_CREATION_ELEMENTS.assign_children_section,
    buttonText: 'Continue',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Create Task Button
  {
    id: 'task_create_button',
    title: 'Create The Task! 🚀',
    description: 'Now tap "Create Task" to finish setting up your first task. Your children will be able to see and complete it!',
    highlightArea: TASK_CREATION_ELEMENTS.create_task_button,
    buttonText: 'Tap Create Task',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Task Created Successfully
  {
    id: 'task_created_success',
    title: 'Task Created Successfully! ✅',
    description: 'Great job! Now let\'s create a reward that your children can spend their coins on.',
    highlightArea: DASHBOARD_ELEMENTS.create_reward_button,
    buttonText: 'Create a Reward',
    glitterEffect: true,
    action: 'navigate',
    targetScreen: 'RewardCreation',
  },
  
  // Reward Creation Form
  {
    id: 'reward_creation_form',
    title: 'Reward Details 🎁',
    description: 'Give your reward a name, like "30 minutes of screen time" or "Pick dinner menu". Make it motivating for your children!',
    highlightArea: REWARD_CREATION_ELEMENTS.reward_name_field,
    buttonText: 'Continue',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Reward Value
  {
    id: 'reward_value',
    title: 'Reward Cost 💰',
    description: 'Set how many coins this reward costs. Special rewards should cost more to encourage saving and goal-setting!',
    highlightArea: REWARD_CREATION_ELEMENTS.reward_value_field,
    buttonText: 'Continue',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // Create Reward Button
  {
    id: 'reward_create_button',
    title: 'Create The Reward! 🎉',
    description: 'Tap "Create Reward" to finish setting up your first reward. Your children will be able to spend their coins on it!',
    highlightArea: REWARD_CREATION_ELEMENTS.create_reward_button,
    buttonText: 'Tap Create Reward',
    glitterEffect: true,
    action: 'highlight',
  },
  
  // All Set - Success
  {
    id: 'tutorial_complete',
    title: 'You\'re All Set! 🌟',
    description: 'Congratulations! You\'ve created your first task and reward. Your children can now start earning and spending coins!',
    highlightArea: {
      x: SCREEN_WIDTH * 0.1,
      y: 180,
      width: SCREEN_WIDTH * 0.8,
      height: 150,
    },
    buttonText: 'Start Using KidsCoin!',
    glitterEffect: true,
    action: 'complete',
    noHighlight: true, // No highlight on completion
  },
];

export interface TutorialState {
  isActive: boolean;
  currentStepIndex: number;
  currentStepId: string;
  hasCreatedTask: boolean;
  hasCreatedReward: boolean;
  currentScreen: string;
}

export const getInitialTutorialState = (): TutorialState => ({
  isActive: false,
  currentStepIndex: 0, // Always start from the first step
  currentStepId: TUTORIAL_STEPS[0].id,
  hasCreatedTask: false,
  hasCreatedReward: false,
  currentScreen: 'Dashboard',
});

export const getTutorialStep = (stepId: string): TutorialStep | null => {
  return TUTORIAL_STEPS.find(step => step.id === stepId) || null;
};

export const getNextTutorialStep = (currentStepId: string): TutorialStep | null => {
  const currentIndex = TUTORIAL_STEPS.findIndex(step => step.id === currentStepId);
  if (currentIndex === -1 || currentIndex === TUTORIAL_STEPS.length - 1) {
    return null;
  }
  return TUTORIAL_STEPS[currentIndex + 1];
};

export const shouldShowTutorialStep = (stepId: string, state: TutorialState): boolean => {
  const step = getTutorialStep(stepId);
  if (!step) return false;

  // Skip task-related steps if task is already created
  if (stepId.includes('task') && state.hasCreatedTask && stepId !== 'task_created_success') {
    return false;
  }

  // Skip reward-related steps if reward is already created
  if (stepId.includes('reward') && state.hasCreatedReward) {
    return false;
  }

  return true;
};

export const updateTutorialProgress = (
  state: TutorialState,
  updates: Partial<TutorialState>
): TutorialState => {
  return { ...state, ...updates };
}; 