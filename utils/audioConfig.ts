// 🎵 CENTRALIZED AUDIO CONFIGURATION
// Adjust all audio settings here in one place
export const AUDIO_CONFIG = {
  // Background Music Settings
  backgroundMusic: {
    defaultVolume: 0.15,  // 15% volume for background music (reduced from 30%)
    tracks: {
      onboarding: require('../assets/music/background.mp3'),
      main: require('../assets/music/background.mp3'),
      celebration: require('../assets/sounds/confetti.wav'),
    }
  },
  
  // 🔊 Sound Effects Settings & Mappings
  soundEffects: {
    defaultVolume: 0.8,  // 80% volume for sound effects
    files: {
      'button_press': { file: require('../assets/sounds/button-click.wav'), volume: 1.0 },
      'button_success': { file: require('../assets/sounds/button-click.wav'), volume: 1.0 },
      'button_back': { file: require('../assets/sounds/button-click.wav'), volume: 0.8 },
      'selection_change': { file: require('../assets/sounds/button-click.wav'), volume: 0.7 },
      'card_flip': { file: require('../assets/sounds/button-click.wav'), volume: 0.6 },
      'achievement_unlock': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
      'coin_collect': { file: require('../assets/sounds/coin.wav'), volume: 0.8 },
      'level_up': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
      'error': { file: require('../assets/sounds/button-click.wav'), volume: 0.4 },
      'celebration': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
      'notification': { file: require('../assets/sounds/button-click.wav'), volume: 0.7 },
      'swipe': { file: require('../assets/sounds/button-click.wav'), volume: 0.5 },
      'pop': { file: require('../assets/sounds/button-click.wav'), volume: 0.6 },
      'chime': { file: require('../assets/sounds/coin.wav'), volume: 0.8 },
    }
  },
  
  // 🎛️ Master Volume Settings
  masterVolume: 0.7,  // 70% overall volume
};

export type SoundType = 
  | 'button_press'
  | 'button_success' 
  | 'button_back'
  | 'selection_change'
  | 'card_flip'
  | 'achievement_unlock'
  | 'coin_collect'
  | 'level_up'
  | 'error'
  | 'celebration'
  | 'notification'
  | 'swipe'
  | 'pop'
  | 'chime';

export type InteractionType = 
  | 'button_primary'
  | 'button_secondary' 
  | 'button_success'
  | 'button_back'
  | 'button_danger'
  | 'selection'
  | 'card_select'
  | 'achievement'
  | 'error'
  | 'celebration'
  | 'navigation';

// Sound mapping for different interaction types
export const INTERACTION_SOUND_MAP: Record<InteractionType, { sound: SoundType; haptic: 'light' | 'medium' | 'heavy' }> = {
  button_primary: { sound: 'button_press', haptic: 'medium' },
  button_secondary: { sound: 'button_press', haptic: 'light' },
  button_success: { sound: 'button_success', haptic: 'heavy' },
  button_back: { sound: 'button_back', haptic: 'light' },
  button_danger: { sound: 'error', haptic: 'heavy' },
  selection: { sound: 'selection_change', haptic: 'light' },
  card_select: { sound: 'card_flip', haptic: 'medium' },
  achievement: { sound: 'achievement_unlock', haptic: 'heavy' },
  error: { sound: 'error', haptic: 'heavy' },
  celebration: { sound: 'celebration', haptic: 'heavy' },
  navigation: { sound: 'swipe', haptic: 'light' },
}; 