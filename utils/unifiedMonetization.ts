import { supabase } from './supabase';

// Unified monetization interfaces
export interface MonetizationFeature {
  id: string;
  name: string;
  description: string;
  category: 'core' | 'ai_generation' | 'analytics' | 'customization' | 'advanced_features';
  is_premium: boolean;
  tier: 'free' | 'basic' | 'premium' | 'enterprise';
  purchase_type: 'one_time' | 'subscription' | 'both';
  usage_limits?: {
    daily?: number;
    monthly?: number;
    total?: number;
  };
  pricing: {
    one_time?: number;
    monthly?: number;
    yearly?: number;
  };
  legacy_key?: string; // For backward compatibility with app_config
}

export interface AdminMonetizationConfig {
  id: string;
  feature_id: string;
  is_enabled: boolean;
  is_premium: boolean;
  tier_requirement: 'free' | 'basic' | 'premium' | 'enterprise';
  purchase_type: 'one_time' | 'subscription' | 'both';
  usage_limits: {
    daily?: number;
    monthly?: number;
    total?: number;
  };
  pricing: {
    one_time?: number;
    monthly?: number;
    yearly?: number;
  };
  created_at: string;
  updated_at: string;
}

export interface UserSubscription {
  id: string;
  user_id: string;
  family_id: string;
  tier: 'free' | 'basic' | 'premium' | 'enterprise';
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  purchase_type: 'one_time' | 'subscription';
  start_date: string;
  end_date?: string;
  features_enabled: string[];
  usage_tracking: { [feature_id: string]: number };
  purchase_token?: string;
  original_transaction_id?: string;
  auto_renew: boolean;
  created_at: string;
  updated_at: string;
}

// Core features (legacy system compatibility)
export const CORE_FEATURES: MonetizationFeature[] = [
  {
    id: 'child_profiles',
    name: 'Child Profiles',
    description: 'Add children to your family',
    category: 'core',
    is_premium: false,
    tier: 'free',
    purchase_type: 'one_time',
    usage_limits: {
      total: 1, // Free tier limit
    },
    pricing: {
      one_time: 0,
    },
    legacy_key: 'free_child_limit',
  },
  {
    id: 'realtime_sync',
    name: 'Real-time Sync',
    description: 'Sync changes across all devices instantly',
    category: 'core',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'both',
    usage_limits: {},
    pricing: {
      one_time: 2.99,
      monthly: 0.99,
    },
    legacy_key: 'realtime_sync_enabled',
  },
  {
    id: 'unlimited_children',
    name: 'Unlimited Children',
    description: 'Add unlimited children to your family',
    category: 'core',
    is_premium: true,
    tier: 'basic',
    purchase_type: 'one_time',
    usage_limits: {
      total: 999,
    },
    pricing: {
      one_time: 4.99,
    },
  },
];

// AI and advanced features
export const ADVANCED_FEATURES: MonetizationFeature[] = [
  {
    id: 'ai_task_generation',
    name: 'AI Task Generation',
    description: 'Generate personalized tasks using AI based on child interests and family patterns',
    category: 'ai_generation',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      daily: 10,
      monthly: 100,
    },
    pricing: {
      monthly: 9.99,
      yearly: 99.99,
    },
  },
  {
    id: 'ai_reward_generation',
    name: 'AI Reward Generation',
    description: 'Generate personalized rewards using AI based on child preferences and budget',
    category: 'ai_generation',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      daily: 10,
      monthly: 100,
    },
    pricing: {
      monthly: 9.99,
      yearly: 99.99,
    },
  },
  {
    id: 'ai_personalized_suggestions',
    name: 'Personalized AI Suggestions',
    description: 'AI suggestions that learn from your family\'s usage patterns and preferences',
    category: 'ai_generation',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      daily: 20,
      monthly: 200,
    },
    pricing: {
      monthly: 9.99,
      yearly: 99.99,
    },
  },
  {
    id: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Detailed insights into child progress, family patterns, and learning recommendations',
    category: 'analytics',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      monthly: 1000,
    },
    pricing: {
      monthly: 7.99,
      yearly: 79.99,
    },
  },
  {
    id: 'learning_insights',
    name: 'Learning Insights',
    description: 'AI-powered insights that improve suggestions based on family usage patterns',
    category: 'analytics',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      monthly: 500,
    },
    pricing: {
      monthly: 7.99,
      yearly: 79.99,
    },
  },
  {
    id: 'custom_categories',
    name: 'Custom Categories',
    description: 'Create your own task and reward categories beyond the default options',
    category: 'customization',
    is_premium: true,
    tier: 'basic',
    purchase_type: 'one_time',
    usage_limits: {
      total: 10,
    },
    pricing: {
      one_time: 4.99,
    },
  },
  {
    id: 'custom_coin_names',
    name: 'Custom Coin Names',
    description: 'Personalize your family\'s currency with custom names and themes',
    category: 'customization',
    is_premium: true,
    tier: 'basic',
    purchase_type: 'one_time',
    usage_limits: {
      total: 5,
    },
    pricing: {
      one_time: 2.99,
    },
  },
  {
    id: 'family_sharing',
    name: 'Family Sharing',
    description: 'Share tasks and rewards across multiple family accounts',
    category: 'advanced_features',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      total: 10,
    },
    pricing: {
      monthly: 12.99,
      yearly: 129.99,
    },
  },
  {
    id: 'export_data',
    name: 'Data Export',
    description: 'Export your family\'s data and progress reports',
    category: 'advanced_features',
    is_premium: true,
    tier: 'premium',
    purchase_type: 'subscription',
    usage_limits: {
      monthly: 5,
    },
    pricing: {
      monthly: 5.99,
      yearly: 59.99,
    },
  },
];

// Legacy tier definitions for backward compatibility
export const LEGACY_TIERS = {
  basic: {
    name: 'Basic',
    price: 4.99,
    features: ['unlimited_children', 'custom_categories', 'custom_coin_names'],
    child_limit: 3,
    realtime: false,
  },
  premium: {
    name: 'Premium',
    price: 9.99,
    features: [
      'unlimited_children',
      'realtime_sync',
      'ai_task_generation',
      'ai_reward_generation',
      'ai_personalized_suggestions',
      'advanced_analytics',
      'learning_insights',
      'family_sharing',
      'export_data',
    ],
    child_limit: 10,
    realtime: true,
  },
  realtime_only: {
    name: 'Real-time Only',
    price: 2.99,
    features: ['realtime_sync'],
    child_limit: 1,
    realtime: true,
  },
  realtime_monthly: {
    name: 'Real-time Monthly',
    price: 0.99,
    features: ['realtime_sync'],
    child_limit: 1,
    realtime: true,
    recurring: true,
  },
};

// Unified functions
export const getAllFeatures = (): MonetizationFeature[] => {
  return [...CORE_FEATURES, ...ADVANCED_FEATURES];
};

export const getFeaturesByTier = (tier: string): MonetizationFeature[] => {
  return getAllFeatures().filter(f => f.tier === tier);
};

export const getFeaturesByCategory = (category: string): MonetizationFeature[] => {
  return getAllFeatures().filter(f => f.category === category);
};

export const getFeaturesByPurchaseType = (purchaseType: 'one_time' | 'subscription' | 'both'): MonetizationFeature[] => {
  return getAllFeatures().filter(f => f.purchase_type === purchaseType || f.purchase_type === 'both');
};

// Admin configuration management
export const getAdminMonetizationConfig = async (): Promise<AdminMonetizationConfig[]> => {
  try {
    const { data, error } = await supabase
      .from('admin_monetization_config')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching admin monetization config:', error);
    return [];
  }
};

export const updateAdminMonetizationConfig = async (
  featureId: string,
  config: Partial<AdminMonetizationConfig>
) => {
  try {
    const { data, error } = await supabase
      .from('admin_monetization_config')
      .upsert({
        feature_id: featureId,
        ...config,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error updating admin monetization config:', error);
    throw error;
  }
};

export const resetFeatureToDefault = async (featureId: string) => {
  try {
    const defaultFeature = getAllFeatures().find(f => f.id === featureId);
    
    if (!defaultFeature) {
      throw new Error(`Feature ${featureId} not found in defaults`);
    }

    const { error } = await supabase
      .from('admin_monetization_config')
      .delete()
      .eq('feature_id', featureId);

    if (error) throw error;
    return true;
  } catch (error) {
    console.error('Error resetting feature to default:', error);
    throw error;
  }
};

// User subscription management
export const getUserSubscription = async (userId: string): Promise<UserSubscription | null> => {
  try {
    const { data, error } = await supabase
      .from('user_subscriptions')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error && error.code === 'PGRST116') {
      // No subscription found, create free tier
      return await createFreeSubscription(userId);
    }

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching user subscription:', error);
    return null;
  }
};

export const createFreeSubscription = async (userId: string): Promise<UserSubscription> => {
  try {
    // Get user's family ID
    const { data: user } = await supabase
      .from('users')
      .select('family_id')
      .eq('id', userId)
      .single();

    if (!user?.family_id) {
      throw new Error('User not associated with a family');
    }

    const freeFeatures = getAllFeatures()
      .filter(f => f.tier === 'free')
      .map(f => f.id);

    const { data, error } = await supabase
      .from('user_subscriptions')
      .insert({
        user_id: userId,
        family_id: user.family_id,
        tier: 'free',
        status: 'active',
        purchase_type: 'one_time',
        start_date: new Date().toISOString(),
        features_enabled: freeFeatures,
        usage_tracking: {},
        auto_renew: false,
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating free subscription:', error);
    throw error;
  }
};

export const upgradeSubscription = async (
  userId: string,
  tier: 'basic' | 'premium' | 'enterprise',
  purchaseType: 'one_time' | 'subscription' = 'one_time',
  purchaseToken?: string,
  originalTransactionId?: string
) => {
  try {
    const legacyTier = LEGACY_TIERS[tier as keyof typeof LEGACY_TIERS];
    const features = legacyTier ? legacyTier.features : getAllFeatures()
      .filter(f => f.tier === tier || f.tier === 'free')
      .map(f => f.id);

    const expiresAt = purchaseType === 'subscription' 
      ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      : undefined;

    const { data, error } = await supabase
      .from('user_subscriptions')
      .update({
        tier,
        purchase_type: purchaseType,
        features_enabled: features,
        end_date: expiresAt,
        purchase_token: purchaseToken,
        original_transaction_id: originalTransactionId,
        auto_renew: purchaseType === 'subscription',
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error upgrading subscription:', error);
    throw error;
  }
};

// Feature access control
export const checkFeatureAccess = async (
  userId: string,
  featureId: string
): Promise<{ hasAccess: boolean; reason?: string; usage?: any }> => {
  try {
    // Get user subscription
    const subscription = await getUserSubscription(userId);
    if (!subscription) {
      return { hasAccess: false, reason: 'No subscription found' };
    }

    // Get admin config for this feature
    const { data: adminConfig } = await supabase
      .from('admin_monetization_config')
      .select('*')
      .eq('feature_id', featureId)
      .single();

    // Get default feature config
    const defaultFeature = getAllFeatures().find(f => f.id === featureId);

    if (!defaultFeature) {
      return { hasAccess: false, reason: 'Feature not found' };
    }

    // Use admin config if available, otherwise use default
    const featureConfig = adminConfig || defaultFeature;

    // Check if feature is enabled
    if (adminConfig && !adminConfig.is_enabled) {
      return { hasAccess: false, reason: 'Feature disabled by admin' };
    }

    // Check if feature requires premium
    if (featureConfig.is_premium) {
      const tierOrder = { free: 0, basic: 1, premium: 2, enterprise: 3 };
      const userTier = tierOrder[subscription.tier as keyof typeof tierOrder] || 0;
      const requiredTier = tierOrder[featureConfig.tier as keyof typeof tierOrder] || 0;

      if (userTier < requiredTier) {
        return { 
          hasAccess: false, 
          reason: `Requires ${featureConfig.tier} tier or higher` 
        };
      }
    }

    // Check usage limits
    const usage = subscription.usage_tracking[featureId] || 0;
    const limits = featureConfig.usage_limits || {};

    if (limits.daily && usage >= limits.daily) {
      return { 
        hasAccess: false, 
        reason: 'Daily usage limit reached',
        usage: { current: usage, limit: limits.daily }
      };
    }

    if (limits.monthly && usage >= limits.monthly) {
      return { 
        hasAccess: false, 
        reason: 'Monthly usage limit reached',
        usage: { current: usage, limit: limits.monthly }
      };
    }

    if (limits.total && usage >= limits.total) {
      return { 
        hasAccess: false, 
        reason: 'Total usage limit reached',
        usage: { current: usage, limit: limits.total }
      };
    }

    return { hasAccess: true, usage: { current: usage, limits } };
  } catch (error) {
    console.error('Error checking feature access:', error);
    return { hasAccess: false, reason: 'Error checking access' };
  }
};

export const recordFeatureUsage = async (userId: string, featureId: string) => {
  try {
    const subscription = await getUserSubscription(userId);
    if (!subscription) return;

    const currentUsage = subscription.usage_tracking[featureId] || 0;
    const newUsage = { ...subscription.usage_tracking, [featureId]: currentUsage + 1 };

    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        usage_tracking: newUsage,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) throw error;
  } catch (error) {
    console.error('Error recording feature usage:', error);
  }
};

// Legacy compatibility functions
export const checkChildLimit = async (userId: string): Promise<{ canAdd: boolean; current: number; limit: number }> => {
  try {
    const access = await checkFeatureAccess(userId, 'child_profiles');
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return { canAdd: false, current: 0, limit: 1 };
    }

    // Get current children count
    const { data: children } = await supabase
      .from('children')
      .select('id')
      .eq('parent_id', userId);

    const currentCount = children?.length || 0;
    const limit = subscription.tier === 'free' ? 1 : 
                  subscription.tier === 'basic' ? 3 : 999;

    return {
      canAdd: access.hasAccess && currentCount < limit,
      current: currentCount,
      limit,
    };
  } catch (error) {
    console.error('Error checking child limit:', error);
    return { canAdd: false, current: 0, limit: 1 };
  }
};

export const checkRealtimeAccess = async (userId: string): Promise<boolean> => {
  try {
    const access = await checkFeatureAccess(userId, 'realtime_sync');
    return access.hasAccess;
  } catch (error) {
    console.error('Error checking realtime access:', error);
    return false;
  }
};

// Admin override functions
export const grantFeatureAccess = async (
  userId: string,
  featureId: string,
  duration?: number // days
) => {
  try {
    const subscription = await getUserSubscription(userId);
    if (!subscription) return;

    const features = [...subscription.features_enabled];
    if (!features.includes(featureId)) {
      features.push(featureId);
    }

    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        features_enabled: features,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) throw error;

    // Log admin override
    await supabase
      .from('admin_overrides')
      .insert({
        user_id: userId,
        feature_id: featureId,
        action: 'granted',
        duration_days: duration,
        created_at: new Date().toISOString(),
      });

    return true;
  } catch (error) {
    console.error('Error granting feature access:', error);
    throw error;
  }
};

export const revokeFeatureAccess = async (userId: string, featureId: string) => {
  try {
    const subscription = await getUserSubscription(userId);
    if (!subscription) return;

    const features = subscription.features_enabled.filter(f => f !== featureId);

    const { error } = await supabase
      .from('user_subscriptions')
      .update({
        features_enabled: features,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId);

    if (error) throw error;

    // Log admin override
    await supabase
      .from('admin_overrides')
      .insert({
        user_id: userId,
        feature_id: featureId,
        action: 'revoked',
        created_at: new Date().toISOString(),
      });

    return true;
  } catch (error) {
    console.error('Error revoking feature access:', error);
    throw error;
  }
};

// Analytics for admin
export const getMonetizationAnalytics = async () => {
  try {
    const { data: subscriptions, error: subError } = await supabase
      .from('user_subscriptions')
      .select('*');

    if (subError) throw subError;

    const { data: usage, error: usageError } = await supabase
      .from('usage_analytics')
      .select('*');

    if (usageError) throw usageError;

    // Calculate analytics
    const analytics = {
      total_users: subscriptions?.length || 0,
      tier_distribution: subscriptions?.reduce((acc, sub) => {
        acc[sub.tier] = (acc[sub.tier] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }) || {},
      purchase_type_distribution: subscriptions?.reduce((acc, sub) => {
        acc[sub.purchase_type] = (acc[sub.purchase_type] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }) || {},
      feature_usage: usage?.reduce((acc, u) => {
        const feature = u.activity_data?.source || 'unknown';
        acc[feature] = (acc[feature] || 0) + 1;
        return acc;
      }, {} as { [key: string]: number }) || {},
      revenue_estimate: calculateRevenueEstimate(subscriptions || []),
    };

    return analytics;
  } catch (error) {
    console.error('Error getting monetization analytics:', error);
    return null;
  }
};

const calculateRevenueEstimate = (subscriptions: UserSubscription[]) => {
  const tierPricing = {
    basic: 4.99,
    premium: 9.99,
    enterprise: 49.99,
  };

  return subscriptions.reduce((total, sub) => {
    if (sub.tier !== 'free' && sub.status === 'active') {
      const basePrice = tierPricing[sub.tier as keyof typeof tierPricing] || 0;
      if (sub.purchase_type === 'subscription' && sub.auto_renew) {
        total += basePrice; // Monthly recurring
      } else {
        total += basePrice; // One-time purchase
      }
    }
    return total;
  }, 0);
};

// Utility functions
export const isFeaturePremium = (featureId: string): boolean => {
  const feature = getAllFeatures().find(f => f.id === featureId);
  return feature?.is_premium || false;
};

export const getFeaturePricing = (featureId: string) => {
  const feature = getAllFeatures().find(f => f.id === featureId);
  return feature?.pricing || {};
};

export const getLegacyTierInfo = (tier: string) => {
  return LEGACY_TIERS[tier as keyof typeof LEGACY_TIERS];
}; 