import { supabase } from './supabase';

export interface RewardValidationResult {
  isValid: boolean;
  errors: string[];
}

export interface RewardData {
  title: string;
  description?: string;
  cost: number;
  category?: string;
  parent_id: string;
}

export interface RewardRedemptionData {
  reward_id: string;
  child_id: string;
  cost: number;
  current_balance: number;
}

// Reward Creation Validation
export const validateReward = (data: RewardData): RewardValidationResult => {
  const errors: string[] = [];

  // Required fields
  if (!data.title || data.title.trim().length === 0) {
    errors.push('Reward title is required');
  }

  if (data.title && data.title.trim().length > 100) {
    errors.push('Reward title must be 100 characters or less');
  }

  if (!data.parent_id) {
    errors.push('Parent ID is required');
  }

  // Cost validation
  if (data.cost == null || data.cost < 1 || data.cost > 1000) {
    errors.push('Reward cost must be between 1 and 1000 coins');
  }

  if (data.cost && !Number.isInteger(data.cost)) {
    errors.push('Reward cost must be a whole number');
  }

  // Optional field validation
  if (data.description && data.description.length > 500) {
    errors.push('Reward description must be 500 characters or less');
  }

  if (data.category && data.category.length > 50) {
    errors.push('Category must be 50 characters or less');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Reward Redemption Validation (Synchronous version for smart hooks)
export const validateRewardRedemptionSync = (cost: number, currentBalance: number): RewardValidationResult => {
  const errors: string[] = [];

  // Cost validation
  if (cost == null || cost < 1) {
    errors.push('Reward cost must be at least 1 coin');
  }

  // Check if child has sufficient balance
  if (currentBalance < cost) {
    errors.push(`Insufficient coins. Need ${cost} coins, but only have ${currentBalance}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Reward Redemption Validation (Async version for server validation)
export const validateRewardRedemption = async (data: RewardRedemptionData): Promise<RewardValidationResult> => {
  const errors: string[] = [];

  // Required fields
  if (!data.reward_id) {
    errors.push('Reward ID is required');
  }

  if (!data.child_id) {
    errors.push('Child ID is required');
  }

  if (data.cost == null || data.cost < 1) {
    errors.push('Reward cost must be at least 1 coin');
  }

  // Check if child has sufficient balance
  if (data.current_balance < data.cost) {
    errors.push(`Insufficient coins. Need ${data.cost} coins, but only have ${data.current_balance}`);
  }

  // Check if reward exists
  if (data.reward_id) {
    try {
      const { data: rewardData, error } = await supabase
        .from('rewards')
        .select('id, cost')
        .eq('id', data.reward_id)
        .single();

      if (error || !rewardData) {
        errors.push('Reward does not exist');
      } else {
        // Verify cost matches
        if (rewardData.cost !== data.cost) {
          errors.push('Reward cost mismatch');
        }
      }
    } catch (error) {
      errors.push('Failed to validate reward');
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Redeem Reward (updates balance and creates unlock record)
export const redeemReward = async (childId: string, rewardId: string, cost: number): Promise<void> => {
  // Start transaction by getting current balance
  const { data: childData, error: fetchError } = await supabase
    .from('children')
    .select('coin_balance')
    .eq('id', childId)
    .single();

  if (fetchError || !childData) {
    throw new Error('Failed to fetch child data');
  }

  if (childData.coin_balance < cost) {
    throw new Error('Insufficient coins');
  }

  // Update child's coin balance
  const newBalance = childData.coin_balance - cost;
  const { error: updateError } = await supabase
    .from('children')
    .update({ coin_balance: newBalance })
    .eq('id', childId);

  if (updateError) {
    throw new Error('Failed to update coin balance');
  }

  // Create unlock record
  const { error: unlockError } = await supabase
    .from('unlocked_rewards')
    .insert({
      child_id: childId,
      reward_id: rewardId,
      unlocked_at: new Date().toISOString()
    });

  if (unlockError) {
    throw new Error('Failed to create unlock record');
  }
};

// Check Achievement Progress
export const checkAchievements = async (childId: string): Promise<void> => {
  // Get child's current stats
  const { data: childData, error: childError } = await supabase
    .from('children')
    .select('coin_balance, level, xp, daily_streak')
    .eq('id', childId)
    .single();

  if (childError || !childData) {
    return;
  }

  // Get completed tasks count
  const { data: taskData, error: taskError } = await supabase
    .from('tasks')
    .select('id')
    .eq('child_id', childId)
    .eq('status', 'completed');

  if (taskError) {
    return;
  }

  const completedTasks = taskData?.length || 0;

  // Get all achievements not yet unlocked by this child
  const { data: achievements, error: achievementError } = await supabase
    .from('achievements')
    .select(`
      id,
      title,
      criteria_type,
      criteria_value,
      coin_reward
    `)
    .not('id', 'in', `(
      SELECT achievement_id 
      FROM child_achievements 
      WHERE child_id = '${childId}'
    )`);

  if (achievementError || !achievements) {
    return;
  }

  // Check each achievement
  for (const achievement of achievements) {
    let shouldUnlock = false;

    switch (achievement.criteria_type) {
      case 'tasks_completed':
        shouldUnlock = completedTasks >= achievement.criteria_value;
        break;
      case 'coins_earned':
        shouldUnlock = childData.coin_balance >= achievement.criteria_value;
        break;
      case 'level_reached':
        shouldUnlock = childData.level >= achievement.criteria_value;
        break;
      case 'streak_days':
        shouldUnlock = childData.daily_streak >= achievement.criteria_value;
        break;
    }

    if (shouldUnlock) {
      // Unlock achievement
      await supabase
        .from('child_achievements')
        .insert({
          child_id: childId,
          achievement_id: achievement.id,
          unlocked_at: new Date().toISOString()
        });

      // Award coin bonus
      if (achievement.coin_reward > 0) {
        await supabase
          .from('children')
          .update({
            coin_balance: childData.coin_balance + achievement.coin_reward
          })
          .eq('id', childId);
      }
    }
  }
};

// Database Schema Constants
export const REWARD_CATEGORIES = [
  'Treats', 'Toys', 'Activities', 'Screen Time', 'Outings', 'Privileges',
  'Money', 'Experiences', 'Games', 'Books', 'Crafts', 'Sports'
] as const;

export const ACHIEVEMENT_CRITERIA_TYPES = [
  'tasks_completed', 'coins_earned', 'level_reached', 'streak_days'
] as const; 