import { supabase } from './supabase';

// Learning system interfaces
export interface UsagePattern {
  id: string;
  family_id: string;
  pattern_type: 'task_preference' | 'reward_preference' | 'difficulty_preference' | 'category_preference';
  pattern_data: {
    category?: string;
    difficulty?: string;
    age_group?: string;
    success_rate?: number;
    usage_frequency?: number;
    preference_score?: number;
    last_updated?: string;
  };
  confidence_score: number;
  created_at: string;
  updated_at: string;
}

export interface FamilyInsights {
  family_id: string;
  preferred_categories: string[];
  avoided_categories: string[];
  successful_difficulty_levels: { [age_group: string]: string };
  peak_activity_times: string[];
  reward_preferences: { [category: string]: number };
  task_completion_patterns: { [category: string]: number };
  interests_extracted: string[];
  family_values: string[];
}

export interface LearningRecommendation {
  type: 'task' | 'reward';
  suggestion: any;
  confidence: number;
  reasoning: string;
  personalization_factors: string[];
}

// Database functions for learning data
export const createFamilyLearningProfile = async (parentId: string) => {
  try {
    const { data, error } = await supabase
      .from('family_learning_profiles')
      .insert({
        parent_id: parentId,
        learning_data: {
          task_patterns: {},
          reward_patterns: {},
          difficulty_preferences: {},
          category_preferences: {},
          time_patterns: {},
          success_metrics: {},
        },
        insights: {
          preferred_categories: [],
          avoided_categories: [],
          successful_difficulty_levels: {},
          peak_activity_times: [],
          reward_preferences: {},
          task_completion_patterns: {},
          interests_extracted: [],
          family_values: [],
        },
        confidence_scores: {
          task_suggestions: 0.5,
          reward_suggestions: 0.5,
          difficulty_matching: 0.5,
          category_matching: 0.5,
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error creating family learning profile:', error);
    throw error;
  }
};

export const getFamilyLearningProfile = async (parentId: string) => {
  try {
    const { data, error } = await supabase
      .from('family_learning_profiles')
      .select('*')
      .eq('parent_id', parentId)
      .single();

    if (error && error.code === 'PGRST116') {
      // Profile doesn't exist, create it
      return await createFamilyLearningProfile(parentId);
    }

    if (error) throw error;
    return data;
  } catch (error) {
    console.error('Error fetching family learning profile:', error);
    throw error;
  }
};

// Learning data collection functions
export const recordTaskUsage = async (
  parentId: string,
  taskData: {
    category: string;
    difficulty: string;
    age_group: string;
    child_id: string;
    success: boolean;
    completion_time?: number;
    source: 'manual' | 'ai' | 'library';
  }
) => {
  try {
    const { data, error } = await supabase
      .from('usage_analytics')
      .insert({
        parent_id: parentId,
        child_id: taskData.child_id,
        activity_type: 'task_usage',
        activity_data: {
          category: taskData.category,
          difficulty: taskData.difficulty,
          age_group: taskData.age_group,
          success: taskData.success,
          completion_time: taskData.completion_time,
          source: taskData.source,
        },
        timestamp: new Date().toISOString(),
      });

    if (error) throw error;

    // Update learning patterns
    await updateLearningPatterns(parentId, 'task', taskData);
    
    return data;
  } catch (error) {
    console.error('Error recording task usage:', error);
    throw error;
  }
};

export const recordRewardUsage = async (
  parentId: string,
  rewardData: {
    category: string;
    cost: number;
    age_group: string;
    child_id: string;
    redeemed: boolean;
    satisfaction_score?: number;
    source: 'manual' | 'ai' | 'library';
  }
) => {
  try {
    const { data, error } = await supabase
      .from('usage_analytics')
      .insert({
        parent_id: parentId,
        child_id: rewardData.child_id,
        activity_id: rewardData.child_id,
        activity_type: 'reward_usage',
        activity_data: {
          category: rewardData.category,
          cost: rewardData.cost,
          age_group: rewardData.age_group,
          redeemed: rewardData.redeemed,
          satisfaction_score: rewardData.satisfaction_score,
          source: rewardData.source,
        },
        timestamp: new Date().toISOString(),
      });

    if (error) throw error;

    // Update learning patterns
    await updateLearningPatterns(parentId, 'reward', rewardData);
    
    return data;
  } catch (error) {
    console.error('Error recording reward usage:', error);
    throw error;
  }
};

// Pattern analysis functions
export const updateLearningPatterns = async (
  parentId: string,
  type: 'task' | 'reward',
  activityData: any
) => {
  try {
    const profile = await getFamilyLearningProfile(parentId);
    
    // Update patterns based on activity
    const updatedLearningData = { ...profile.learning_data };
    
    if (type === 'task') {
      updateTaskPatterns(updatedLearningData, activityData);
    } else {
      updateRewardPatterns(updatedLearningData, activityData);
    }
    
    // Calculate new insights
    const insights = calculateInsights(updatedLearningData);
    
    // Update confidence scores
    const confidenceScores = calculateConfidenceScores(updatedLearningData);
    
    // Save updated profile
    const { error } = await supabase
      .from('family_learning_profiles')
      .update({
        learning_data: updatedLearningData,
        insights: insights,
        confidence_scores: confidenceScores,
        updated_at: new Date().toISOString(),
      })
      .eq('parent_id', parentId);

    if (error) throw error;
    
    return { learningData: updatedLearningData, insights, confidenceScores };
  } catch (error) {
    console.error('Error updating learning patterns:', error);
    throw error;
  }
};

const updateTaskPatterns = (learningData: any, taskData: any) => {
  // Update category preferences
  if (!learningData.category_preferences) learningData.category_preferences = {};
  if (!learningData.category_preferences[taskData.category]) {
    learningData.category_preferences[taskData.category] = { count: 0, success_rate: 0 };
  }
  
  const categoryData = learningData.category_preferences[taskData.category];
  categoryData.count = (categoryData.count || 0) + 1;
  categoryData.success_rate = ((categoryData.success_rate || 0) * (categoryData.count - 1) + (taskData.success ? 1 : 0)) / categoryData.count;
  
  // Update difficulty preferences
  if (!learningData.difficulty_preferences) learningData.difficulty_preferences = {};
  if (!learningData.difficulty_preferences[taskData.age_group]) {
    learningData.difficulty_preferences[taskData.age_group] = {};
  }
  
  const ageGroupDifficulty = learningData.difficulty_preferences[taskData.age_group];
  if (!ageGroupDifficulty[taskData.difficulty]) {
    ageGroupDifficulty[taskData.difficulty] = { count: 0, success_rate: 0 };
  }
  
  const difficultyData = ageGroupDifficulty[taskData.difficulty];
  difficultyData.count += 1;
  difficultyData.success_rate = (difficultyData.success_rate * (difficultyData.count - 1) + (taskData.success ? 1 : 0)) / difficultyData.count;
  
  // Update task patterns
  if (!learningData.task_patterns) learningData.task_patterns = {};
  if (!learningData.task_patterns[taskData.source]) {
    learningData.task_patterns[taskData.source] = { count: 0, success_rate: 0 };
  }
  
  const sourceData = learningData.task_patterns[taskData.source];
  sourceData.count += 1;
  sourceData.success_rate = (sourceData.success_rate * (sourceData.count - 1) + (taskData.success ? 1 : 0)) / sourceData.count;
};

const updateRewardPatterns = (learningData: any, rewardData: any) => {
  // Update reward category preferences
  if (!learningData.reward_patterns) learningData.reward_patterns = {};
  if (!learningData.reward_patterns[rewardData.category]) {
    learningData.reward_patterns[rewardData.category] = { count: 0, redemption_rate: 0, avg_satisfaction: 0 };
  }
  
  const categoryData = learningData.reward_patterns[rewardData.category];
  categoryData.count += 1;
  categoryData.redemption_rate = (categoryData.redemption_rate * (categoryData.count - 1) + (rewardData.redeemed ? 1 : 0)) / categoryData.count;
  
  if (rewardData.satisfaction_score) {
    categoryData.avg_satisfaction = (categoryData.avg_satisfaction * (categoryData.count - 1) + rewardData.satisfaction_score) / categoryData.count;
  }
  
  // Update cost patterns
  if (!learningData.cost_patterns) learningData.cost_patterns = {};
  if (!learningData.cost_patterns[rewardData.age_group]) {
    learningData.cost_patterns[rewardData.age_group] = { total_cost: 0, count: 0, avg_cost: 0 };
  }
  
  const ageGroupCost = learningData.cost_patterns[rewardData.age_group];
  ageGroupCost.total_cost += rewardData.cost;
  ageGroupCost.count += 1;
  ageGroupCost.avg_cost = ageGroupCost.total_cost / ageGroupCost.count;
};

const calculateInsights = (learningData: any): FamilyInsights => {
  const insights: Partial<FamilyInsights> = {
    preferred_categories: [],
    avoided_categories: [],
    successful_difficulty_levels: {},
    reward_preferences: {},
    task_completion_patterns: {},
    interests_extracted: [],
    family_values: [],
  };
  
  // Calculate preferred categories
  if (learningData.category_preferences) {
    const categories = Object.entries(learningData.category_preferences)
      .map(([category, data]: [string, any]) => ({
        category,
        score: data.success_rate * Math.log(data.count + 1),
      }))
      .sort((a, b) => b.score - a.score);
    
    insights.preferred_categories = categories.slice(0, 3).map(c => c.category);
    insights.avoided_categories = categories.slice(-2).map(c => c.category);
  }
  
  // Calculate successful difficulty levels
  if (learningData.difficulty_preferences) {
    Object.entries(learningData.difficulty_preferences).forEach(([ageGroup, difficulties]: [string, any]) => {
      const bestDifficulty = Object.entries(difficulties)
        .reduce((best, [difficulty, data]: [string, any]) => {
          if (data.success_rate > best.success_rate) {
            return { difficulty, success_rate: data.success_rate };
          }
          return best;
        }, { difficulty: 'medium', success_rate: 0 });
      
      insights.successful_difficulty_levels![ageGroup] = bestDifficulty.difficulty;
    });
  }
  
  // Calculate reward preferences
  if (learningData.reward_patterns) {
    Object.entries(learningData.reward_patterns).forEach(([category, data]: [string, any]) => {
      insights.reward_preferences![category] = data.redemption_rate * (data.avg_satisfaction || 1);
    });
  }
  
  // Calculate task completion patterns
  if (learningData.category_preferences) {
    Object.entries(learningData.category_preferences).forEach(([category, data]: [string, any]) => {
      insights.task_completion_patterns![category] = data.success_rate;
    });
  }
  
  return insights as FamilyInsights;
};

const calculateConfidenceScores = (learningData: any) => {
  const minDataPoints = 5;
  
  // Calculate confidence based on data volume and consistency
  const taskDataPoints = Object.values(learningData.category_preferences || {})
    .reduce((total: number, data: any) => total + (data?.count || 0), 0);
  
  const rewardDataPoints = Object.values(learningData.reward_patterns || {})
    .reduce((total: number, data: any) => total + (data?.count || 0), 0);
  
  const taskConfidence = Math.min(1.0, (taskDataPoints as number) / (minDataPoints * 3));
  const rewardConfidence = Math.min(1.0, (rewardDataPoints as number) / (minDataPoints * 2));
  
  return {
    task_suggestions: Math.max(0.3, taskConfidence),
    reward_suggestions: Math.max(0.3, rewardConfidence),
    difficulty_matching: Math.max(0.3, taskConfidence * 0.8),
    category_matching: Math.max(0.3, (taskConfidence + rewardConfidence) / 2),
  };
};

// AI enhancement functions
export const enhanceAIPromptWithLearning = async (
  parentId: string,
  basePrompt: string,
  type: 'task' | 'reward',
  childAge: number
) => {
  try {
    const profile = await getFamilyLearningProfile(parentId);
    const insights = profile.insights;
    
    let enhancedPrompt = basePrompt;
    
    // Add family preferences
    if (insights.preferred_categories?.length > 0) {
      enhancedPrompt += `\n\nThis family particularly enjoys: ${insights.preferred_categories.join(', ')}`;
    }
    
    if (insights.avoided_categories?.length > 0) {
      enhancedPrompt += `\nThis family tends to avoid: ${insights.avoided_categories.join(', ')}`;
    }
    
    // Add difficulty preferences
    const ageGroup = getAgeGroupForLearning(childAge);
    if (insights.successful_difficulty_levels?.[ageGroup]) {
      enhancedPrompt += `\nFor this age group, ${insights.successful_difficulty_levels[ageGroup]} difficulty works best.`;
    }
    
    // Add interests
    if (insights.interests_extracted?.length > 0) {
      enhancedPrompt += `\nFamily interests include: ${insights.interests_extracted.join(', ')}`;
    }
    
    // Add success patterns
    if (type === 'task' && insights.task_completion_patterns) {
      const successfulCategories = Object.entries(insights.task_completion_patterns)
        .filter(([_, rate]) => typeof rate === 'number' && rate > 0.7)
        .map(([category, _]) => category);
      
      if (successfulCategories.length > 0) {
        enhancedPrompt += `\nThis family has high success rates with: ${successfulCategories.join(', ')}`;
      }
    }
    
    if (type === 'reward' && insights.reward_preferences) {
      const preferredRewards = Object.entries(insights.reward_preferences)
        .filter(([_, score]) => typeof score === 'number' && score > 0.7)
        .map(([category, _]) => category);
      
      if (preferredRewards.length > 0) {
        enhancedPrompt += `\nThis family prefers these reward types: ${preferredRewards.join(', ')}`;
      }
    }
    
    enhancedPrompt += `\n\nPersonalize the suggestions based on these family patterns and preferences.`;
    
    return enhancedPrompt;
  } catch (error) {
    console.error('Error enhancing AI prompt with learning:', error);
    return basePrompt; // Return original prompt if enhancement fails
  }
};

export const getPersonalizedRecommendations = async (
  parentId: string,
  childId: string,
  type: 'task' | 'reward',
  count: number = 3
): Promise<LearningRecommendation[]> => {
  try {
    const profile = await getFamilyLearningProfile(parentId);
    const insights = profile.insights;
    const confidenceScores = profile.confidence_scores;
    
    const recommendations: LearningRecommendation[] = [];
    
    // Get child's age group
    const { data: child } = await supabase
      .from('children')
      .select('created_at')
      .eq('id', childId)
      .single();
    
    const childAge = child ? calculateAge(child.created_at) : 10;
    const ageGroup = getAgeGroupForLearning(childAge);
    
    // Generate recommendations based on learning patterns
    if (type === 'task') {
      // Recommend tasks in preferred categories
      insights.preferred_categories?.forEach((category, index) => {
        if (recommendations.length < count) {
          recommendations.push({
            type: 'task',
            suggestion: {
              category,
              difficulty: insights.successful_difficulty_levels?.[ageGroup] || 'medium',
              personalized: true,
            },
            confidence: confidenceScores.task_suggestions * (1 - index * 0.1),
            reasoning: `This family has shown high success rates with ${category} tasks`,
            personalization_factors: ['category_preference', 'difficulty_matching'],
          });
        }
      });
    } else {
      // Recommend rewards in preferred categories
      const preferredRewards = Object.entries(insights.reward_preferences || {})
        .sort(([,a], [,b]) => (b as number) - (a as number))
        .slice(0, count);
      
      preferredRewards.forEach(([category, score], index) => {
        recommendations.push({
          type: 'reward',
          suggestion: {
            category,
            personalized: true,
            preference_score: score,
          },
          confidence: confidenceScores.reward_suggestions * (1 - index * 0.1),
          reasoning: `This family frequently redeems and enjoys ${category} rewards`,
          personalization_factors: ['reward_preference', 'redemption_history'],
        });
      });
    }
    
    return recommendations;
  } catch (error) {
    console.error('Error getting personalized recommendations:', error);
    return [];
  }
};

// Utility functions
const getAgeGroupForLearning = (age: number): string => {
  if (age <= 5) return 'TODDLER';
  if (age <= 8) return 'PRESCHOOL';
  if (age <= 12) return 'ELEMENTARY';
  if (age <= 15) return 'MIDDLE_SCHOOL';
  if (age <= 18) return 'HIGH_SCHOOL';
  return 'YOUNG_ADULT';
};

const calculateAge = (createdAt: string): number => {
  const now = new Date();
  const created = new Date(createdAt);
  const ageInMs = now.getTime() - created.getTime();
  const ageInYears = Math.floor(ageInMs / (1000 * 60 * 60 * 24 * 365));
  return Math.max(3, ageInYears + 7); // Assume child was created when they were 7
};

// Analytics functions
export const getFamilyLearningAnalytics = async (parentId: string) => {
  try {
    const profile = await getFamilyLearningProfile(parentId);
    
    // Get usage analytics
    const { data: usageData } = await supabase
      .from('usage_analytics')
      .select('*')
      .eq('parent_id', parentId)
      .order('timestamp', { ascending: false })
      .limit(100);
    
    // Calculate analytics
    const analytics = {
      total_activities: usageData?.length || 0,
      learning_confidence: profile.confidence_scores,
      top_categories: profile.insights.preferred_categories || [],
      success_patterns: profile.insights.task_completion_patterns || {},
      reward_patterns: profile.insights.reward_preferences || {},
      last_updated: profile.updated_at,
      data_quality: calculateDataQuality(profile.learning_data),
    };
    
    return analytics;
  } catch (error) {
    console.error('Error getting family learning analytics:', error);
    return null;
  }
};

const calculateDataQuality = (learningData: any): string => {
  const taskData = Object.values(learningData.category_preferences || {})
    .reduce((total: number, data: any) => total + data.count, 0);
  
  const rewardData = Object.values(learningData.reward_patterns || {})
    .reduce((total: number, data: any) => total + data.count, 0);
  
  const totalData = (taskData as number) + (rewardData as number);
  
  if (totalData < 10) return 'Low';
  if (totalData < 50) return 'Medium';
  if (totalData < 100) return 'High';
  return 'Excellent';
};

export const resetFamilyLearningData = async (parentId: string) => {
  try {
    // Reset learning profile
    await supabase
      .from('family_learning_profiles')
      .update({
        learning_data: {
          task_patterns: {},
          reward_patterns: {},
          difficulty_preferences: {},
          category_preferences: {},
          time_patterns: {},
          success_metrics: {},
        },
        insights: {
          preferred_categories: [],
          avoided_categories: [],
          successful_difficulty_levels: {},
          peak_activity_times: [],
          reward_preferences: {},
          task_completion_patterns: {},
          interests_extracted: [],
          family_values: [],
        },
        confidence_scores: {
          task_suggestions: 0.5,
          reward_suggestions: 0.5,
          difficulty_matching: 0.5,
          category_matching: 0.5,
        },
        updated_at: new Date().toISOString(),
      })
      .eq('parent_id', parentId);
    
    // Clear usage analytics
    await supabase
      .from('usage_analytics')
      .delete()
      .eq('parent_id', parentId);
    
    return true;
  } catch (error) {
    console.error('Error resetting family learning data:', error);
    return false;
  }
}; 