-- Add Monetization Features for Child Profile Limits
-- Created: 2025-01-17
-- Description: Adds subscription management and child limits for freemium model

-- Add monetization fields to family_settings
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS child_limit INT NOT NULL DEFAULT 1;
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS has_unlimited_children BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS parent_pin TEXT NOT NULL DEFAULT '1234';
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS realtime_sync_enabled BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS realtime_subscription_expires_at TIMESTAMPTZ;

-- Create app configuration table for admin controls
CREATE TABLE IF NOT EXISTS app_config (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  key TEXT NOT NULL UNIQUE,
  value JSONB NOT NULL,
  description TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Insert default monetization settings (admin can change these)
INSERT INTO app_config (key, value, description) VALUES
('monetization_enabled', 'true', 'Whether child profile monetization is enabled'),
('free_child_limit', '1', 'Number of children allowed on free tier'),
('premium_child_limit', '10', 'Number of children allowed on premium tier (10 = unlimited for most families)'),
('realtime_sync_enabled', 'true', 'Whether real-time sync monetization is enabled'),
('free_realtime_enabled', 'false', 'Whether free tier includes real-time sync'),
('pricing_tiers', '{"basic": {"price": 4.99, "children": 3, "realtime": false}, "premium": {"price": 9.99, "children": 10, "realtime": true}, "realtime_only": {"price": 2.99, "children": 1, "realtime": true}, "realtime_monthly": {"price": 0.99, "children": 1, "realtime": true, "recurring": "monthly"}}', 'Pricing tiers for in-app purchases')
ON CONFLICT (key) DO NOTHING;

-- Create subscriptions table for tracking purchases
CREATE TABLE IF NOT EXISTS subscriptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tier TEXT NOT NULL, -- 'basic', 'premium', 'realtime_only', 'realtime_monthly'
  purchase_type TEXT NOT NULL DEFAULT 'one_time', -- 'one_time', 'subscription'
  status TEXT NOT NULL DEFAULT 'active', -- 'active', 'expired', 'cancelled'
  purchase_token TEXT, -- App Store/Google Play receipt
  original_transaction_id TEXT, -- For subscription renewals
  expires_at TIMESTAMPTZ,
  auto_renew BOOLEAN NOT NULL DEFAULT FALSE,
  features JSONB NOT NULL DEFAULT '{}', -- {"realtime": true, "children": 3}
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Function to check if parent can add more children
CREATE OR REPLACE FUNCTION can_add_child(parent_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  current_children_count INT;
  family_child_limit INT;
  has_unlimited BOOLEAN;
  monetization_enabled BOOLEAN;
BEGIN
  -- Check if monetization is enabled globally
  SELECT (value::TEXT)::BOOLEAN INTO monetization_enabled
  FROM app_config 
  WHERE key = 'monetization_enabled';
  
  -- If monetization is disabled, allow unlimited children
  IF NOT monetization_enabled THEN
    RETURN TRUE;
  END IF;

  -- Get current children count
  SELECT COUNT(*) INTO current_children_count
  FROM children 
  WHERE children.parent_id = can_add_child.parent_id;

  -- Get family's child limit and unlimited status
  SELECT child_limit, has_unlimited_children INTO family_child_limit, has_unlimited
  FROM family_settings 
  WHERE family_settings.parent_id = can_add_child.parent_id;

  -- If no family settings exist, use default limits
  IF family_child_limit IS NULL THEN
    SELECT (value::TEXT)::INT INTO family_child_limit
    FROM app_config 
    WHERE key = 'free_child_limit';
  END IF;

  -- Return true if unlimited or under limit
  RETURN (has_unlimited OR current_children_count < family_child_limit);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if real-time sync is enabled for family
CREATE OR REPLACE FUNCTION has_realtime_access(parent_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  global_realtime_enabled BOOLEAN;
  free_realtime_enabled BOOLEAN;
  family_realtime_enabled BOOLEAN;
  realtime_expires_at TIMESTAMPTZ;
BEGIN
  -- Check if real-time sync monetization is globally enabled
  SELECT (value::TEXT)::BOOLEAN INTO global_realtime_enabled
  FROM app_config 
  WHERE key = 'realtime_sync_enabled';
  
  -- If globally disabled, everyone gets real-time
  IF NOT global_realtime_enabled THEN
    RETURN TRUE;
  END IF;

  -- Check if free tier includes real-time
  SELECT (value::TEXT)::BOOLEAN INTO free_realtime_enabled
  FROM app_config 
  WHERE key = 'free_realtime_enabled';

  -- Get family's real-time settings
  SELECT realtime_sync_enabled, realtime_subscription_expires_at 
  INTO family_realtime_enabled, realtime_expires_at
  FROM family_settings 
  WHERE family_settings.parent_id = has_realtime_access.parent_id;

  -- If no family settings exist, use free tier default
  IF family_realtime_enabled IS NULL THEN
    RETURN free_realtime_enabled;
  END IF;

  -- Check if subscription is still valid
  IF family_realtime_enabled AND realtime_expires_at IS NOT NULL THEN
    RETURN realtime_expires_at > NOW();
  END IF;

  -- Return family setting (for one-time purchases or free tier)
  RETURN family_realtime_enabled OR free_realtime_enabled;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to upgrade family plan with real-time sync support
CREATE OR REPLACE FUNCTION upgrade_family_plan(
  parent_id UUID,
  tier TEXT,
  purchase_token TEXT DEFAULT NULL,
  original_transaction_id TEXT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  new_child_limit INT;
  realtime_enabled BOOLEAN := FALSE;
  purchase_type TEXT := 'one_time';
  expires_at TIMESTAMPTZ;
  features JSONB;
  result JSONB;
BEGIN
  -- Get tier configuration
  IF tier = 'basic' THEN
    new_child_limit := 3;
    realtime_enabled := FALSE;
  ELSIF tier = 'premium' THEN
    new_child_limit := 10;
    realtime_enabled := TRUE;
  ELSIF tier = 'realtime_only' THEN
    new_child_limit := 1; -- Keep current limit, just add real-time
    realtime_enabled := TRUE;
  ELSIF tier = 'realtime_monthly' THEN
    new_child_limit := 1;
    realtime_enabled := TRUE;
    purchase_type := 'subscription';
    expires_at := NOW() + INTERVAL '1 month';
  ELSE
    RAISE EXCEPTION 'Invalid tier: %', tier;
  END IF;

  -- Build features object
  features := jsonb_build_object(
    'realtime', realtime_enabled,
    'children', new_child_limit
  );

  -- Update family settings
  INSERT INTO family_settings (
    parent_id, 
    child_limit, 
    has_unlimited_children, 
    realtime_sync_enabled, 
    realtime_subscription_expires_at
  )
  VALUES (
    parent_id, 
    new_child_limit, 
    (tier = 'premium'), 
    realtime_enabled,
    expires_at
  )
  ON CONFLICT (parent_id) DO UPDATE SET
    child_limit = CASE 
      WHEN tier IN ('realtime_only', 'realtime_monthly') THEN family_settings.child_limit
      ELSE new_child_limit
    END,
    has_unlimited_children = (tier = 'premium'),
    realtime_sync_enabled = realtime_enabled,
    realtime_subscription_expires_at = expires_at,
    updated_at = NOW();

  -- Record subscription
  INSERT INTO subscriptions (
    parent_id, 
    tier, 
    purchase_type,
    purchase_token, 
    original_transaction_id,
    expires_at,
    auto_renew,
    features
  )
  VALUES (
    parent_id, 
    tier, 
    purchase_type,
    purchase_token, 
    original_transaction_id,
    expires_at,
    (purchase_type = 'subscription'),
    features
  );

  -- Return success result
  SELECT jsonb_build_object(
    'success', true,
    'tier', tier,
    'purchase_type', purchase_type,
    'child_limit', new_child_limit,
    'has_unlimited_children', (tier = 'premium'),
    'realtime_enabled', realtime_enabled,
    'expires_at', expires_at,
    'features', features
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add RLS policies for new tables
ALTER TABLE app_config ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;

-- App config is read-only for all authenticated users
CREATE POLICY "Anyone can read app config" ON app_config FOR SELECT USING (auth.uid() IS NOT NULL);

-- Subscriptions can only be viewed by the parent who owns them
CREATE POLICY "Parents can view own subscriptions" ON subscriptions FOR ALL USING (auth.uid() = parent_id);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_family_settings_child_limit ON family_settings(child_limit);
CREATE INDEX IF NOT EXISTS idx_subscriptions_parent_id ON subscriptions(parent_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_app_config_key ON app_config(key); 