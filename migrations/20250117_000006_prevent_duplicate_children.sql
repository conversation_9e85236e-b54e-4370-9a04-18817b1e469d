-- Prevent Duplicate Children Migration
-- Created: 2025-01-17
-- Description: Adds unique constraint to prevent duplicate children per parent

-- Step 1: Remove existing duplicate children, keeping the first one created
DELETE FROM children 
WHERE id IN (
  SELECT id 
  FROM (
    SELECT id, 
           ROW_NUMBER() OVER (PARTITION BY parent_id, name ORDER BY created_at ASC) as rn
    FROM children
  ) ranked
  WHERE rn > 1
);

-- Step 2: Add unique constraint to prevent duplicate children names per parent
ALTER TABLE children 
ADD CONSTRAINT unique_child_name_per_parent UNIQUE (parent_id, name);

-- Step 3: Create index for better performance on parent_id + name lookups
CREATE INDEX idx_children_parent_name ON children(parent_id, name); 