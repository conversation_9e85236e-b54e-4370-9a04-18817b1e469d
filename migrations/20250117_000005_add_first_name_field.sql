-- Add First Name Field Migration
-- Created: 2025-01-17
-- Description: Adds first_name field to users table for better personalization

-- Add first_name field to users table
ALTER TABLE users 
ADD COLUMN first_name TEXT;

-- Create an index for faster first name queries (useful for personalization)
CREATE INDEX idx_users_first_name ON users(first_name);

-- Update existing users to extract first name from full_name if available
UPDATE users 
SET first_name = SPLIT_PART(full_name, ' ', 1)
WHERE full_name IS NOT NULL AND first_name IS NULL; 