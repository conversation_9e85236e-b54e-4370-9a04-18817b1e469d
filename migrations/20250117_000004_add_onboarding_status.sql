-- Add Onboarding Status Migration
-- Created: 2025-01-17
-- Description: Adds onboarding completion tracking to users table

-- Add onboarding_completed field to users table
ALTER TABLE users 
ADD COLUMN onboarding_completed BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN onboarding_completed_at TIMESTAMPTZ;

-- Add family_name field to users table (from onboarding data)
ALTER TABLE users 
ADD COLUMN family_name TEXT;

-- Create index for faster onboarding status queries
CREATE INDEX idx_users_onboarding_completed ON users(onboarding_completed);

-- Update existing users to have onboarding_completed = false by default
-- (This is already the default, but being explicit)
UPDATE users SET onboarding_completed = false WHERE onboarding_completed IS NULL; 