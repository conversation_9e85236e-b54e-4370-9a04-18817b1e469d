-- KidsCoin Initial Schema Migration
-- Created: 2025-01-17
-- Description: Creates all core tables for the KidsCoin application

-- Enable UUID extension (may already be enabled)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users (Parents) - extends auth.users
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Family-level settings managed by the parent
CREATE TABLE family_settings (
  parent_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  task_verification_mode TEXT NOT NULL DEFAULT 'strict' CHECK (task_verification_mode IN ('strict', 'trusting')),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Children linked to a parent user
CREATE TABLE children (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  age INT,
  avatar TEXT,
  coin_balance INT NOT NULL DEFAULT 0,
  level INT NOT NULL DEFAULT 1,
  xp INT NOT NULL DEFAULT 0,
  daily_streak INT NOT NULL DEFAULT 0,
  last_activity_date DATE,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Task Templates created by parents
CREATE TABLE task_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  value INT NOT NULL, -- coin value
  category TEXT,
  frequency TEXT, -- e.g., 'Daily', 'Weekly'
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Assigned Tasks (instances of templates or custom tasks)
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  child_id UUID NOT NULL REFERENCES children(id) ON DELETE CASCADE,
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  value INT NOT NULL, -- coin value
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'pending_approval', 'completed')),
  is_recurring BOOLEAN NOT NULL DEFAULT FALSE,
  template_id UUID REFERENCES task_templates(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Rewards available in the "Shop"
CREATE TABLE rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  cost INT NOT NULL, -- coin cost
  category TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- A record of each reward unlocked by a child
CREATE TABLE unlocked_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  child_id UUID NOT NULL REFERENCES children(id) ON DELETE CASCADE,
  reward_id UUID NOT NULL REFERENCES rewards(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Short-lived codes for in-person task verification
CREATE TABLE task_completion_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  code TEXT NOT NULL, -- A short, easy-to-read code (e.g., 4-digit number)
  expires_at TIMESTAMPTZ NOT NULL DEFAULT NOW() + INTERVAL '5 minutes',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Achievements system
CREATE TABLE achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  icon TEXT,
  criteria_type TEXT NOT NULL, -- 'tasks_completed', 'coins_earned', 'streak_days', etc.
  criteria_value INT NOT NULL, -- threshold value
  coin_reward INT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Track which achievements each child has unlocked
CREATE TABLE child_achievements (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  child_id UUID NOT NULL REFERENCES children(id) ON DELETE CASCADE,
  achievement_id UUID NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  UNIQUE(child_id, achievement_id)
);

-- Add some useful indexes for performance
CREATE INDEX idx_children_parent_id ON children(parent_id);
CREATE INDEX idx_tasks_child_id ON tasks(child_id);
CREATE INDEX idx_tasks_parent_id ON tasks(parent_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_task_templates_parent_id ON task_templates(parent_id);
CREATE INDEX idx_rewards_parent_id ON rewards(parent_id);
CREATE INDEX idx_unlocked_rewards_child_id ON unlocked_rewards(child_id);
CREATE INDEX idx_child_achievements_child_id ON child_achievements(child_id);
CREATE INDEX idx_task_completion_codes_task_id ON task_completion_codes(task_id);
CREATE INDEX idx_task_completion_codes_expires_at ON task_completion_codes(expires_at);

-- Add Row Level Security (RLS) policies for security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE family_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE children ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE unlocked_rewards ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_completion_codes ENABLE ROW LEVEL SECURITY;
ALTER TABLE achievements ENABLE ROW LEVEL SECURITY;
ALTER TABLE child_achievements ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (parents can only see their own data)
CREATE POLICY "Users can view own profile" ON users FOR ALL USING (auth.uid() = id);

CREATE POLICY "Parents can manage own settings" ON family_settings FOR ALL USING (auth.uid() = parent_id);

CREATE POLICY "Parents can manage own children" ON children FOR ALL USING (auth.uid() = parent_id);

CREATE POLICY "Parents can manage own task templates" ON task_templates FOR ALL USING (auth.uid() = parent_id);

CREATE POLICY "Parents can manage own tasks" ON tasks FOR ALL USING (auth.uid() = parent_id);

CREATE POLICY "Parents can manage own rewards" ON rewards FOR ALL USING (auth.uid() = parent_id);

CREATE POLICY "Parents can view unlocked rewards for their children" ON unlocked_rewards FOR ALL USING (
  EXISTS (
    SELECT 1 FROM children 
    WHERE children.id = unlocked_rewards.child_id 
    AND children.parent_id = auth.uid()
  )
);

CREATE POLICY "Parents can manage completion codes for their tasks" ON task_completion_codes FOR ALL USING (
  EXISTS (
    SELECT 1 FROM tasks 
    WHERE tasks.id = task_completion_codes.task_id 
    AND tasks.parent_id = auth.uid()
  )
);

-- Achievements are global (read-only for now)
CREATE POLICY "Anyone can read achievements" ON achievements FOR SELECT USING (true);

CREATE POLICY "Parents can view their children's achievements" ON child_achievements FOR ALL USING (
  EXISTS (
    SELECT 1 FROM children 
    WHERE children.id = child_achievements.child_id 
    AND children.parent_id = auth.uid()
  )
); 