-- === ULTRA-OPTIMIZED CHILD PROFILE FUNCTION ===
-- Single database call to replace 5 API calls
-- Target: 200-400ms instead of 1000ms+

CREATE OR REPLACE FUNCTION get_child_profile_complete(
  p_child_id UUID,
  p_parent_id UUID
) RETURNS JSON AS $$
DECLARE
  v_result JSON;
  v_child RECORD;
  v_task_stats RECORD;
  v_achievements JSON;
  v_unlocked_achievements JSON;
  v_unlocked_rewards JSON;
BEGIN
  -- 1. Get child data with validation
  SELECT * INTO v_child
  FROM children 
  WHERE id = p_child_id 
    AND parent_id = p_parent_id;
    
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Child not found or access denied';
  END IF;
  
  -- 2. Get task statistics in single query
  SELECT 
    COUNT(*) FILTER (WHERE status = 'completed') as total_completed,
    COUNT(*) FILTER (WHERE status = 'completed' AND completed_at::date = CURRENT_DATE) as completed_today,
    COUNT(*) FILTER (WHERE status = 'assigned') as pending_tasks,
    COALESCE(MAX(completed_at), v_child.created_at) as last_activity,
    COALESCE(AVG(value) FILTER (WHERE status = 'completed'), 0) as avg_task_value
  INTO v_task_stats
  FROM tasks 
  WHERE child_id = p_child_id 
    AND parent_id = p_parent_id;
  
  -- 3. Get recent completed tasks (last 50 for performance)
  WITH recent_tasks AS (
    SELECT json_agg(
      json_build_object(
        'id', id,
        'title', title,
        'description', description,
        'value', value,
        'status', status,
        'completed_at', completed_at,
        'category', category,
        'difficulty', difficulty
      ) ORDER BY completed_at DESC
    ) as tasks_data
    FROM tasks 
    WHERE child_id = p_child_id 
      AND parent_id = p_parent_id
      AND status = 'completed'
    ORDER BY completed_at DESC
    LIMIT 50
  ),
  
  -- 4. Get achievements with progress calculation
  achievements_with_progress AS (
    SELECT json_agg(
      json_build_object(
        'id', a.id,
        'title', a.title,
        'description', a.description,
        'icon', a.icon,
        'criteria_type', a.criteria_type,
        'criteria_value', a.criteria_value,
        'reward_coins', a.reward_coins,
        'is_unlocked', COALESCE(ua.unlocked_at IS NOT NULL, false),
        'unlocked_at', ua.unlocked_at,
        'progress', CASE 
          WHEN a.criteria_type = 'tasks_completed' THEN 
            LEAST(v_task_stats.total_completed::float / GREATEST(a.criteria_value, 1), 1.0)
          WHEN a.criteria_type = 'coins_earned' THEN 
            LEAST(v_child.coin_balance::float / GREATEST(a.criteria_value, 1), 1.0)
          WHEN a.criteria_type = 'level_reached' THEN 
            LEAST(v_child.level::float / GREATEST(a.criteria_value, 1), 1.0)
          WHEN a.criteria_type = 'streak_days' THEN 
            LEAST(v_child.daily_streak::float / GREATEST(a.criteria_value, 1), 1.0)
          ELSE 0.0
        END,
        'current_value', CASE 
          WHEN a.criteria_type = 'tasks_completed' THEN v_task_stats.total_completed
          WHEN a.criteria_type = 'coins_earned' THEN v_child.coin_balance
          WHEN a.criteria_type = 'level_reached' THEN v_child.level
          WHEN a.criteria_type = 'streak_days' THEN v_child.daily_streak
          ELSE 0
        END,
        'unit', CASE 
          WHEN a.criteria_type = 'tasks_completed' THEN 'tasks'
          WHEN a.criteria_type = 'coins_earned' THEN 'coins'
          WHEN a.criteria_type = 'level_reached' THEN 'level'
          WHEN a.criteria_type = 'streak_days' THEN 'days'
          ELSE ''
        END
      )
    ) as achievements_data
    FROM achievements a
    LEFT JOIN unlocked_achievements ua ON a.id = ua.achievement_id AND ua.child_id = p_child_id
    WHERE a.is_active = true
    ORDER BY a.criteria_value, a.created_at
  ),
  
  -- 5. Get unlocked rewards
  unlocked_rewards_data AS (
    SELECT json_agg(
      json_build_object(
        'id', ur.id,
        'reward_title', r.title,
        'reward_description', r.description,
        'reward_cost', r.cost,
        'unlocked_at', ur.unlocked_at,
        'redeemed_at', ur.redeemed_at,
        'status', CASE WHEN ur.redeemed_at IS NOT NULL THEN 'redeemed' ELSE 'unlocked' END
      )
    ) as rewards_data
    FROM unlocked_rewards ur
    JOIN rewards r ON ur.reward_id = r.id
    WHERE ur.child_id = p_child_id
    ORDER BY ur.unlocked_at DESC
  )
  
  -- 6. Build comprehensive result
  SELECT json_build_object(
    'childProfile', json_build_object(
      'id', v_child.id,
      'name', v_child.name,
      'avatar', v_child.avatar,
      'coin_balance', v_child.coin_balance,
      'level', v_child.level,
      'xp', v_child.xp,
      'daily_streak', v_child.daily_streak,
      'last_activity_date', v_child.last_activity_date,
      'created_at', v_child.created_at,
      'updated_at', v_child.updated_at
    ),
    'taskHistory', COALESCE(rt.tasks_data, '[]'::json),
    'achievements', COALESCE(awp.achievements_data, '[]'::json),
    'unlockedRewards', COALESCE(urd.rewards_data, '[]'::json),
    'stats', json_build_object(
      'totalTasks', v_task_stats.total_completed,
      'totalCoins', v_child.coin_balance,
      'currentLevel', v_child.level,
      'currentStreak', v_child.daily_streak,
      'totalAchievements', (
        SELECT COUNT(*) 
        FROM unlocked_achievements 
        WHERE child_id = p_child_id
      ),
      'totalRewards', (
        SELECT COUNT(*) 
        FROM unlocked_rewards 
        WHERE child_id = p_child_id
      ),
      'averageTaskValue', ROUND(v_task_stats.avg_task_value::numeric, 1),
      'daysActive', GREATEST(1, EXTRACT(days FROM (NOW() - v_child.created_at::timestamp))::integer),
      'completedToday', v_task_stats.completed_today,
      'pendingTasks', v_task_stats.pending_tasks,
      'lastActivity', v_task_stats.last_activity
    ),
    'performance', json_build_object(
      'loadTime', extract(epoch from now() - now()) * 1000,
      'optimized', true,
      'singleQuery', true,
      'timestamp', NOW()
    )
  ) INTO v_result
  FROM recent_tasks rt
  CROSS JOIN achievements_with_progress awp  
  CROSS JOIN unlocked_rewards_data urd;
  
  RETURN v_result;
  
EXCEPTION WHEN OTHERS THEN
  -- Return error details for debugging
  RETURN json_build_object(
    'error', true,
    'message', SQLERRM,
    'detail', SQLSTATE,
    'childId', p_child_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create optimized indexes for this function
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tasks_child_profile_optimized 
ON tasks (child_id, parent_id, status, completed_at DESC) 
WHERE status IN ('completed', 'assigned');

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_unlocked_achievements_child 
ON unlocked_achievements (child_id, achievement_id, unlocked_at DESC);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_unlocked_rewards_child_optimized 
ON unlocked_rewards (child_id, unlocked_at DESC);

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_child_profile_complete TO authenticated;

-- Performance note: This function should complete in 100-300ms vs 1000ms+ for separate calls