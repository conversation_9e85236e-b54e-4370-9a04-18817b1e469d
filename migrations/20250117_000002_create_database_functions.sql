-- Database Functions for KidsCoin Edge Functions
-- Created: 2025-01-17
-- Description: Creates PostgreSQL functions for complex operations

-- Function to atomically complete a task and award coins
CREATE OR REPLACE FUNCTION complete_task(
  task_id UUID,
  award_coins BOOLEAN DEFAULT TRUE
) RETURNS JSONB AS $$
DECLARE
  task_record RECORD;
  child_record RECORD;
  result JSONB;
BEGIN
  -- Get the task details
  SELECT t.*, c.coin_balance, c.xp, c.level 
  INTO task_record
  FROM tasks t
  JOIN children c ON c.id = t.child_id
  WHERE t.id = task_id AND t.status = 'pending';

  -- Check if task exists and is pending
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Task not found or not pending';
  END IF;

  -- Update task status
  UPDATE tasks 
  SET 
    status = 'completed',
    completed_at = NOW(),
    updated_at = NOW()
  WHERE id = task_id;

  -- Award coins and XP if specified
  IF award_coins THEN
    UPDATE children 
    SET 
      coin_balance = coin_balance + task_record.value,
      xp = xp + task_record.value, -- XP equals coin value for now
      updated_at = NOW()
    WHERE id = task_record.child_id;

    -- Check for level up (every 100 XP = 1 level)
    UPDATE children 
    SET level = (xp / 100) + 1
    WHERE id = task_record.child_id 
    AND (xp / 100) + 1 > level;
  END IF;

  -- Update daily streak if this is first task today
  UPDATE children 
  SET 
    daily_streak = CASE 
      WHEN last_activity_date = CURRENT_DATE THEN daily_streak
      WHEN last_activity_date = CURRENT_DATE - INTERVAL '1 day' THEN daily_streak + 1
      ELSE 1 -- Reset streak if gap > 1 day
    END,
    last_activity_date = CURRENT_DATE,
    updated_at = NOW()
  WHERE id = task_record.child_id;

  -- Return the updated task
  SELECT to_jsonb(t.*) INTO result
  FROM tasks t 
  WHERE t.id = task_id;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to generate a verification code for a task
CREATE OR REPLACE FUNCTION generate_verification_code(
  task_id UUID
) RETURNS TEXT AS $$
DECLARE
  code TEXT;
BEGIN
  -- Generate a 4-digit code
  code := LPAD((RANDOM() * 9999)::INT::TEXT, 4, '0');
  
  -- Delete any existing codes for this task
  DELETE FROM task_completion_codes WHERE task_id = generate_verification_code.task_id;
  
  -- Insert the new code
  INSERT INTO task_completion_codes (task_id, code)
  VALUES (generate_verification_code.task_id, code);
  
  RETURN code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to verify and use a completion code
CREATE OR REPLACE FUNCTION verify_completion_code(
  task_id UUID,
  submitted_code TEXT
) RETURNS BOOLEAN AS $$
DECLARE
  valid_code BOOLEAN := FALSE;
BEGIN
  -- Check if code exists and is not expired
  SELECT EXISTS(
    SELECT 1 FROM task_completion_codes 
    WHERE task_id = verify_completion_code.task_id 
    AND code = submitted_code 
    AND expires_at > NOW()
  ) INTO valid_code;
  
  -- If valid, delete the code (single use)
  IF valid_code THEN
    DELETE FROM task_completion_codes 
    WHERE task_id = verify_completion_code.task_id 
    AND code = submitted_code;
  END IF;
  
  RETURN valid_code;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and unlock achievements for a child
CREATE OR REPLACE FUNCTION check_achievement_progress(
  child_id UUID
) RETURNS JSONB AS $$
DECLARE
  child_stats RECORD;
  achievement RECORD;
  unlocked_achievements JSONB := '[]'::JSONB;
BEGIN
  -- Get child's current stats
  SELECT 
    c.*,
    COUNT(t.id) AS total_tasks_completed,
    COALESCE(SUM(t.value), 0) AS total_coins_earned
  INTO child_stats
  FROM children c
  LEFT JOIN tasks t ON t.child_id = c.id AND t.status = 'completed'
  WHERE c.id = check_achievement_progress.child_id
  GROUP BY c.id;

  -- Check each achievement
  FOR achievement IN 
    SELECT a.* FROM achievements a
    WHERE a.id NOT IN (
      SELECT ca.achievement_id 
      FROM child_achievements ca 
      WHERE ca.child_id = check_achievement_progress.child_id
    )
  LOOP
    -- Check if criteria is met
    IF (achievement.criteria_type = 'tasks_completed' AND child_stats.total_tasks_completed >= achievement.criteria_value) OR
       (achievement.criteria_type = 'coins_earned' AND child_stats.total_coins_earned >= achievement.criteria_value) OR
       (achievement.criteria_type = 'streak_days' AND child_stats.daily_streak >= achievement.criteria_value) OR
       (achievement.criteria_type = 'level_reached' AND child_stats.level >= achievement.criteria_value) THEN
      
      -- Unlock the achievement
      INSERT INTO child_achievements (child_id, achievement_id)
      VALUES (check_achievement_progress.child_id, achievement.id);
      
      -- Award bonus coins if any
      IF achievement.coin_reward > 0 THEN
        UPDATE children 
        SET coin_balance = coin_balance + achievement.coin_reward
        WHERE id = check_achievement_progress.child_id;
      END IF;
      
      -- Add to result
      unlocked_achievements := unlocked_achievements || to_jsonb(achievement);
    END IF;
  END LOOP;

  RETURN unlocked_achievements;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to clean up expired verification codes (should be run periodically)
CREATE OR REPLACE FUNCTION cleanup_expired_codes() RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM task_completion_codes WHERE expires_at < NOW();
  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER; 