-- Remove Legacy Monetization System
-- Created: 2025-01-17
-- Description: Removes legacy app_config and subscriptions tables, replacing with unified system

-- Drop legacy functions that reference app_config
DROP FUNCTION IF EXISTS can_add_child(UUID);
DROP FUNCTION IF EXISTS has_realtime_access(UUID);
DROP FUNCTION IF EXISTS upgrade_family_plan(UUID, TEXT, TEXT, TEXT);

-- Drop legacy tables
DROP TABLE IF EXISTS subscriptions CASCADE;
DROP TABLE IF EXISTS app_config CASCADE;

-- Remove legacy indexes
DROP INDEX IF EXISTS idx_subscriptions_parent_id;
DROP INDEX IF EXISTS idx_subscriptions_status;
DROP INDEX IF EXISTS idx_subscriptions_purchase_type;
DROP INDEX IF EXISTS idx_subscriptions_expires_at;
DROP INDEX IF EXISTS idx_app_config_key;

-- Remove legacy columns from family_settings (keep only unified system columns)
ALTER TABLE family_settings DROP COLUMN IF EXISTS child_limit;
ALTER TABLE family_settings DROP COLUMN IF EXISTS has_unlimited_children;
ALTER TABLE family_settings DROP COLUMN IF EXISTS realtime_sync_enabled;
ALTER TABLE family_settings DROP COLUMN IF EXISTS realtime_subscription_expires_at;

-- Keep only the parent_pin column as it's still useful
-- ALTER TABLE family_settings DROP COLUMN IF EXISTS parent_pin; 