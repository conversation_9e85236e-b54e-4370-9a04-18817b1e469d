-- Complete Database Updates for KidsCoin Monetization Features
-- This script contains all the remaining updates needed to complete the database setup

-- 1. Add missing columns to family_settings
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS parent_pin TEXT NOT NULL DEFAULT '1234';
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS realtime_sync_enabled BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS realtime_subscription_expires_at TIMESTAMPTZ;

-- 2. Add missing columns to subscriptions table
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS purchase_type TEXT NOT NULL DEFAULT 'one_time';
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS original_transaction_id TEXT;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS auto_renew BOOLEAN NOT NULL DEFAULT FALSE;
ALTER TABLE subscriptions ADD COLUMN IF NOT EXISTS features <PERSON><PERSON>N<PERSON> NOT NULL DEFAULT '{}';

-- 3. Insert missing app config entries
INSERT INTO app_config (key, value, description) VALUES
('realtime_sync_enabled', 'true', 'Whether real-time sync monetization is enabled'),
('free_realtime_enabled', 'false', 'Whether free tier includes real-time sync')
ON CONFLICT (key) DO NOTHING;

-- 4. Update pricing tiers to include real-time options
UPDATE app_config SET 
  value = '{"basic": {"price": 4.99, "children": 3, "realtime": false}, "premium": {"price": 9.99, "children": 10, "realtime": true}, "realtime_only": {"price": 2.99, "children": 1, "realtime": true}, "realtime_monthly": {"price": 0.99, "children": 1, "realtime": true, "recurring": "monthly"}}',
  updated_at = NOW()
WHERE key = 'pricing_tiers';

-- 5. Create function to check if real-time sync is enabled for family
CREATE OR REPLACE FUNCTION has_realtime_access(parent_id UUID) RETURNS BOOLEAN AS $$
DECLARE
  global_realtime_enabled BOOLEAN;
  free_realtime_enabled BOOLEAN;
  family_realtime_enabled BOOLEAN;
  realtime_expires_at TIMESTAMPTZ;
BEGIN
  -- Check if real-time sync monetization is globally enabled
  SELECT (value::TEXT)::BOOLEAN INTO global_realtime_enabled
  FROM app_config 
  WHERE key = 'realtime_sync_enabled';
  
  -- If globally disabled, everyone gets real-time
  IF NOT global_realtime_enabled THEN
    RETURN TRUE;
  END IF;

  -- Check if free tier includes real-time
  SELECT (value::TEXT)::BOOLEAN INTO free_realtime_enabled
  FROM app_config 
  WHERE key = 'free_realtime_enabled';

  -- Get family's real-time settings
  SELECT realtime_sync_enabled, realtime_subscription_expires_at 
  INTO family_realtime_enabled, realtime_expires_at
  FROM family_settings 
  WHERE family_settings.parent_id = has_realtime_access.parent_id;

  -- If no family settings exist, use free tier default
  IF family_realtime_enabled IS NULL THEN
    RETURN COALESCE(free_realtime_enabled, false);
  END IF;

  -- Check if subscription is still valid
  IF family_realtime_enabled AND realtime_expires_at IS NOT NULL THEN
    RETURN realtime_expires_at > NOW();
  END IF;

  -- Return family setting (for one-time purchases or free tier)
  RETURN family_realtime_enabled OR COALESCE(free_realtime_enabled, false);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Update upgrade_family_plan function to support real-time sync
CREATE OR REPLACE FUNCTION upgrade_family_plan(
  parent_id UUID,
  tier TEXT,
  purchase_token TEXT DEFAULT NULL,
  original_transaction_id TEXT DEFAULT NULL
) RETURNS JSONB AS $$
DECLARE
  new_child_limit INT;
  realtime_enabled BOOLEAN := FALSE;
  purchase_type TEXT := 'one_time';
  expires_at TIMESTAMPTZ;
  features JSONB;
  result JSONB;
BEGIN
  -- Get tier configuration
  IF tier = 'basic' THEN
    new_child_limit := 3;
    realtime_enabled := FALSE;
  ELSIF tier = 'premium' THEN
    new_child_limit := 10;
    realtime_enabled := TRUE;
  ELSIF tier = 'realtime_only' THEN
    new_child_limit := 1; -- Keep current limit, just add real-time
    realtime_enabled := TRUE;
  ELSIF tier = 'realtime_monthly' THEN
    new_child_limit := 1;
    realtime_enabled := TRUE;
    purchase_type := 'subscription';
    expires_at := NOW() + INTERVAL '1 month';
  ELSE
    RAISE EXCEPTION 'Invalid tier: %', tier;
  END IF;

  -- Build features object
  features := jsonb_build_object(
    'realtime', realtime_enabled,
    'children', new_child_limit
  );

  -- Update family settings
  INSERT INTO family_settings (
    parent_id, 
    child_limit, 
    has_unlimited_children, 
    realtime_sync_enabled, 
    realtime_subscription_expires_at
  )
  VALUES (
    parent_id, 
    new_child_limit, 
    (tier = 'premium'), 
    realtime_enabled,
    expires_at
  )
  ON CONFLICT (parent_id) DO UPDATE SET
    child_limit = CASE 
      WHEN tier IN ('realtime_only', 'realtime_monthly') THEN family_settings.child_limit
      ELSE new_child_limit
    END,
    has_unlimited_children = (tier = 'premium'),
    realtime_sync_enabled = realtime_enabled,
    realtime_subscription_expires_at = expires_at,
    updated_at = NOW();

  -- Record subscription
  INSERT INTO subscriptions (
    parent_id, 
    tier, 
    purchase_type,
    purchase_token, 
    original_transaction_id,
    expires_at,
    auto_renew,
    features
  )
  VALUES (
    parent_id, 
    tier, 
    purchase_type,
    purchase_token, 
    original_transaction_id,
    expires_at,
    (purchase_type = 'subscription'),
    features
  );

  -- Return success result
  SELECT jsonb_build_object(
    'success', true,
    'tier', tier,
    'purchase_type', purchase_type,
    'child_limit', new_child_limit,
    'has_unlimited_children', (tier = 'premium'),
    'realtime_enabled', realtime_enabled,
    'expires_at', expires_at,
    'features', features
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 7. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_family_settings_realtime ON family_settings(realtime_sync_enabled);
CREATE INDEX IF NOT EXISTS idx_subscriptions_purchase_type ON subscriptions(purchase_type);
CREATE INDEX IF NOT EXISTS idx_subscriptions_expires_at ON subscriptions(expires_at);

-- 8. Verify the updates were applied correctly
SELECT 'Database updates completed successfully' as status; 