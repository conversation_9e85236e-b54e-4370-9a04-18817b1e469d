import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

/**
 * UIStore - CLIENT STATE ONLY
 * 
 * This store manages UI-specific state that is NOT server data:
 * - Modal visibility states
 * - Form data (temporary user input)
 * - Loading states (UI feedback)
 * - Error states (UI feedback)
 * - Temporary UI state (refreshing, completing, etc.)
 * 
 * NEVER store server data here - that belongs in TanStack Query
 */

// Types
export interface ModalState {
  isOpen: boolean;
  data?: any; // UI data only, not server data
}

export type ModalId = 
  | 'taskCompletion'
  | 'parentPin'
  | 'childSelector'
  | 'rewardModal'
  | 'achievementModal'
  | 'settingsModal'
  | 'confirmationModal'
  | 'taskVerification'
  | 'parentModeAccess'
  | 'taskCreation'
  | 'taskAssignment'
  | 'rewardCreation'
  | 'rewardRedemption'
  | 'rewardManagement'
  | 'childProfile'
  | 'childProfileEditor'
  | 'childSettings'
  | 'childAnalytics'
  | 'taskTemplateEdit'
  | 'taskTemplateDelete'
  | 'avatarPicker'
  | 'aiGeneration'
  | 'celebrationModal'
  | 'unlockedModal'
  | 'codeModal';

export interface FormData {
  // Task-related forms (CLIENT STATE ONLY - temporary user input)
  taskVerificationCode: string;
  selectedTask: any; // UI reference only, not server data
  taskTitle: string;
  taskDescription: string;
  taskCoinValue: string;
  taskCategory: string;
  taskFrequency: string;
  
  // Child-related forms (CLIENT STATE ONLY - temporary user input)
  selectedChild: any; // UI reference only, not server data
  childName: string;
  childAvatar: string;
  
  // Reward-related forms (CLIENT STATE ONLY - temporary user input)
  selectedReward: any; // UI reference only, not server data
  rewardTitle: string;
  rewardDescription: string;
  rewardCoinCost: string;
  rewardCategory: string;
  
  // General forms (CLIENT STATE ONLY - temporary user input)
  pinCode: string;
  verificationCode: string;
  selectedChildren: string[];
  selectedTemplate: any; // UI reference only, not server data
  selectedPeriod: 'week' | 'month' | 'all';
  selectedTab: string;
  selectedAgeRange: string;
  selectedCategory: string;
  
  // AI Generation (CLIENT STATE ONLY - temporary user input)
  interests: string;
  difficulty: 'easy' | 'medium' | 'hard';
  budgetRange: { min: number; max: number };
}

interface UIState {
  // Modal management (CLIENT STATE ONLY)
  modals: Record<ModalId, ModalState>;
  
  // Form data (CLIENT STATE ONLY - temporary user input)
  forms: FormData;
  
  // Loading states (CLIENT STATE ONLY - UI feedback)
  loading: Record<string, boolean>;
  
  // Error states (CLIENT STATE ONLY - UI feedback)
  errors: Record<string, string | null>;
  
  // Temporary UI state (CLIENT STATE ONLY)
  refreshing: boolean;
  completing: Record<string, boolean>; // taskId -> isCompleting
  
  // Actions
  openModal: (modalId: ModalId, data?: any) => void;
  closeModal: (modalId: ModalId) => void;
  closeAllModals: () => void;
  
  setFormData: <K extends keyof FormData>(field: K, value: FormData[K]) => void;
  resetForm: () => void;
  
  setLoading: (key: string, loading: boolean) => void;
  setError: (key: string, error: string | null) => void;
  clearErrors: () => void;
  
  setRefreshing: (refreshing: boolean) => void;
  setCompleting: (taskId: string, completing: boolean) => void;
  
  reset: () => void;
}

const initialFormData: FormData = {
  // Task-related forms
  taskVerificationCode: '',
  selectedTask: null,
  taskTitle: '',
  taskDescription: '',
  taskCoinValue: '5',
  taskCategory: '',
  taskFrequency: 'Daily',
  
  // Child-related forms
  selectedChild: null,
  childName: '',
  childAvatar: '😊',
  
  // Reward-related forms
  selectedReward: null,
  rewardTitle: '',
  rewardDescription: '',
  rewardCoinCost: '25',
  rewardCategory: '',
  
  // General forms
  pinCode: '',
  verificationCode: '',
  selectedChildren: [],
  selectedTemplate: null,
  selectedPeriod: 'week',
  selectedTab: 'overview',
  selectedAgeRange: 'all',
  selectedCategory: 'all',
  
  // AI Generation
  interests: '',
  difficulty: 'medium',
  budgetRange: { min: 10, max: 100 },
};

const createInitialModals = (): Record<ModalId, ModalState> => ({
  taskCompletion: { isOpen: false },
  parentPin: { isOpen: false },
  childSelector: { isOpen: false },
  rewardModal: { isOpen: false },
  achievementModal: { isOpen: false },
  settingsModal: { isOpen: false },
  confirmationModal: { isOpen: false },
  taskVerification: { isOpen: false },
  parentModeAccess: { isOpen: false },
  taskCreation: { isOpen: false },
  taskAssignment: { isOpen: false },
  rewardCreation: { isOpen: false },
  rewardRedemption: { isOpen: false },
  rewardManagement: { isOpen: false },
  childProfile: { isOpen: false },
  childProfileEditor: { isOpen: false },
  childSettings: { isOpen: false },
  childAnalytics: { isOpen: false },
  taskTemplateEdit: { isOpen: false },
  taskTemplateDelete: { isOpen: false },
  avatarPicker: { isOpen: false },
  aiGeneration: { isOpen: false },
  celebrationModal: { isOpen: false },
  unlockedModal: { isOpen: false },
  codeModal: { isOpen: false },
});

const initialState = {
  modals: createInitialModals(),
  forms: initialFormData,
  loading: {},
  errors: {},
  refreshing: false,
  completing: {},
};

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Modal management
      openModal: (modalId, data) => {
        set((state) => ({
          modals: {
            ...state.modals,
            [modalId]: { isOpen: true, data }
          }
        }), false, `openModal:${modalId}`);
      },
      
      closeModal: (modalId) => {
        set((state) => ({
          modals: {
            ...state.modals,
            [modalId]: { isOpen: false, data: undefined }
          }
        }), false, `closeModal:${modalId}`);
      },
      
      closeAllModals: () => {
        set({ modals: createInitialModals() }, false, 'closeAllModals');
      },
      
      // Form management
      setFormData: (field, value) => {
        set((state) => ({
          forms: {
            ...state.forms,
            [field]: value
          }
        }), false, `setFormData:${field}`);
      },
      
      resetForm: () => {
        set({ forms: initialFormData }, false, 'resetForm');
      },
      
      // Loading management
      setLoading: (key, loading) => {
        set((state) => ({
          loading: {
            ...state.loading,
            [key]: loading
          }
        }), false, `setLoading:${key}`);
      },
      
      // Error management
      setError: (key, error) => {
        set((state) => ({
          errors: {
            ...state.errors,
            [key]: error
          }
        }), false, `setError:${key}`);
      },
      
      clearErrors: () => {
        set({ errors: {} }, false, 'clearErrors');
      },
      
      // UI state management
      setRefreshing: (refreshing) => {
        set({ refreshing }, false, 'setRefreshing');
      },
      
      setCompleting: (taskId, completing) => {
        set((state) => ({
          completing: {
            ...state.completing,
            [taskId]: completing
          }
        }), false, `setCompleting:${taskId}`);
      },
      
      // Reset
      reset: () => {
        set(initialState, false, 'reset');
      },
    }),
    {
      name: 'ui-store',
    }
  )
);

// Optimized selectors
export const useModal = (modalId: ModalId) => 
  useUIStore((state) => state.modals[modalId]);

// Individual selectors to prevent getSnapshot infinite loops
export const useOpenModal = () => useUIStore((state) => state.openModal);
export const useCloseModal = () => useUIStore((state) => state.closeModal);
export const useCloseAllModals = () => useUIStore((state) => state.closeAllModals);

// Individual form field selectors
export const useFormField = (field: keyof FormData) => 
  useUIStore((state) => state.forms[field]);

// Legacy form data selector (DEPRECATED - use useFormField above)
export const useFormData = () => 
  useUIStore((state) => state.forms);

export const useSetFormData = () => useUIStore((state) => state.setFormData);
export const useResetForm = () => useUIStore((state) => state.resetForm);

// Legacy object selectors (DEPRECATED - use individual selectors above)
export const useModalActions = () => 
  useUIStore((state) => ({
    openModal: state.openModal,
    closeModal: state.closeModal,
    closeAllModals: state.closeAllModals,
  }));

export const useFormActions = () => 
  useUIStore((state) => ({
    setFormData: state.setFormData,
    resetForm: state.resetForm,
  }));

export const useLoadingState = (key: string) => 
  useUIStore((state) => state.loading[key] || false);

export const useErrorState = (key: string) => 
  useUIStore((state) => state.errors[key] || null);

export const useCompletingState = (taskId: string) => 
  useUIStore((state) => state.completing[taskId] || false);