import { create } from 'zustand';

// Types
export type AppMode = 'parent' | 'kid';

interface AppState {
  // Core app state (CLIENT STATE ONLY)
  currentMode: AppMode;
  selectedChildId: string | null;
  
  // Tutorial state (CLIENT STATE ONLY)
  tutorialStep: number;
  tutorialCompleted: boolean;
  
  // Settings (CLIENT STATE ONLY)
  soundEnabled: boolean;
  hapticsEnabled: boolean;
  
  // Actions
  setMode: (mode: AppMode) => void;
  selectChild: (childId: string | null) => void;
  nextTutorialStep: () => void;
  completeTutorial: () => void;
  resetTutorial: () => void;
  toggleSound: () => void;
  toggleHaptics: () => void;
  reset: () => void;
}

const initialState = {
  currentMode: 'parent' as AppMode,
  selectedChildId: null,
  tutorialStep: 0,
  tutorialCompleted: false,
  soundEnabled: true,
  hapticsEnabled: true,
};

export const useAppStore = create<AppState>((set) => ({
  ...initialState,
  
  // Mode management
  setMode: (mode) => set({ currentMode: mode }),
  
  // Child selection (CLIENT STATE ONLY - just the ID)
  selectChild: (childId) => set({ selectedChildId: childId }),
  
  // Tutorial management
  nextTutorialStep: () => set((state) => ({ 
    tutorialStep: state.tutorialStep + 1 
  })),
  
  completeTutorial: () => set({ 
    tutorialCompleted: true,
    tutorialStep: 0 
  }),
  
  resetTutorial: () => set({ 
    tutorialCompleted: false,
    tutorialStep: 0 
  }),
  
  // Settings
  toggleSound: () => set((state) => ({ 
    soundEnabled: !state.soundEnabled 
  })),
  
  toggleHaptics: () => set((state) => ({ 
    hapticsEnabled: !state.hapticsEnabled 
  })),
  
  // Reset everything
  reset: () => set(initialState),
}));

// Selectors for optimized subscriptions
export const useCurrentMode = () => useAppStore((state) => state.currentMode);
export const useSelectedChildId = () => useAppStore((state) => state.selectedChildId);
export const useTutorialStep = () => useAppStore((state) => state.tutorialStep);
export const useTutorialCompleted = () => useAppStore((state) => state.tutorialCompleted);
export const useSoundEnabled = () => useAppStore((state) => state.soundEnabled);
export const useHapticsEnabled = () => useAppStore((state) => state.hapticsEnabled);