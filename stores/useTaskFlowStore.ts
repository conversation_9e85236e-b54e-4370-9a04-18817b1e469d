import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Types
export interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  child_id: string;
  created_at: string;
  due_date?: string;
}

export interface PendingApproval {
  id: string;
  taskId: string;
  childId: string;
  taskTitle: string;
  taskValue: number;
  childName: string;
  createdAt: string;
  expiresAt: string;
}

export type VerificationMode = 'strict' | 'trusting';

interface TaskFlowState {
  // Task completion flow (CLIENT STATE ONLY)
  completingTaskId: string | null;
  verificationMode: VerificationMode;
  currentTaskForVerification: Task | null;
  
  // Approval system (CLIENT STATE ONLY)
  pendingApprovals: PendingApproval[];
  
  // Task management (CLIENT STATE ONLY)
  selectedTasks: string[];
  bulkOperationMode: boolean;
  
  // Actions
  startTaskCompletion: (taskId: string) => void;
  completeTask: (taskId: string) => void;
  cancelTaskCompletion: () => void;
  
  setVerificationMode: (mode: VerificationMode) => void;
  setCurrentTaskForVerification: (task: Task | null) => void;
  
  // Approval management
  addPendingApproval: (approval: PendingApproval) => void;
  removePendingApproval: (approvalId: string) => void;
  clearExpiredApprovals: () => void;
  
  // Task selection
  toggleTaskSelection: (taskId: string) => void;
  selectAllTasks: (taskIds: string[]) => void;
  clearTaskSelection: () => void;
  setBulkOperationMode: (enabled: boolean) => void;
  
  // Utility
  reset: () => void;
}

const initialState = {
  completingTaskId: null,
  verificationMode: 'strict' as VerificationMode,
  currentTaskForVerification: null,
  pendingApprovals: [],
  selectedTasks: [],
  bulkOperationMode: false,
};

export const useTaskFlowStore = create<TaskFlowState>()(
  devtools(
    (set, get) => ({
      ...initialState,
      
      // Task completion flow
      startTaskCompletion: (taskId) => {
        set({ completingTaskId: taskId }, false, 'startTaskCompletion');
      },
      
      completeTask: (taskId) => {
        const { completingTaskId, selectedTasks } = get();
        
        // If this was the completing task, clear it
        if (completingTaskId === taskId) {
          set({ completingTaskId: null }, false, 'completeTask:clearCompleting');
        }
        
        // Remove from selected tasks if it was selected
        if (selectedTasks.includes(taskId)) {
          set((state) => ({
            selectedTasks: state.selectedTasks.filter(id => id !== taskId)
          }), false, 'completeTask:removeFromSelected');
        }
      },
      
      cancelTaskCompletion: () => {
        set({ 
          completingTaskId: null,
          currentTaskForVerification: null 
        }, false, 'cancelTaskCompletion');
      },
      
      // Verification
      setVerificationMode: (mode) => {
        set({ verificationMode: mode }, false, 'setVerificationMode');
      },
      
      setCurrentTaskForVerification: (task) => {
        set({ currentTaskForVerification: task }, false, 'setCurrentTaskForVerification');
      },
      
      // Approval management
      addPendingApproval: (approval) => {
        set((state) => ({
          pendingApprovals: [...state.pendingApprovals, approval]
        }), false, 'addPendingApproval');
      },
      
      removePendingApproval: (approvalId) => {
        set((state) => ({
          pendingApprovals: state.pendingApprovals.filter(a => a.id !== approvalId)
        }), false, 'removePendingApproval');
      },
      
      clearExpiredApprovals: () => {
        const now = new Date().toISOString();
        set((state) => ({
          pendingApprovals: state.pendingApprovals.filter(a => a.expiresAt > now)
        }), false, 'clearExpiredApprovals');
      },
      
      // Task selection
      toggleTaskSelection: (taskId) => {
        set((state) => {
          const isSelected = state.selectedTasks.includes(taskId);
          return {
            selectedTasks: isSelected
              ? state.selectedTasks.filter(id => id !== taskId)
              : [...state.selectedTasks, taskId]
          };
        }, false, 'toggleTaskSelection');
      },
      
      selectAllTasks: (taskIds) => {
        set({ selectedTasks: taskIds }, false, 'selectAllTasks');
      },
      
      clearTaskSelection: () => {
        set({ selectedTasks: [] }, false, 'clearTaskSelection');
      },
      
      setBulkOperationMode: (enabled) => {
        set({ 
          bulkOperationMode: enabled,
          selectedTasks: enabled ? [] : get().selectedTasks
        }, false, 'setBulkOperationMode');
      },
      
      // Reset
      reset: () => {
        set(initialState, false, 'reset');
      },
    }),
    {
      name: 'task-flow-store',
    }
  )
);

// Optimized selectors
export const useTaskCompletion = () => useTaskFlowStore((state) => ({
  completingTaskId: state.completingTaskId,
  currentTaskForVerification: state.currentTaskForVerification,
  verificationMode: state.verificationMode,
}));

export const useTaskCompletionActions = () => useTaskFlowStore((state) => ({
  startTaskCompletion: state.startTaskCompletion,
  completeTask: state.completeTask,
  cancelTaskCompletion: state.cancelTaskCompletion,
  setCurrentTaskForVerification: state.setCurrentTaskForVerification,
}));

export const usePendingApprovals = () => useTaskFlowStore((state) => state.pendingApprovals);

export const useTaskSelection = () => useTaskFlowStore((state) => ({
  selectedTasks: state.selectedTasks,
  bulkOperationMode: state.bulkOperationMode,
}));

export const useTaskSelectionActions = () => useTaskFlowStore((state) => ({
  toggleTaskSelection: state.toggleTaskSelection,
  selectAllTasks: state.selectAllTasks,
  clearTaskSelection: state.clearTaskSelection,
  setBulkOperationMode: state.setBulkOperationMode,
}));