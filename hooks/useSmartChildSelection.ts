/**
 * 🎯 SIMPLIFIED SMART CHILD SELECTION HOOK
 * 
 * Combines useChildren with simple state management
 */

import { useState } from 'react';
import { useChildren } from './useChildren';

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  parent_id: string;
  created_at: string;
}

export const useSmartChildSelection = () => {
  const { children, loading, error } = useChildren();
  const [selectedChildId, setSelectedChildId] = useState<string | null>(null);

  const selectChild = (childId: string) => {
    setSelectedChildId(childId);
  };

  const selectedChild = children.find(child => child.id === selectedChildId);

  // Auto-select first child if none selected
  if (children.length > 0 && !selectedChildId) {
    setSelectedChildId(children[0].id);
  }

  const hasChildren = children.length > 0;
  const hasMultipleChildren = children.length > 1;
  const loadingChildren = loading;

  const autoSelectFirstChild = () => {
    if (children.length > 0 && !selectedChildId) {
      setSelectedChildId(children[0].id);
    }
  };

  const openChildSelector = () => {
    // This would typically open a modal
    // For now, just auto-select first child
    autoSelectFirstChild();
  };

  return {
    error,
    children,
    loading,
    selectedChildId,
    selectedChild,
    selectChild,
    hasChildren,
    hasMultipleChildren,
    loadingChildren,
    autoSelectFirstChild,
    openChildSelector,
  };
};
