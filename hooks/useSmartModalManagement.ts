/**
 * 🎯 SIMPLIFIED SMART MODAL MANAGEMENT HOOK
 * 
 * Combines useUIStore with simple modal management
 */

import { useUIStore, ModalId } from '../stores/useUIStore';

export const useSmartModalManagement = () => {
  const { openModal, closeModal, modals } = useUIStore();

  const isModalOpen = (modalId: string) => {
    const modal = modals[modalId as ModalId];
    return modal?.isOpen || false;
  };

  const getModalData = (modalId: string) => {
    const modal = modals[modalId as ModalId];
    return modal?.data || null;
  };

  const showError = (message: string) => {
    openModal('confirmationModal' as ModalId, { error: message });
  };

  return {
    openModal,
    closeModal,
    isModalOpen,
    getModalData,
    showError,
  };
};
