/**
 * 🎯 SIMPLIFIED SMART REWARD REDEMPTION HOOK
 * 
 * Combines useRewards with modal management
 */

import { useRewards } from './useRewards';
import { useSmartModalManagement } from './useSmartModalManagement';
import { ModalId } from '../stores/useUIStore';

export const useSmartRewardRedemption = () => {
  const { rewards, redeemReward } = useRewards();
  const { openModal, closeModal, isModalOpen, getModalData } = useSmartModalManagement();

  const handleRedeemReward = async (rewardId: string, childId: string) => {
    try {
      await redeemReward(rewardId, childId);
      openModal('rewardRedemption' as ModalId, { success: true });
    } catch (error) {
      console.error('Error redeeming reward:', error);
      openModal('rewardRedemption' as ModalId, { error: error.message });
    }
  };

  const handleRedeemConfirm = async () => {
    const modalData = getModalData('rewardRedemption');
    if (modalData?.selectedReward && modalData?.childId) {
      await handleRedeemReward(modalData.selectedReward.id, modalData.childId);
    }
  };

  const showError = (message: string) => {
    openModal('rewardRedemption' as ModalId, { error: message });
  };

  return {
    rewards,
    handleRedeemReward,
    handleRedeemConfirm,
    showError,
    isModalOpen: (modalId: string) => isModalOpen(modalId),
    getModalData: (modalId: string) => getModalData(modalId),
  };
};
