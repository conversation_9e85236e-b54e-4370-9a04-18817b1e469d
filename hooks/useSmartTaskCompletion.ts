/**
 * 🎯 SIMPLIFIED SMART TASK COMPLETION HOOK
 * 
 * Combines useTasks with simple state management
 */

import { useState } from 'react';
import { useTasks } from './useTasks';
import { useFamilySettings } from './useFamilySettings';

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  child_id: string;
  parent_id: string;
  created_at: string;
  completed_at?: string;
  child_name?: string;
  child_avatar?: string;
  is_recurring?: boolean;
}

export const useSmartTaskCompletion = () => {
  const { tasks, loading, completeTask, loadTasks } = useTasks();
  const { familySettings } = useFamilySettings();
  const [completingTasks, setCompletingTasks] = useState<Set<string>>(new Set());
  const [currentTaskForVerification, setCurrentTaskForVerification] = useState<Task | null>(null);

  const handleCompleteTask = async (task: Task) => {
    setCompletingTasks(prev => new Set(prev).add(task.id));
    
    try {
      await completeTask(task.id);
    } catch (error) {
      console.error('Error completing task:', error);
    } finally {
      setCompletingTasks(prev => {
        const newSet = new Set(prev);
        newSet.delete(task.id);
        return newSet;
      });
    }
  };

  const handleTaskComplete = async (task: Task) => {
    return handleCompleteTask(task);
  };

  const handlePinSuccess = () => {
    // Handle PIN success logic
    setCurrentTaskForVerification(null);
  };

  const handlePinCancel = () => {
    // Handle PIN cancel logic
    setCurrentTaskForVerification(null);
  };

  const refreshTasks = async () => {
    await loadTasks();
  };

  const isCompleting = (taskId: string) => completingTasks.has(taskId);

  return {
    tasks,
    loading,
    familySettings,
    loadingTasks: loading,
    handleCompleteTask,
    handleTaskComplete,
    handlePinSuccess,
    handlePinCancel,
    refreshTasks,
    isCompleting,
    currentTaskForVerification,
  };
};
