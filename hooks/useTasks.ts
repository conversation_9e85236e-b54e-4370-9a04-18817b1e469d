/**
 * 🎯 SIMPLIFIED TASKS HOOK
 * 
 * Simple useState/useEffect pattern instead of TanStack Query
 * Direct Supabase calls for maximum performance
 */

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../utils/supabase';

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  child_id: string;
  parent_id: string;
  created_at: string;
  completed_at?: string;
  child_name?: string;
  child_avatar?: string;
  is_recurring?: boolean;
}

interface UseTasksParams {
  child_id?: string;
  status?: string;
}

export const useTasks = (params?: UseTasksParams) => {
  const { user } = useAuth();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadTasks = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from('tasks')
        .select('*')
        .eq('parent_id', user.id);

      if (params?.child_id) {
        query = query.eq('child_id', params.child_id);
      }
      if (params?.status) {
        query = query.eq('status', params.status);
      }

      const { data, error } = await query.order('created_at', { ascending: false });
      
      if (error) throw error;
      setTasks(data || []);
    } catch (err) {
      console.error('Error loading tasks:', err);
      setError(err instanceof Error ? err.message : 'Failed to load tasks');
    } finally {
      setLoading(false);
    }
  };

  const completeTask = async (taskId: string) => {
    try {
      const { data, error } = await supabase.rpc('complete_task', { task_id: taskId });
      if (error) throw error;
      
      // Refresh tasks after completion
      loadTasks();
      return data;
    } catch (err) {
      console.error('Error completing task:', err);
      throw err;
    }
  };

  const createTask = async (taskData: Omit<Task, 'id' | 'created_at'>) => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          ...taskData,
          parent_id: user?.id,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Refresh tasks after creation
      loadTasks();
      return data;
    } catch (err) {
      console.error('Error creating task:', err);
      throw err;
    }
  };

  // Load tasks on mount and when params change
  useEffect(() => {
    loadTasks();
  }, [user?.id, params?.child_id, params?.status]);

  return {
    tasks,
    loading,
    error,
    loadTasks,
    completeTask,
    createTask,
  };
};
