/**
 * 🎯 SIMPLIFIED REWARDS HOOK
 * 
 * Simple useState/useEffect pattern instead of TanStack Query
 * Direct Supabase calls for maximum performance
 */

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { supabase } from '../utils/supabase';

export interface Reward {
  id: string;
  title: string;
  description?: string;
  cost: number;
  category?: string;
  parent_id: string;
  created_at: string;
  updated_at?: string;
}

export const useRewards = () => {
  const { user } = useAuth();
  const [rewards, setRewards] = useState<Reward[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadRewards = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await supabase
        .from('rewards')
        .select('*')
        .eq('parent_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setRewards(data || []);
    } catch (err) {
      console.error('Error loading rewards:', err);
      setError(err instanceof Error ? err.message : 'Failed to load rewards');
    } finally {
      setLoading(false);
    }
  };

  const createReward = async (rewardData: Omit<Reward, 'id' | 'created_at'>) => {
    try {
      const { data, error } = await supabase
        .from('rewards')
        .insert({
          ...rewardData,
          parent_id: user?.id,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      // Refresh rewards after creation
      loadRewards();
      return data;
    } catch (err) {
      console.error('Error creating reward:', err);
      throw err;
    }
  };

  const updateReward = async (rewardId: string, data: Partial<Reward>) => {
    try {
      const { error } = await supabase
        .from('rewards')
        .update(data)
        .eq('id', rewardId);
      
      if (error) throw error;
      
      // Refresh rewards after update
      loadRewards();
    } catch (err) {
      console.error('Error updating reward:', err);
      throw err;
    }
  };

  const deleteReward = async (rewardId: string) => {
    try {
      const { error } = await supabase
        .from('rewards')
        .delete()
        .eq('id', rewardId);
      
      if (error) throw error;
      
      // Refresh rewards after deletion
      loadRewards();
    } catch (err) {
      console.error('Error deleting reward:', err);
      throw err;
    }
  };

  const redeemReward = async (rewardId: string, childId: string) => {
    try {
      const { data, error } = await supabase.rpc('redeem_reward', {
        reward_id: rewardId,
        child_id: childId,
      });
      
      if (error) throw error;
      return data;
    } catch (err) {
      console.error('Error redeeming reward:', err);
      throw err;
    }
  };

  // Load rewards on mount
  useEffect(() => {
    loadRewards();
  }, [user?.id]);

  return {
    rewards,
    loading,
    error,
    loadRewards,
    createReward,
    updateReward,
    deleteReward,
    redeemReward,
  };
};
