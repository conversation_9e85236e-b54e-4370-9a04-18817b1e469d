/**
 * 🎯 SIMPLIFIED TASK TEMPLATES HOOK
 * 
 * Direct Supabase calls for task templates
 */

import { useState, useEffect } from 'react';
import { supabase } from '../utils/supabase';
import { useAuth } from '../contexts/AuthContext';

export interface TaskTemplate {
  id: string;
  title: string;
  description: string;
  value: number;
  category: string;
  frequency: string;
  parent_id: string;
  created_at: string;
}

export const useTaskTemplates = () => {
  const { user } = useAuth();
  const [taskTemplates, setTaskTemplates] = useState<TaskTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadTaskTemplates = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('task_templates')
        .select('*')
        .eq('parent_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      
      setTaskTemplates(data || []);
    } catch (err) {
      console.error('Error loading task templates:', err);
      setError(err instanceof Error ? err.message : 'Failed to load task templates');
    } finally {
      setLoading(false);
    }
  };

  const createTaskTemplate = async (templateData: Omit<TaskTemplate, 'id' | 'created_at'>) => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('task_templates')
        .insert({ ...templateData, parent_id: user.id })
        .select()
        .single();
      
      if (error) throw error;
      
      setTaskTemplates(prev => [data, ...prev]);
      return data;
    } catch (err) {
      console.error('Error creating task template:', err);
      throw err;
    }
  };

  const updateTaskTemplate = async (templateId: string, data: Partial<TaskTemplate>) => {
    try {
      const { data: updatedTemplate, error } = await supabase
        .from('task_templates')
        .update(data)
        .eq('id', templateId)
        .select()
        .single();
      
      if (error) throw error;
      
      setTaskTemplates(prev => 
        prev.map(template => 
          template.id === templateId ? updatedTemplate : template
        )
      );
      return updatedTemplate;
    } catch (err) {
      console.error('Error updating task template:', err);
      throw err;
    }
  };

  const deleteTaskTemplate = async (templateId: string) => {
    try {
      const { error } = await supabase
        .from('task_templates')
        .delete()
        .eq('id', templateId);
      
      if (error) throw error;
      
      setTaskTemplates(prev => prev.filter(template => template.id !== templateId));
    } catch (err) {
      console.error('Error deleting task template:', err);
      throw err;
    }
  };

  useEffect(() => {
    loadTaskTemplates();
  }, [user]);

  return {
    taskTemplates,
    loading,
    error,
    loadTaskTemplates,
    createTaskTemplate,
    updateTaskTemplate,
    deleteTaskTemplate,
  };
};
