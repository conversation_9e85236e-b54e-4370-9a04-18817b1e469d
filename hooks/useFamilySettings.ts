/**
 * 🎯 SIMPLIFIED FAMILY SETTINGS HOOK
 * 
 * Direct Supabase calls for family settings
 */

import { useState, useEffect } from 'react';
import { supabase } from '../utils/supabase';
import { useAuth } from '../contexts/AuthContext';

export interface FamilySettings {
  id: string;
  parent_id: string;
  task_verification_mode: 'strict' | 'relaxed';
  parent_pin?: string;
  created_at: string;
  updated_at: string;
}

export const useFamilySettings = () => {
  const { user } = useAuth();
  const [familySettings, setFamilySettings] = useState<FamilySettings | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadFamilySettings = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('family_settings')
        .select('*')
        .eq('parent_id', user.id)
        .single();
      
      if (error && error.code !== 'PGRST116') throw error; // PGRST116 = no rows returned
      
      setFamilySettings(data || { 
        parent_id: user.id, 
        task_verification_mode: 'strict',
        parent_pin: '1234' // Default PIN
      } as FamilySettings);
    } catch (err) {
      console.error('Error loading family settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load family settings');
    } finally {
      setLoading(false);
    }
  };

  const updateFamilySettings = async (data: Partial<FamilySettings>) => {
    if (!user) return;
    
    try {
      const { data: updatedSettings, error } = await supabase
        .from('family_settings')
        .upsert({ 
          parent_id: user.id, 
          ...data,
          updated_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (error) throw error;
      
      setFamilySettings(updatedSettings);
      return updatedSettings;
    } catch (err) {
      console.error('Error updating family settings:', err);
      throw err;
    }
  };

  useEffect(() => {
    loadFamilySettings();
  }, [user]);

  return {
    familySettings,
    loading,
    error,
    loadFamilySettings,
    updateFamilySettings,
  };
};
