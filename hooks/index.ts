/**
 * 🎯 SIMPLIFIED HOOKS INDEX
 * 
 * Clean, simple hooks using useState/useEffect pattern
 * Direct Supabase calls for maximum performance
 */

export { useTasks } from './useTasks';
export { useChildren } from './useChildren';
export { useRewards } from './useRewards';
export { useTaskTemplates } from './useTaskTemplates';
export { useFamilySettings } from './useFamilySettings';

// Simplified smart hooks
export { useSmartTaskCompletion } from './useSmartTaskCompletion';
export { useSmartChildSelection } from './useSmartChildSelection';
export { useSmartModalManagement } from './useSmartModalManagement';
export { useSmartRewardRedemption } from './useSmartRewardRedemption';
