/**
 * �� SIMPLIFIED CHILDREN HOOK
 * 
 * Direct Supabase calls for children data
 */

import { useState, useEffect } from 'react';
import { supabase } from '../utils/supabase';
import { useAuth } from '../contexts/AuthContext';

export interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  parent_id: string;
  created_at: string;
  coins?: number; // Legacy alias
  streak?: number; // Legacy alias
}

export const useChildren = () => {
  const { user } = useAuth();
  const [children, setChildren] = useState<Child[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadChildren = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const { data, error } = await supabase
        .from('children')
        .select('*')
        .eq('parent_id', user.id)
        .order('created_at', { ascending: true });
      
      if (error) throw error;
      
      // Add legacy aliases for backward compatibility
      const childrenWithAliases = (data || []).map(child => ({
        ...child,
        coins: child.coin_balance,
        streak: child.daily_streak,
      }));
      
      setChildren(childrenWithAliases);
    } catch (err) {
      console.error('Error loading children:', err);
      setError(err instanceof Error ? err.message : 'Failed to load children');
    } finally {
      setLoading(false);
    }
  };

  const updateChild = async (childId: string, data: Partial<Child>) => {
    try {
      const { data: updatedChild, error } = await supabase
        .from('children')
        .update(data)
        .eq('id', childId)
        .select()
        .single();
      
      if (error) throw error;
      
      setChildren(prev => 
        prev.map(child => 
          child.id === childId ? { ...updatedChild, coins: updatedChild.coin_balance, streak: updatedChild.daily_streak } : child
        )
      );
      return updatedChild;
    } catch (err) {
      console.error('Error updating child:', err);
      throw err;
    }
  };

  const resetChildProgress = async (childId: string) => {
    try {
      const { error } = await supabase.rpc('reset_child_progress', { child_id: childId });
      
      if (error) throw error;
      
      // Reload children to get updated data
      await loadChildren();
    } catch (err) {
      console.error('Error resetting child progress:', err);
      throw err;
    }
  };

  useEffect(() => {
    loadChildren();
  }, [user]);

  return {
    children,
    loading,
    error,
    loadChildren,
    updateChild,
    resetChildProgress,
  };
};
