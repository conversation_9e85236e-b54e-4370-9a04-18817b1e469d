# KidsCoin Setup Guide

Complete setup instructions for the KidsCoin family financial literacy app with Nintendo-style gamification.

## 🏗️ Architecture Overview

KidsCoin uses a modern tech stack designed for scalability and real-time family collaboration:

- **Frontend:** React Native with Expo SDK 53
- **Backend:** Supabase (PostgreSQL + Edge Functions)
- **Real-time:** Supabase Realtime for cross-device sync
- **Authentication:** Social login (Apple/Google) via Supabase Auth
- **Monetization:** Freemium model with in-app purchases

## 📋 Prerequisites

- Node.js 18+
- Expo CLI (`npm install -g @expo/cli`)
- Supabase account
- iOS/Android development environment (optional for testing)

## 🚀 Quick Start

### 1. Clone & Install Dependencies

```bash
git clone <repository-url>
cd kidscoin
npm install
```

### 2. Environment Setup

Create `.env` file in project root:

```env
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=your_supabase_project_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# OAuth Credentials (Public - safe to include)
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_google_web_client_id
EXPO_PUBLIC_GOOGLE_IOS_CLIENT_ID=your_google_ios_client_id
EXPO_PUBLIC_GOOGLE_ANDROID_CLIENT_ID=your_google_android_client_id
EXPO_PUBLIC_APPLE_SERVICE_ID=your_apple_service_id

# Build Configuration
PUBLIC_BUILDER_KEY=your_eas_builder_key
```

### 3. Database Setup

#### Run Initial Schema Migration

Copy and execute in Supabase SQL Editor:

```sql
-- 1. Base Schema (required)
-- Copy contents from: migrations/20250117_000001_create_initial_schema.sql

-- 2. Database Functions (required)  
-- Copy contents from: migrations/20250117_000002_create_database_functions.sql

-- 3. Monetization Features (required)
-- Copy contents from: migrations/20250117_000003_add_monetization_features.sql
```

#### Verify Tables Created

Your database should now have these tables:
- `users` - Parent profiles
- `family_settings` - Family configuration
- `children` - Child profiles with monetization limits
- `tasks` / `task_templates` - Task management
- `rewards` / `unlocked_rewards` - Reward system
- `user_subscriptions` - User subscription management
- `admin_monetization_config` - Admin monetization configuration

### 4. Edge Functions Deployment

Deploy the API layer to Supabase:

```bash
# Install Supabase CLI
npm install -g supabase

# Login to Supabase
supabase login

# Link to your project
supabase link --project-ref your-project-ref

# Deploy all Edge Functions
supabase functions deploy family-settings
supabase functions deploy children
supabase functions deploy monetization
supabase functions deploy task-complete-strict
```

#### Required Edge Functions:
- **`family-settings`** - Family configuration management
- **`children`** - Child profile CRUD with limit checking
- **`monetization`** - Subscription and limit management
- **`task-complete-strict`** - PIN-based task completion with real-time sync

### 5. Authentication Setup

#### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create OAuth 2.0 credentials for your app
3. Add redirect URLs:
   - `https://your-project.supabase.co/auth/v1/callback`
   - Your app's custom scheme for mobile

#### Apple Sign-In Setup
1. Go to [Apple Developer Console](https://developer.apple.com)
2. Create App ID with Sign In with Apple capability
3. Create Service ID for web authentication
4. Configure redirect URLs in Supabase Auth settings

#### Configure Supabase Auth
1. Go to Authentication > Settings in Supabase dashboard
2. Add your OAuth providers with client IDs
3. **Important:** Enable "Skip nonce check" for Apple Sign-In
4. Set custom JWT expiry (recommended: 7 days)

### 6. Real-Time Setup

Real-time sync is enabled automatically but requires premium subscription for full functionality.

#### Enable Real-time in Supabase
1. Go to Database > Replication in Supabase dashboard
2. Enable realtime for these tables:
   - `tasks`
   - `children` 
   - `family_settings`
3. Verify RLS policies are active

### 7. Start Development

```bash
# Start Expo development server
npx expo start

# For iOS
npx expo start --ios

# For Android  
npx expo start --android
```

## 🎮 Key Features Implemented

### Nintendo-Style Onboarding
- Animated splash screen with rotating sparkles
- Interactive family settings configuration
- Character creation with emoji avatars
- Celebration animations with haptic feedback

### Freemium Monetization Model
- **Free Tier:** 1 child profile, basic features
- **Basic Plan:** $4.99 - 3 children, no real-time sync
- **Premium Plan:** $9.99 - Unlimited children + real-time sync
- **Real-time Only:** $2.99 one-time or $0.99/month
- Admin controls to enable/disable monetization globally

### Real-Time Cross-Device Sync
- Parents can update tasks from any device
- Children see changes instantly on their tablets
- Haptic feedback for different update types
- Connection status indicators

### Strict Mode PIN Entry
- Gamified PIN entry with Nintendo-style animations
- Child-friendly celebration sequences
- Cross-device approval workflow
- Configurable family PIN (default: 1234)

## 🔧 Configuration Options

### Admin Controls (via admin_monetization_config table)

```sql
-- Disable monetization globally
UPDATE admin_monetization_config SET monetization_enabled = false;

-- Give everyone unlimited children
UPDATE admin_monetization_config SET free_tier_limits = '{"children": 999, "realtime": true, "ai_generation": 1000, "library_access": true}';

-- Update pricing tiers
UPDATE admin_monetization_config SET tiers = '{"basic": {"price": 4.99, "limits": {"children": 3, "realtime": false, "ai_generation": 20, "library_access": true}}, "premium": {"price": 9.99, "limits": {"children": 10, "realtime": true, "ai_generation": 100, "library_access": true}}}';
```

### Family Settings

Parents can configure:
- Task verification mode (Strict vs Trusting)
- Custom PIN for task approval
- Child limits (based on subscription)
- Real-time sync preferences

## 🔐 Security Features

### Row Level Security (RLS)
- Families can only access their own data
- Parent-only access to admin functions
- Secure PIN verification on server-side

### Data Protection
- OAuth tokens stored securely by Supabase
- PINs hashed in database
- Real-time channels are family-scoped

## 📊 Monitoring & Analytics

### Available Metrics
- Real-time connection status
- Task completion rates by child
- Subscription conversion funnel
- Feature usage analytics

### Logging
- Edge Function execution logs
- Real-time connection diagnostics
- Authentication success/failure rates

## 🧪 Testing

### Demo Components Available
- `TaskCompletionDemo` - Test PIN entry and real-time sync
- Onboarding flow with reset functionality
- Sample family data for testing

### Test Accounts
Default PIN for demo: `1234`

### Testing Real-Time Sync
1. Open app on multiple devices/simulators
2. Sign in with same parent account
3. Complete task on one device
4. Verify instant updates on other devices

## 🚀 Production Deployment

### Pre-Deployment Checklist
- [ ] Configure OAuth redirect URLs for production
- [ ] Set up proper SSL certificates
- [ ] Configure App Store/Google Play billing
- [ ] Set production environment variables
- [ ] Enable Supabase production mode
- [ ] Test real-time sync in production environment

### Environment Variables for Production
```env
EXPO_PUBLIC_SUPABASE_URL=your_production_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_production_anon_key
# ... other production OAuth credentials
```

### Deployment Commands
```bash
# Build for production
eas build --platform all

# Deploy Edge Functions to production
supabase functions deploy --project-ref your-production-ref
```

## 🆘 Troubleshooting

### Common Issues

#### "No script URL provided" Error
- Ensure Metro bundler is running: `npx expo start --clear`
- Check network connectivity
- Restart Expo development server

#### Real-Time Not Working
- Verify real-time is enabled in Supabase dashboard
- Check subscription status in `family_settings` table
- Confirm RLS policies are active
- Test connection status in app

#### Authentication Failures
- Verify OAuth redirect URLs match exactly
- Check "Skip nonce check" is enabled for Apple
- Ensure client IDs are correct in environment variables

#### PIN Entry Not Working
- Verify family_settings table has parent_pin column
- Check task_id exists and belongs to authenticated user
- Confirm strict mode is enabled in family settings

### Getting Help
- Check Supabase logs for Edge Function errors
- Monitor real-time connection status in app
- Use browser dev tools for web debugging
- Check Expo logs for mobile-specific issues

## 📱 Mobile-Specific Setup

### iOS Configuration
- Configure URL schemes in `app.json`
- Set up Apple Developer certificates
- Test on physical device for haptic feedback

### Android Configuration  
- Configure intent filters for OAuth
- Set up Google Play Console
- Test real-time sync over mobile networks

## 🎯 Next Steps

After basic setup:
1. Customize pricing tiers in `admin_monetization_config`
2. Add your branding and colors
3. Set up in-app purchase processing
4. Configure push notifications
5. Add analytics tracking
6. Test with real family data

## 📚 API Documentation

### Edge Functions Endpoints

#### Family Settings
```typescript
GET /functions/v1/family-settings
PUT /functions/v1/family-settings
```

#### Children Management
```typescript
POST /functions/v1/children
PUT /functions/v1/children/:id
DELETE /functions/v1/children/:id
```

#### Monetization
```typescript
GET /functions/v1/monetization/limits
POST /functions/v1/monetization/upgrade
POST /functions/v1/monetization/check-child-limit
```

#### Task Completion
```typescript
GET /functions/v1/task-complete-strict?task_id=xxx
POST /functions/v1/task-complete-strict
```

### Database Functions
```sql
-- Check user subscription status
SELECT * FROM user_subscriptions WHERE parent_id = 'parent-uuid' AND status = 'active';

-- Check admin monetization config
SELECT * FROM admin_monetization_config;

-- Complete task with celebration
SELECT complete_task('task-uuid', true);

-- Create user subscription
INSERT INTO user_subscriptions (parent_id, tier, purchase_type, status, purchase_token) 
VALUES ('parent-uuid', 'premium', 'one_time', 'active', 'receipt-token');
```

---

🎉 **Congratulations!** Your KidsCoin app is now set up with full freemium monetization, real-time sync, and Nintendo-style gamification. Happy coding! 