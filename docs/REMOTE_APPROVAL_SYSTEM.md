# Remote Approval System Design

## Overview
The Remote Approval System allows parents with premium plans to approve task completions from their own device via push notifications, while basic plan users use PIN entry on the child's device.

## User Experience Flow

### Basic Plan (Local PIN)
```
1. Child completes task
2. Task requires verification
3. <PERSON><PERSON> enters PIN on child's device
4. Task approved and completed
```

### Remote Plan (Push Notification)
```
1. Child completes task  
2. Task requires verification
3. Push notification sent to parent's device
4. <PERSON><PERSON> opens app and sees pending approval
5. <PERSON><PERSON> approves/denies from their device
6. Child's device receives real-time update
7. Task completed (or denied with explanation)
```

## Technical Implementation

### Database Schema
```sql
-- Add to family_settings table
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS subscription_plan TEXT DEFAULT 'basic';
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS push_token TEXT;
ALTER TABLE family_settings ADD COLUMN IF NOT EXISTS parent_device_id TEXT;

-- Pending approvals table
CREATE TABLE IF NOT EXISTS pending_approvals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID REFERENCES tasks(id),
  child_id UUID REFERENCES children(id),
  parent_id UUID REFERENCES auth.users(id),
  task_title TEXT NOT NULL,
  task_value INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 hour'),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'denied', 'expired'))
);
```

### Push Notification Service
```typescript
// Edge Function: send-approval-notification
export const sendApprovalNotification = async (approvalRequest: {
  parentId: string;
  taskTitle: string;
  childName: string;
  coinValue: number;
  approvalId: string;
}) => {
  // Get parent's push token
  const { data: familySettings } = await supabase
    .from('family_settings')
    .select('push_token')
    .eq('parent_id', approvalRequest.parentId)
    .single();

  if (!familySettings?.push_token) {
    throw new Error('No push token found for parent');
  }

  // Send push notification via Expo Push Notifications
  const message = {
    to: familySettings.push_token,
    sound: 'default',
    title: `${approvalRequest.childName} completed a task!`,
    body: `"${approvalRequest.taskTitle}" - ${approvalRequest.coinValue} coins`,
    data: { 
      type: 'task_approval',
      approvalId: approvalRequest.approvalId 
    },
  };

  return await fetch('https://exp.host/--/api/v2/push/send', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(message),
  });
};
```

### Real-time Updates
```typescript
// Parent App - Listen for approval responses
useEffect(() => {
  const subscription = supabase
    .channel('pending_approvals')
    .on('postgres_changes', 
      { 
        event: 'UPDATE', 
        schema: 'public', 
        table: 'pending_approvals',
        filter: `parent_id=eq.${user.id}`
      }, 
      (payload) => {
        // Update UI with new approval request
        handleNewApprovalRequest(payload.new);
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, []);

// Child App - Listen for approval decisions  
useEffect(() => {
  const subscription = supabase
    .channel('task_approvals')
    .on('postgres_changes',
      {
        event: 'UPDATE',
        schema: 'public', 
        table: 'pending_approvals',
        filter: `child_id=eq.${currentChild.id}`
      },
      (payload) => {
        if (payload.new.status === 'approved') {
          handleTaskApproved(payload.new);
        } else if (payload.new.status === 'denied') {
          handleTaskDenied(payload.new);
        }
      }
    )
    .subscribe();

  return () => subscription.unsubscribe();
}, [currentChild]);
```

### Parent Approval Interface
```typescript
// Parent Dashboard - Pending Approvals Section
const PendingApprovals = () => {
  const { data: pendingApprovals } = useQuery({
    queryKey: ['pending-approvals'],
    queryFn: () => approvalsApi.getPending(),
    refetchInterval: 30000, // Backup polling
  });

  const handleApprove = async (approvalId: string) => {
    await approvalsApi.approve(approvalId);
    queryClient.invalidateQueries(['pending-approvals']);
  };

  const handleDeny = async (approvalId: string, reason: string) => {
    await approvalsApi.deny(approvalId, reason);
    queryClient.invalidateQueries(['pending-approvals']);
  };

  return (
    <View style={styles.pendingSection}>
      <Text style={styles.sectionTitle}>⏳ Pending Approvals</Text>
      {pendingApprovals?.map(approval => (
        <ApprovalCard 
          key={approval.id}
          approval={approval}
          onApprove={() => handleApprove(approval.id)}
          onDeny={(reason) => handleDeny(approval.id, reason)}
        />
      ))}
    </View>
  );
};
```

## Implementation Status

### ✅ Completed
- Basic PIN verification system
- Task verification toggle in parent dashboard
- Plan detection logic (framework)
- Documentation of full system

### 🚧 In Progress
- Remote plan placeholder alerts
- Database schema preparation

### 📋 Next Steps
1. **Push Notification Setup**
   - Integrate Expo Push Notifications
   - Handle device token registration
   - Set up notification permissions

2. **Real-time Sync**
   - Implement Supabase realtime subscriptions
   - Add approval request/response flow
   - Handle connection failures gracefully

3. **Parent Approval UI**
   - Add pending approvals section to parent dashboard
   - Create approval/denial modals
   - Add approval history

4. **Edge Cases**
   - Handle expired approvals
   - Offline approval caching
   - Multiple parent devices
   - Child device disconnection during approval

## Benefits

### For Parents
- **Convenience**: Approve tasks without interrupting their day
- **Context**: See exactly what child completed with full details
- **Control**: Easy approve/deny with optional reasons
- **History**: Track all approvals and decisions

### For Children
- **Instant Feedback**: Know immediately when parent approves
- **Clear Expectations**: Understand what requires approval
- **Motivation**: Gamified approval process

### For Business
- **Premium Feature**: Clear value proposition for remote plan
- **Engagement**: Keeps parents connected to child's progress
- **Scalability**: Works across multiple devices and locations