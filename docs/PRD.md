# KidsCoin Product Requirements Document (PRD)

## 1. Overview

KidsCoin is a digital platform designed to teach children financial literacy and responsibility in a fun, gamified, and safe environment. The application serves as a bridge between parents and children, transforming everyday chores and good habits into learning opportunities.

The core product loop is designed to be simple yet effective:
1.  **Parent assigns tasks.**
2.  **Child completes tasks to earn digital coins.**
3.  **Child redeems coins for real-world rewards.**

This process is enriched with gamification elements like levels, XP, streaks, and achievements to keep children engaged, motivated, and excited about financial responsibility.

## 2. Target Audience & User Personas

### 2.1. The Parent

*   **Persona:** "<PERSON>, the Organized Parent"
*   **Demographics:** 30-45 years old, tech-savvy, has one or more children aged 5-12.
*   **Goals:**
    *   To teach their children the value of money and a strong work ethic.
    *   To find a simple, consistent system for managing chores and allowances.
    *   To reduce nagging and arguments about household responsibilities.
    *   To have clear visibility into their children's activities and progress.
*   **Frustrations:**
    *   Whiteboards and sticker charts are messy and easily forgotten.
    *   Keeping track of cash for allowances is inconvenient.
    *   It's hard to make abstract concepts like "saving" tangible for young kids.

### 2.2. The Child

*   **Persona:** "<PERSON>, the Eager Earner"
*   **Demographics:** 5-12 years old, familiar with tablets/smartphones, enjoys games.
*   **Goals:**
    *   To earn rewards, like more screen time, a new toy, or a special outing.
    *   To feel a sense of accomplishment and independence.
    *   To have fun and be entertained while learning.
*   **Frustrations:**
    *   Chores are boring and feel like a burden.
    *   Not understanding why they can't have everything they want immediately.
    *   Feeling like they have no control over their own "money".

## 3. Goals & Success Metrics

| Goal                                                              | Success Metrics                                                                                               |
| ----------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------- |
| **Primary: Foster Financial Literacy in Children**                | - Task Completion Rate > 70% <br/> - % of children setting and achieving a savings goal for a reward.             |
| **Parent Enablement: Simplify chore & allowance management**      | - Parent DAU/WAU Ratio > 40% <br/> - Average number of active tasks per family > 5.                              |
| **Child Engagement: Create a fun and motivating experience**      | - Child DAU/WAU Ratio > 50% <br/> - Average session length in Kid Mode. <br/> - Daily Streak retention rate.      |
| **Platform Stability & Performance**                              | - Crash-free session rate > 99.5% <br/> - API response time < 200ms (p95).                                          |

## 4. Core Epics, Features, and User Stories

This section details the features of the application based on the current implementation.

### Epic 0: Onboarding Experience

This epic focuses on creating a delightful, intuitive first-time setup experience that gets families up and running quickly while building excitement about the platform.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                      | Features Implied                                                                                                  |
| ----- | --------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| **0.1** | As a Parent, I want a welcoming splash screen that immediately communicates the value of KidsCoin in simple terms, so I feel confident this is the right choice for my family. | - Animated splash screen with clear value proposition. <br/> - "Teaching financial responsibility through fun" messaging. <br/> - Smooth transition animations between onboarding steps. |
| **0.2** | As a Parent, I want to create my account with minimal friction (email/password or social login) so I can get started quickly without barriers. | - Streamlined signup flow with clear progress indicators. <br/> - Social login options (Google, Apple) as primary CTAs. <br/> - Email/password as secondary option. |
| **0.3** | As a Parent, I want to set my family's "house rules" upfront (task verification mode) with clear explanations and visuals, so I understand the implications of my choice. | - Interactive toggle between "Strict" and "Trusting" modes. <br/> - Visual examples showing how each mode works. <br/> - "You can change this later" reassurance text. |
| **0.4** | As a Parent, I want to create my first child's profile in a guided, step-by-step process with delightful animations, so the experience feels magical rather than tedious. | - Progressive disclosure: Name → Age → Avatar selection → Starting coin balance. <br/> - Large, friendly avatar gallery with diverse representation. <br/> - Celebratory animations when each step is completed. <br/> - Option to "Add Another Child" or "Continue" at the end. |
| **0.5** | As a Parent, I want to create my first task template in a guided tutorial that shows me exactly how the system works, so I immediately understand the value and feel empowered to continue. | - Guided task creation with helpful hints and examples. <br/> - Pre-populated suggestions like "Make Bed - 5 coins" that can be customized. <br/> - Live preview of how the task will appear to the child. <br/> - "Assign to [Child Name]" button that demonstrates the assignment flow. |
| **0.6** | As a Parent, I want to set up my first reward in the shop during onboarding, so I complete the full loop and understand how motivation works in the platform. | - Guided reward creation with suggested examples ("Extra Screen Time - 30 coins"). <br/> - Visual representation of the reward shop as the child will see it. <br/> - Explanation of the earning/spending cycle. |
| **0.7** | As a Parent, I want a final "Let's Go!" screen that summarizes what I've set up and shows me next steps, so I feel accomplished and know how to proceed. | - Summary cards showing: Child created, Task assigned, Reward available. <br/> - "Switch to Kid Mode" prominent CTA. <br/> - "Invite [Child] to try their first task!" messaging. <br/> - Access to a quick tutorial or help center. |
| **0.8** | As a Parent, I want the option to skip ahead or go back during onboarding without losing my progress, so I have control over the experience. | - Clear "Back" and "Skip" buttons on each step. <br/> - Progress saved automatically. <br/> - Option to "Complete Setup Later" with graceful exit to main app. |

### Epic 1: Family & User Management

This epic covers the setup and management of parent and child accounts.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                      | Features Implied                                                                                                  |
| ----- | --------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------- |
| **1.1** | As a Parent, I want to create a secure account for my family to begin our financial literacy journey.                   | Standard user authentication (Email/Password, Social Logins).                                                     |
| **1.2** | As a Parent, I want to add a unique profile for each of my children (with name, age, and avatar) so I can personalize their experience. | - "Add Child" form with fields for name, age, avatar, and starting balance. <br/> - A pre-defined list of customizable avatars. <br/> - Children are linked to the parent account.         |
| **1.3** | As a Parent, I want to edit my child's profile information at any time to keep it up to date.                         | - "Edit Child" functionality to update all profile fields.                                                                                     |
| **1.4** | As a Parent, I want to be able to remove a child's profile if it's no longer needed.                                   | - "Delete Child" with a confirmation dialog to prevent accidental data loss.                                       |
| **1.5** | As a Parent, I want a Family Dashboard that gives me a high-level overview of my family's progress (total coins, task completion rate, etc.). | - A main dashboard view with aggregate statistic cards. <br/> - "Most Active Child" highlight. <br/> - A feed of recent task completions across the family. |
| **1.6** | As a Parent, I want to switch between my view and the kid's view to see the app as my child does.                       | - A prominent Parent/Kid mode toggle in the main application layout.                                                                             |
| **1.7** | As a Parent, I want to view an individual child's detailed profile to see their specific tasks, rewards, and settings. | - Navigable child profile pages from the Family Dashboard or the Children Manager screen.                                    |
| **1.8** | As a Parent, I want to configure the task completion rules for my family, so I can choose the level of friction that is appropriate for my children's age and my supervision style. | - A settings page for the parent. <br/> - An option to set "Task Verification" to either "Strict" (requires parent approval/code) or "Trusting" (child can complete tasks directly). |

### Epic 2: Task Lifecycle Management

This epic covers the creation, assignment, and completion of tasks that drive the core loop.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                          | Features Implied                                                                                                                                                             |
| ----- | -------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **2.1** | As a Parent, I want to create reusable Task Templates for common chores (e.g., "Make Bed") so I can quickly assign them without re-typing details.     | - A dedicated "Task Templates" manager. <br/> - Full CRUD (Create, Read, Update, Delete, Duplicate) for templates. <br/> - Templates include title, description, coin value, category, and suggested frequency.     |
| **2.2** | As a Parent, I want to assign a task from a template or create a custom one-off task for a specific child.                   | - An "Assign Task" flow from the template manager that allows selecting one or more children. <br/> - A "Create Task" form directly within the child's profile. <br/> - Ability to set tasks as recurring (e.g., daily).                     |
| **2.3** | As a Parent, I want to be able to reverse an accidental completion to maintain an accurate record of tasks.               | - Parents have the ability to mark a "completed" task as "pending" again, which correctly adjusts the child's coin balance.                               |
| **2.4** | As a Child, I want to see a clear list of my pending tasks so I know exactly what I need to do to earn coins.                                    | - A visually distinct "To-Do" list in the child's dashboard.                                                                                                                                   |
| **2.5** | As a Child, I want to mark a task as "Done" and, *if required by my parent's settings*, request approval so that I can get credit for my work. | - An action on the task card to initiate completion. <br/> - If verification is 'Strict', this action changes the task state to "Pending Approval". <br/> - If verification is 'Trusting', the task is completed immediately. |
| **2.6** | As a Parent, *if I have strict verification enabled*, I want to approve my child's completed tasks so I can verify the work was done correctly before coins are awarded. | - A "Pending Approval" queue or filter in the parent dashboard. <br/> - "Approve" and "Reject" actions for these tasks. |
| **2.7** | As a Parent, *if I have strict verification enabled*, I want the option to provide a one-time code for my child to complete a task so they can get instant gratification when I'm with them. | - A "Generate Code" button on tasks pending approval. <br/> - The child's UI will prompt for this code to finalize completion. |
| **2.8** | As a Parent, I want to see which tasks are pending, awaiting approval, and completed for each child to track their progress and accountability. | - Segregated lists for "Pending", "Awaiting Approval" (if applicable), and "Completed" tasks with clear statistics. |

### Epic 3: Rewards & Motivation System

This epic focuses on how children are motivated to use their earned coins, connecting effort to tangible outcomes.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                | Features Implied                                                                                                |
| ----- | ---------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------------- |
| **3.1** | As a Parent, I want to create a "Reward Shop" with items or privileges my child can buy with their coins so that the coins have real-world value. | - A "Rewards Manager" for parents. <br/> - Full CRUD for rewards, including title, description, and coin cost.   |
| **3.2** | As a Child, I want to browse the Reward Shop to see what I can save up for and get motivated.                                      | - A "Rewards" tab or section in the child's dashboard showing available rewards and their costs.                           |
| **3.3** | As a Child, I want to "buy" a reward when I have enough coins so I can claim my prize.                           | - A "Redeem" button that becomes active when the child's balance meets the reward cost. This should deduct the coins and mark the reward as unlocked.                         |
| **3.4** | As a Parent, I want to be notified when my child redeems a reward so I can fulfill it (e.g., buy the toy, grant the screen time). | - *[Future Enhancement]* A notification system for parents. Currently, parents can see unlocked rewards in their dashboard. |
| **3.5** | As a Parent, I want to manually mark a reward as "Unlocked" or "Relocked" to have full control over reward fulfillment.                   | - A toggle for `isUnlocked` status on rewards, managed from the parent's interface.                                                   |

### Epic 4: Gamification & Engagement

This epic details the game-like elements that make the app engaging and fun for children.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                                     | Features Implied                                                                                                   |
| ----- | ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------ |
| **4.1** | As a Child, I want to earn Experience Points (XP) for completing tasks so I can "Level Up" and feel a sense of progression and mastery.          | - XP awarded for tasks. <br/> - A Level system with a visible XP progress bar. <br/> - Database fields for `level`, `xp`, and `nextLevelXp`.   |
| **4.2** | As a Child, I want to maintain a "Daily Streak" for completing at least one task a day to earn bonus rewards and stay consistent.                         | - A streak counter that increments daily upon first task completion.                                                     |
| **4.3** | As a Child, I want to unlock Achievements for reaching milestones (e.g., "Completed 50 tasks") to get recognition and bonus coins. | - An "Achievements" page. <br/> - Pre-defined achievements with unlock criteria, progress tracking, and coin rewards. |
| **4.4** | As a Child, I want a visually rich and playful interface so that using the app feels like a game, not a chore list.                   | - "Candy" theme with bright colors, playful fonts, and dynamic animations. <br/> - Interactive elements like floating stars, pop-in effects, and sound feedback.     |

### Epic 5: Kid's Kiosk Mode

This epic ensures a safe, focused, and immersive environment for the child, free from distractions.

| ID    | User Story (As a [Persona], I want to [Action] so that [Benefit])                                                   | Features Implied                                                                                                                                  |
| ----- | ------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| **5.1** | As a Parent, I want to lock the app in "Kid Mode" with a passcode so my child can't access parent settings or other apps on the device. | - A passcode/PIN protection feature to exit Kid Mode. <br/> - This mode should integrate with OS-level features to disable system navigation where possible (e.g., Guided Access on iOS / Screen Pinning on Android). |
| **5.2** | As a Parent, I want the app to stay in Kid Mode even if it's closed and reopened so I don't have to re-lock it every time. | - Persistent state for Kiosk Mode that survives app restarts.                                                                                                                |
| **5.3** | As a Child, I want to be able to switch between my profile and my sibling's profile without needing a parent, if we share a device.      | - A simple child selector within the locked Kid Mode interface.                                                       |
| **5.4** | As a Parent, I want task completion to be frictionless when Kiosk Mode is unlocked, *bypassing the 'Strict' verification setting*, so I can quickly approve tasks when I'm supervising my child on their device. | - When Kiosk Mode is unlocked (via parent passcode), the child can mark tasks as complete without needing a secondary code, as parental supervision is assumed. |

## 5. Proposed Technical Architecture

The platform will be rebuilt from the ground up to be scalable, robust, and real-time, using a modern technology stack.

### 5.1. Frontend: Expo (React Native)

*   **Framework:** React Native with the latest Expo SDK.
*   **Reasoning:**
    *   **Cross-Platform:** A single codebase for both iOS and Android reduces development time and ensures feature parity.
    *   **Rapid Development:** Expo's managed workflow, extensive libraries, and OTA (Over-the-Air) updates streamline the development and release cycle.
    *   **Native Performance:** Access to native UI components and APIs ensures a smooth and responsive user experience, which is critical for a gamified app.
    *   **Strong Community:** A large ecosystem of libraries and community support for any challenges that arise.

### 5.2. Backend: Supabase

*   **Platform:** Supabase will be used as the Backend-as-a-Service (BaaS).
*   **Reasoning:**
    *   **Integrated Solution:** Provides a comprehensive suite of tools (Database, Auth, Storage, Realtime, Edge Functions) out of the box, reducing complexity and the need to manage multiple services.
    *   **PostgreSQL Database:** A powerful, reliable, and highly-scalable open-source relational database at its core. The auto-generated APIs make data access straightforward.
    *   **Realtime Communication:** Supabase's Realtime Engine is perfect for instantly syncing state between the parent and child apps. When a parent assigns a task, it will appear on the child's device immediately.
    *   **Authentication:** Secure and easy-to-implement user management for parents with built-in support for social providers.
    *   **Scalability:** Supabase is built to scale, providing a solid foundation for future user growth and feature expansion.

### 5.3. High-Level Database Schema (Supabase/Postgres)

```sql
-- Users (Parents)
CREATE TABLE users (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  full_name TEXT
  -- other profile data
);

-- Family-level settings managed by the parent
CREATE TABLE family_settings (
  parent_id UUID PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
  task_verification_mode TEXT NOT NULL DEFAULT 'strict', -- 'strict' (requires approval), 'trusting' (child can complete directly)
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Children linked to a parent user
CREATE TABLE children (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  age INT,
  avatar TEXT,
  coin_balance INT NOT NULL DEFAULT 0,
  level INT NOT NULL DEFAULT 1,
  xp INT NOT NULL DEFAULT 0,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Task Templates created by parents
CREATE TABLE task_templates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  value INT NOT NULL, -- coin value
  category TEXT,
  frequency TEXT, -- e.g., 'Daily', 'Weekly'
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Assigned Tasks (instances of templates or custom tasks)
CREATE TABLE tasks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  child_id UUID NOT NULL REFERENCES children(id) ON DELETE CASCADE,
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  value INT NOT NULL, -- coin value
  status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'pending_approval', 'completed'
  is_recurring BOOLEAN NOT NULL DEFAULT FALSE,
  template_id UUID REFERENCES task_templates(id) ON DELETE SET NULL,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMPTZ
);

-- Rewards available in the "Shop"
CREATE TABLE rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  parent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  cost INT NOT NULL, -- coin cost
  category TEXT,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- A record of each reward unlocked by a child
CREATE TABLE unlocked_rewards (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  child_id UUID NOT NULL REFERENCES children(id) ON DELETE CASCADE,
  reward_id UUID NOT NULL REFERENCES rewards(id) ON DELETE CASCADE,
  unlocked_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Short-lived codes for in-person task verification
CREATE TABLE task_completion_codes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  code TEXT NOT NULL, -- A short, easy-to-read code (e.g., 4-digit number)
  expires_at TIMESTAMPTZ NOT NULL DEFAULT NOW() + INTERVAL '5 minutes',
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

### 5.4. Realtime Task Assignment Flow Example

1.  **Parent Assigns Task:** The parent's app calls a Supabase Edge Function `assignTask`.
2.  **Function Logic:** The function creates a new row in the `tasks` table with the `child_id` and task details.
3.  **Realtime Broadcast:** Supabase's Realtime service broadcasts the `INSERT` event on the `tasks` table.
4.  **Child Receives Task:** The child's app, subscribed to changes on the `tasks` table where `child_id` matches their own, receives the broadcast and instantly adds the new task to their to-do list with a subtle UI update.

### 5.5. Conditional Task Verification Flow
The verification process is determined by the `family_settings.task_verification_mode` for the parent.

**Scenario 1: 'Trusting' Mode is Enabled**
1.  **Child Completes Task:** The child taps "Done" on a task in their app.
2.  **Direct Completion:** The app calls a Supabase function. This function first checks that the family's setting is indeed `'trusting'`. It then updates the task `status` to `'completed'` and, in the same transaction, adds the coin value to the `children.coin_balance`.
3.  **Realtime Broadcast:** Supabase broadcasts the `status` and `coin_balance` updates. The child's app shows a success animation, and the parent's app reflects the completed task in real-time.

**Scenario 2: 'Strict' Mode is Enabled (Default)**
This mode provides three paths for task verification, giving the parent flexibility.

*   **A) Remote Parent Approval (The "Trust but Verify" method)**
    1.  **Child Requests Approval:** The child's app marks a task as done. This calls a function that updates the task `status` to `'pending_approval'`.
    2.  **Realtime Broadcast:** Supabase broadcasts this status change.
    3.  **Parent Notified:** The parent's app receives the update and shows the task in a queue needing approval.
    4.  **Parent Approves:** The parent taps "Approve." This calls a function that updates the task `status` to `'completed'` and awards the coins.
    5.  **Child Receives Coins:** Supabase broadcasts the final status and balance change. The child's app receives this and triggers a celebration animation.

*   **B) In-Person Code Approval (The "Instant Gratification" method)**
    1.  **Child Requests Approval & Parent Generates Code:** The child taps "Done". Simultaneously, the parent taps a "Generate Code" button for that task on their device. This creates a short-lived, single-use code in the `task_completion_codes` table.
    2.  **Child Enters Code:** The parent reads the code to the child, who enters it into their app.
    3.  **Code Verification & Completion:** The child's app sends the code to a Supabase function. If the code is valid for that specific task, the function updates the task `status` to `'completed'` and awards the coins. The child sees the success animation immediately.

*   **C) Unlocked Kiosk Mode (The "Supervised Session" method)**
    1.  **Parent Unlocks Kiosk:** The parent enters their PIN to unlock Kiosk Mode on the child's device. The app is now in a temporary "supervised" state.
    2.  **Child Completes Task:** The child taps "Done" on a task.
    3.  **Frictionless Approval:** Because the app is in the "supervised" state, it calls a function that directly sets the task `status` to `'completed'` and awards coins, bypassing the need for a code or remote approval.

## 6. API Definitions (via Edge Functions)

To ensure a clean separation between the frontend and backend, the application will not interact with the database directly from the client. Instead, it will communicate through a series of well-defined RESTful endpoints implemented as Supabase Edge Functions.

---

### **Resource: `users` & `family_settings`**

| Endpoint                                  | Method | Description                                                                                               | Request Body                                | Success Response (200)                               |
| ----------------------------------------- | ------ | --------------------------------------------------------------------------------------------------------- | ------------------------------------------- | ---------------------------------------------------- |
| `/api/family/settings`                    | `GET`  | Fetches the current settings for the authenticated user's family.                                         | `(none)`                                    | `{ "task_verification_mode": "strict" }`             |
| `/api/family/settings`                    | `PUT`  | Updates the family's settings. Validates that the user is the parent/owner.                               | `{ "task_verification_mode": "trusting" }`   | `{ "task_verification_mode": "trusting" }`           |

---

### **Resource: `children`**

| Endpoint                | Method   | Description                                                           | Request Body                                     | Success Response (200 or 201)                          |
| ----------------------- | -------- | --------------------------------------------------------------------- | ------------------------------------------------ | ------------------------------------------------------ |
| `/api/children`         | `POST`   | Adds a new child to the authenticated parent's family.                | `{ "name": "Leo", "age": 8, "avatar": "👦" }`      | The full new `child` object with its generated `id`.   |
| `/api/children/:id`     | `PUT`    | Updates a specific child's profile.                                   | `{ "name": "Leo", "coinBalance": 150 }`           | The full updated `child` object.                       |
| `/api/children/:id`     | `DELETE` | Removes a child from the family.                                      | `(none)`                                         | `{ "success": true, "message": "Child removed." }`     |

---

### **Resource: `task_templates`**

| Endpoint                      | Method   | Description                                         | Request Body                                                              | Success Response (200 or 201)                      |
| ----------------------------- | -------- | --------------------------------------------------- | ------------------------------------------------------------------------- | -------------------------------------------------- |
| `/api/task-templates`         | `POST`   | Creates a new reusable task template for the family.  | `{ "title": "Clean Room", "value": 10 }`                                  | The full new `task_template` object.               |
| `/api/task-templates/:id`     | `PUT`    | Updates an existing task template.                  | `{ "value": 15 }`                                                         | The full updated `task_template` object.           |
| `/api/task-templates/:id`     | `DELETE` | Deletes a task template.                            | `(none)`                                                                  | `{ "success": true, "message": "Template removed." }` |

---

### **Resource: `tasks` (Assigned Tasks)**

| Endpoint                              | Method | Description                                                                                                                                              | Request Body                                                                             | Success Response (200)                                                     |
| ------------------------------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------------- | -------------------------------------------------------------------------- |
| `/api/tasks`                          | `POST` | Assigns a new task to one or more children. Can be from a template or a custom one-off task.                                                              | `{ "child_ids": ["..."], "title": "Walk the dog", "value": 5 }`                           | An array of the newly created `task` objects.                              |
| `/api/tasks/:id/complete`             | `POST` | The primary endpoint for a child to mark a task as done. The function's internal logic will handle the "Strict" vs. "Trusting" family setting.             | `(none)`                                                                                 | The updated `task` object with `status: 'completed'` or `status: 'pending_approval'`. |
| `/api/tasks/:id/approve`              | `POST` | **Parent-only.** Approves a task that is in `pending_approval` status. The function will update the task status and award coins to the child.               | `(none)`                                                                                 | `{ "task": { ... }, "child": { ... } }` (updated objects)                   |
| `/api/tasks/:id/generate-code`        | `POST` | **Parent-only.** Generates a short-lived, one-time verification code for a specific task.                                                                  | `(none)`                                                                                 | `{ "code": "1234" }`                                                       |
| `/api/tasks/:id/complete-with-code`   | `POST` | **Child-facing.** Child submits the code provided by the parent. The function verifies the code and, if valid, completes the task and awards coins.         | `{ "code": "1234" }`                                                                     | `{ "success": true, "message": "Task complete!" }`                         |

---

### **Resource: `rewards`**

| Endpoint                | Method   | Description                                                                                                      | Request Body                                     | Success Response (200 or 201)                           |
| ----------------------- | -------- | ---------------------------------------------------------------------------------------------------------------- | ------------------------------------------------ | ------------------------------------------------------- |
| `/api/rewards`          | `POST`   | Creates a new reward in the family's reward shop.                                                                  | `{ "title": "Ice Cream Trip", "cost": 100 }`      | The full new `reward` object.                           |
| `/api/rewards/:id`      | `PUT`    | Updates an existing reward.                                                                                        | `{ "cost": 120 }`                                | The full updated `reward` object.                       |
| `/api/rewards/:id`      | `DELETE` | Deletes a reward from the shop.                                                                                    | `(none)`                                         | `{ "success": true, "message": "Reward removed." }`     |
| `/api/rewards/:id/redeem` | `POST` | **Child-facing.** A child attempts to redeem a reward. The function will verify they have enough coins before proceeding. | `(none)`                                         | `{ "success": true, "message": "Reward redeemed!" }`    |

## 7. Future Considerations (Out of Scope for Initial Rebuild)

*   **Advanced Financial Education:** The current `FinancialEducation` component is a placeholder. A future version could include interactive video lessons, quizzes, and structured learning paths.
*   **Savings Goals & Interest:** Allowing children to create savings goals and earning a small "interest" on their balance (paid by the parent) to teach another core financial concept.
*   **Detailed Reporting:** More in-depth analytics for parents on earning/spending habits over time.
*   **Multi-Parent/Guardian Access:** Allowing more than one parent or guardian to manage the family account.