# 🚀 IMPLEMENTATION GUIDE: ARCHITECTURAL OVERHAUL
## Specific Code Patterns & Memory Commitments for Cursor

---

## 🎯 **CORE PRINCIPLES FOR CURSOR TO REMEMBER**

### **1. State Management Separation**
```typescript
// ✅ ALWAYS: Server state in TanStack Query
const { data: tasks } = useQuery(['tasks'], fetchTasks);

// ✅ ALWAYS: Client state in Zustand
const { selectedChildId } = useAppStore();

// ✅ ALWAYS: Combine in smart hooks
export const useSmartTaskCompletion = () => {
  const { data: tasks } = useQuery(['tasks']);
  const { selectedChildId } = useAppStore();
  return { tasks, selectedChildId };
};
```

### **2. GraphQL for Complex Queries Only**
```typescript
// ✅ USE GraphQL for: Child profiles, dashboard data, analytics
const { data } = useQuery(['child-profile', childId], () =>
  graphqlClient.query(`
    query GetChildProfile($childId: UUID!) {
      child(id: $childId) {
        id, name, avatar, coin_balance, level, xp
        tasks { id, title, value, status, completed_at }
        achievements { id, title, description, unlocked_at }
        rewards { id, title, cost, unlocked_at }
      }
    }
  `, { childId })
);

// ❌ DON'T use GraphQL for: Simple CRUD operations
const { data: tasks } = useQuery(['tasks'], () => 
  supabase.from('tasks').select('*')
);
```

### **3. Direct Supabase for Simple Operations**
```typescript
// ✅ ALWAYS: Direct Supabase for CRUD
const createTask = async (taskData) => {
  const { data, error } = await supabase
    .from('tasks')
    .insert(taskData)
    .select()
    .single();
  
  if (error) throw error;
  return data;
};

// ❌ NEVER: Edge Functions for simple CRUD
const createTask = async (taskData) => {
  return apiCall('/tasks', { method: 'POST', body: JSON.stringify(taskData) });
};
```

---

## 🏗️ **IMPLEMENTATION PATTERNS**

### **Pattern 1: Smart Hooks Structure**

```typescript
// hooks/smart/useSmartTaskCompletion.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAppStore } from '../../stores/useAppStore';
import { useUIStore } from '../../stores/useUIStore';
import { supabase } from '../../utils/supabase';

export const useSmartTaskCompletion = () => {
  // === TANSTACK QUERY: Server State ===
  const { data: tasks, isLoading } = useQuery({
    queryKey: ['tasks'],
    queryFn: () => supabase.from('tasks').select('*').then(res => res.data)
  });
  
  const { data: familySettings } = useQuery({
    queryKey: ['family-settings'],
    queryFn: () => supabase.from('family_settings').select('*').single().then(res => res.data)
  });
  
  const mutation = useMutation({
    mutationFn: (taskId: string) => 
      supabase.rpc('complete_task', { task_id: taskId }),
    onSuccess: () => {
      queryClient.invalidateQueries(['tasks']);
    }
  });
  
  // === ZUSTAND: Client State ===
  const { selectedChildId } = useAppStore();
  const { openModal, closeModal } = useUIStore();
  
  // === COMPUTED STATE ===
  const selectedChildTasks = tasks?.filter(t => t.child_id === selectedChildId) || [];
  const isCompleting = (taskId: string) => mutation.isLoading && mutation.variables === taskId;
  
  // === ACTIONS ===
  const handleCompleteTask = async (task: Task) => {
    if (familySettings?.task_verification_mode === 'strict') {
      openModal('parentPin', { taskId: task.id });
    } else {
      await mutation.mutateAsync(task.id);
    }
  };
  
  return {
    // Server data
    tasks: selectedChildTasks,
    familySettings,
    isLoading,
    
    // Client state
    selectedChildId,
    isCompleting,
    
    // Actions
    handleCompleteTask,
  };
};
```

### **Pattern 2: GraphQL Implementation**

```typescript
// graphql/client.ts
import { ApolloClient, InMemoryCache, createHttpLink } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { supabase } from '../utils/supabase';

const httpLink = createHttpLink({
  uri: `${process.env.EXPO_PUBLIC_SUPABASE_URL}/graphql/v1`,
});

const authLink = setContext(async (_, { headers }) => {
  const { data: { session } } = await supabase.auth.getSession();
  
  return {
    headers: {
      ...headers,
      authorization: session?.access_token ? `Bearer ${session.access_token}` : "",
    }
  };
});

export const graphqlClient = new ApolloClient({
  link: authLink.concat(httpLink),
  cache: new InMemoryCache(),
});

// graphql/queries.ts
export const GET_CHILD_PROFILE = `
  query GetChildProfile($childId: UUID!) {
    child(id: $childId) {
      id
      name
      avatar
      coin_balance
      level
      xp
      daily_streak
      last_activity_date
      tasks {
        id
        title
        description
        value
        status
        completed_at
        category
        difficulty
      }
      achievements {
        id
        title
        description
        icon
        unlocked_at
        coin_reward
      }
      rewards {
        id
        title
        description
        cost
        unlocked_at
      }
    }
  }
`;

// hooks/graphql/useChildProfile.ts
import { useQuery } from '@apollo/client';
import { GET_CHILD_PROFILE } from '../../graphql/queries';

export const useChildProfile = (childId: string) => {
  return useQuery(GET_CHILD_PROFILE, {
    variables: { childId },
    skip: !childId,
  });
};
```

### **Pattern 3: Atomic Component Structure**

```typescript
// components/atoms/Button.tsx
import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';

interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  disabled = false,
  size = 'medium',
}) => (
  <TouchableOpacity
    style={[
      styles.button,
      styles[variant],
      styles[size],
      disabled && styles.disabled
    ]}
    onPress={onPress}
    disabled={disabled}
  >
    <Text style={[styles.text, styles[`${variant}Text`]]}>
      {title}
    </Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  button: {
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  primary: {
    backgroundColor: '#FF6B9D',
  },
  secondary: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#FF6B9D',
  },
  danger: {
    backgroundColor: '#FF4444',
  },
  small: { paddingVertical: 8, paddingHorizontal: 16 },
  medium: { paddingVertical: 12, paddingHorizontal: 24 },
  large: { paddingVertical: 16, paddingHorizontal: 32 },
  disabled: { opacity: 0.5 },
  text: { fontWeight: '600' },
  primaryText: { color: 'white' },
  secondaryText: { color: '#FF6B9D' },
  dangerText: { color: 'white' },
});

// components/molecules/TaskCard.tsx
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';

interface TaskCardProps {
  task: Task;
  onComplete: (task: Task) => void;
  isCompleting?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onComplete,
  isCompleting = false,
}) => (
  <Card>
    <View style={styles.content}>
      <View style={styles.header}>
        <Text style={styles.title}>{task.title}</Text>
        <Text style={styles.value}>+{task.value} coins</Text>
      </View>
      {task.description && (
        <Text style={styles.description}>{task.description}</Text>
      )}
      <Button
        title={isCompleting ? "Completing..." : "Complete"}
        onPress={() => onComplete(task)}
        disabled={isCompleting}
        size="small"
      />
    </View>
  </Card>
);

const styles = StyleSheet.create({
  content: { padding: 16 },
  header: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  title: { fontSize: 16, fontWeight: '600' },
  value: { fontSize: 14, color: '#FF6B9D', fontWeight: '600' },
  description: { fontSize: 14, color: '#666', marginBottom: 12 },
});

// components/organisms/TaskList.tsx
import React from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { TaskCard } from '../molecules/TaskCard';
import { useSmartTaskCompletion } from '../../hooks/smart/useSmartTaskCompletion';

export const TaskList: React.FC = () => {
  const { tasks, handleCompleteTask, isCompleting } = useSmartTaskCompletion();
  
  return (
    <FlatList
      data={tasks}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <TaskCard
          task={item}
          onComplete={handleCompleteTask}
          isCompleting={isCompleting(item.id)}
        />
      )}
      style={styles.list}
    />
  );
};

const styles = StyleSheet.create({
  list: { flex: 1 },
});
```

---

## 🧠 **CURSOR MEMORY COMMITMENTS**

### **1. Always Use These Patterns**

#### **State Management**
```typescript
// ✅ ALWAYS: This pattern for smart hooks
export const useSmart[Feature] = () => {
  // TanStack Query for server state
  const { data, isLoading } = useQuery(['key'], queryFn);
  
  // Zustand for client state
  const { state } = useStore();
  
  // Computed state
  const computed = useMemo(() => compute(data, state), [data, state]);
  
  // Actions
  const handleAction = useCallback(async (params) => {
    // Update client state
    setState(params);
    
    // Update server state
    await mutation.mutateAsync(params);
  }, [mutation, setState]);
  
  return { data, computed, handleAction, isLoading };
};
```

#### **Component Structure**
```typescript
// ✅ ALWAYS: This component pattern
export const [Feature]Screen: React.FC = () => {
  // Smart hook for data and actions
  const { data, handleAction, isLoading } = useSmart[Feature]();
  
  // UI state from Zustand
  const { modals } = useUIStore();
  
  // Render with atomic components
  return (
    <ScreenTemplate>
      <[Feature]List data={data} onAction={handleAction} />
      <[Feature]Modal visible={modals.[feature]} />
    </ScreenTemplate>
  );
};
```

#### **GraphQL Usage**
```typescript
// ✅ ALWAYS: GraphQL for complex queries only
const { data, loading, error } = useQuery(GET_[FEATURE]_PROFILE, {
  variables: { id },
  skip: !id,
});

// ✅ ALWAYS: Direct Supabase for simple operations
const { data, error } = await supabase
  .from('table')
  .select('*')
  .eq('id', id)
  .single();
```

### **2. Never Use These Patterns**

#### **❌ NEVER: Server data in Zustand**
```typescript
// ❌ DON'T: Store server data in Zustand
const useStore = create((set) => ({
  tasks: [], // Server data
  setTasks: (tasks) => set({ tasks }),
}));
```

#### **❌ NEVER: Edge Functions for CRUD**
```typescript
// ❌ DON'T: Use Edge Functions for simple operations
const createTask = async (data) => {
  return fetch('/api/tasks', { method: 'POST', body: JSON.stringify(data) });
};
```

#### **❌ NEVER: Mixed state management**
```typescript
// ❌ DON'T: Mix patterns in components
const Component = () => {
  const [localState, setLocalState] = useState(); // Local state
  const { data } = useQuery(['key']); // TanStack Query
  const { state } = useStore(); // Zustand
  // Confusing and hard to maintain
};
```

---

## 📋 **IMPLEMENTATION CHECKLIST**

### **Week 1: Foundation**
- [ ] Install GraphQL dependencies
- [ ] Set up Apollo Client
- [ ] Create basic GraphQL schema
- [ ] Create atomic component library structure

### **Week 2: State Management**
- [ ] Consolidate Zustand stores
- [ ] Create smart hooks for main features
- [ ] Update components to use new patterns
- [ ] Remove duplicate state management

### **Week 3: GraphQL Implementation**
- [ ] Implement GraphQL for child profiles
- [ ] Create GraphQL queries for complex operations
- [ ] Update components to use GraphQL
- [ ] Add performance monitoring

### **Week 4: Optimization**
- [ ] Remove unnecessary Edge Functions
- [ ] Optimize database queries
- [ ] Add error boundaries
- [ ] Performance testing

---

## 🎯 **SUCCESS METRICS**

### **Performance**
- Child profile load time: < 400ms (currently 1000ms+)
- API calls per screen: < 3 (currently 5-8)
- Bundle size: No increase
- Memory usage: 20% reduction

### **Developer Experience**
- Consistent patterns across codebase
- Clear separation of concerns
- Type-safe operations
- Easy debugging

### **Maintainability**
- Reduced code duplication
- Clear architectural boundaries
- Comprehensive documentation
- Test coverage > 80%

---

## 💡 **REMEMBER FOR CURSOR**

1. **Always separate server and client state**
2. **Use GraphQL only for complex queries**
3. **Use direct Supabase for simple CRUD**
4. **Create smart hooks that combine both**
5. **Use atomic component design**
6. **Document patterns consistently**
7. **Monitor performance continuously**

**This guide ensures consistent, maintainable, and performant code! 🚀** 