# Verification System Documentation

## Overview
This document clarifies the verification system used in KidsCoin for task completion and security.

## Current System (Fixed)

### Parent PIN
- **What**: A PIN set by parents during onboarding (4+ characters, up to 20)
- **Purpose**: Used for task verification in "strict" mode and kiosk unlocking
- **Storage**: `family_settings.parent_pin`
- **When used**: Child wants to complete a task in strict mode
- **UI**: Secure text input, asks "Enter Parent PIN"

### How It Works
1. **Onboarding**: <PERSON><PERSON> sets a PIN when choosing "strict" verification mode
2. **Task Completion**: Child taps "Complete" on a task
3. **Verification**: <PERSON><PERSON> asks for parent PIN (not a generated code)
4. **Validation**: App checks entered PIN against `family_settings.parent_pin`
5. **Success**: Task is marked complete if PIN matches

## Verification Modes

### Strict Mode
- Requires parent PIN for every task completion
- Set during onboarding or in family settings
- PIN is persistent and reusable
- Default mode for safety

### Trusting Mode  
- No PIN required for task completion
- Children can mark tasks complete independently
- Optional for families who prefer less friction

## Previous System (Removed)

### Temporary Verification Codes ❌
- **What**: 4-digit codes generated per task
- **Storage**: `task_completion_codes` table with 5-minute expiration
- **Problem**: Conflicted with parent PIN expectations
- **Status**: Removed in favor of parent PIN system

## Database Schema

### family_settings table
```sql
- parent_id: UUID (references users.id)
- task_verification_mode: 'strict' | 'trusting' 
- parent_pin: TEXT (null for trusting mode)
- updated_at: TIMESTAMP
```

### task_completion_codes table ❌
```sql
-- This table is no longer used for task verification
-- May be removed in future cleanup
```

## Code Implementation

### Key Files
- `components/child-mode/UnifiedChildMode.tsx` - Uses parent PIN
- `screens/TaskCompletionScreen.tsx` - Uses parent PIN  
- `screens/onboarding/FamilySetupScreen.tsx` - Sets parent PIN
- `supabase/functions/family-settings/index.ts` - Stores parent PIN

### Example Usage
```typescript
// In child mode when task completion requested
if (verificationMode === 'strict') {
  // Show PIN modal
  const enteredPin = await getPinFromUser();
  
  // Validate against family settings
  if (enteredPin === familySettings.parent_pin) {
    await completeTask(taskId);
  } else {
    showError('Incorrect PIN');
  }
}
```

## User Experience

### Clear Expectations
- ✅ Onboarding tells parents they're setting a PIN for task verification
- ✅ Task completion asks for "Parent PIN" (not confusing codes)
- ✅ Same PIN works consistently across the app
- ✅ No temporary codes that expire and confuse users

### Simple Flow
1. Parent sets PIN once during setup
2. Child completes task → asks for parent PIN
3. Parent enters their PIN → task approved
4. PIN continues to work for all future tasks

## Security Notes

- PINs are stored in plain text (consider hashing for production)
- PINs are transmitted securely via HTTPS
- PIN validation happens server-side for security
- Maximum 20 characters prevents overly complex PINs

## Future Considerations

- Add PIN change functionality in settings
- Consider PIN hashing for enhanced security  
- Add failed attempt limits to prevent brute force
- Optional biometric unlock for parents