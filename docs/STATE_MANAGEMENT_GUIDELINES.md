# State Management Guidelines

## 🎯 **Core Principles**

### **1. Clear Separation of Concerns**

#### **TanStack Query (Server State)**
- ✅ **DO**: Fetch, cache, and manage server data
- ✅ **DO**: Handle loading, error, and retry states
- ✅ **DO**: Optimistic updates for mutations
- ✅ **DO**: Background refetching and cache invalidation

#### **Zustand (Client State)**
- ✅ **DO**: Manage UI state (modals, forms, navigation)
- ✅ **DO**: Store temporary user input
- ✅ **DO**: Handle app mode and preferences
- ✅ **DO**: Manage local component state

### **2. Never Duplicate Server Data**

#### **❌ WRONG - Server Data in Zustand**
```typescript
// DON'T: Store server data in Zustand
const useAppStore = create((set) => ({
  children: [], // ❌ Server data
  tasks: [], // ❌ Server data
  familySettings: null, // ❌ Server data
}));
```

#### **✅ RIGHT - Server Data in TanStack Query**
```typescript
// DO: Use TanStack Query for server data
const { data: children } = useQuery(['children'], fetchChildren);
const { data: tasks } = useQuery(['tasks'], fetchTasks);
const { data: familySettings } = useQuery(['family-settings'], fetchSettings);
```

### **3. Smart Hooks Pattern**

#### **✅ CORRECT IMPLEMENTATION**
```typescript
export const useSmartTaskCompletion = () => {
  // TANSTACK QUERY: Server state
  const { data: tasks, refetch } = useTasksDirect();
  const { data: familySettings } = useQuery(['family-settings']);
  
  // ZUSTAND: Client state only
  const { completingTaskId, startTaskCompletion } = useTaskFlowStore();
  const { openModal } = useModalActions();
  
  // Computed state (derived, not stored)
  const isCompleting = (taskId: string) => completingTaskId === taskId;
  
  return {
    // Server data from TanStack Query
    tasks,
    familySettings,
    
    // Client state from Zustand
    isCompleting,
    
    // Actions that update both
    handleTaskComplete: async (task) => {
      // Update client state
      startTaskCompletion(task.id);
      
      // Update server state
      await mutation.mutateAsync(task);
      
      // Refresh server data
      refetch();
    }
  };
};
```

## 📋 **State Ownership Matrix**

| **State Type** | **TanStack Query** | **Zustand** | **Example** |
|----------------|-------------------|-------------|-------------|
| **Server Data** | ✅ | ❌ | `tasks`, `children`, `familySettings` |
| **Loading States** | ✅ | ✅ | TanStack: `isLoading`, Zustand: `refreshing` |
| **Error States** | ✅ | ✅ | TanStack: `error`, Zustand: `uiErrors` |
| **UI State** | ❌ | ✅ | `modalOpen`, `selectedTab`, `formData` |
| **App State** | ❌ | ✅ | `currentMode`, `selectedChildId` |
| **Temporary Input** | ❌ | ✅ | `draftTitle`, `pinCode` |
| **Computed State** | ✅ | ❌ | `taskStats`, `completionRate` |

## 🏗️ **Store Structure Guidelines**

### **AppStore (Global Client State)**
```typescript
interface AppState {
  // Navigation
  currentMode: 'parent' | 'kid';
  selectedChildId: string | null; // Just the ID, not the object
  
  // Tutorial
  tutorialStep: number;
  tutorialCompleted: boolean;
  
  // Settings
  soundEnabled: boolean;
  hapticsEnabled: boolean;
}
```

### **UIStore (UI-Specific Client State)**
```typescript
interface UIState {
  // Modals
  modals: Record<ModalId, ModalState>;
  
  // Forms (temporary user input)
  forms: FormData;
  
  // Loading/Error feedback
  loading: Record<string, boolean>;
  errors: Record<string, string | null>;
  
  // Temporary UI state
  refreshing: boolean;
  completing: Record<string, boolean>;
}
```

### **TaskFlowStore (Workflow Client State)**
```typescript
interface TaskFlowState {
  // Task completion workflow
  completingTaskId: string | null;
  currentTaskForVerification: Task | null;
  
  // Task selection (UI state)
  selectedTasks: string[];
  bulkOperationMode: boolean;
  
  // Approvals (client-side tracking)
  pendingApprovals: PendingApproval[];
}
```

## 🚫 **Anti-Patterns to Avoid**

### **1. Server Data in Zustand**
```typescript
// ❌ DON'T
const useAppStore = create((set) => ({
  children: [], // Server data
  setChildren: (children) => set({ children }),
}));
```

### **2. Loading States in Wrong Place**
```typescript
// ❌ DON'T: Server loading in Zustand
const useAppStore = create((set) => ({
  loadingChildren: false, // Should be in TanStack Query
}));

// ✅ DO: Use TanStack Query loading
const { isLoading: loadingChildren } = useQuery(['children']);
```

### **3. Duplicate State Management**
```typescript
// ❌ DON'T: Manage same data in both places
const { data: tasks } = useQuery(['tasks']); // TanStack
const { tasks } = useTaskStore(); // Zustand - duplicate!

// ✅ DO: Single source of truth
const { data: tasks } = useQuery(['tasks']); // Only TanStack
```

### **4. Async Logic in Zustand**
```typescript
// ❌ DON'T: Complex async in Zustand
const useAppStore = create((set, get) => ({
  fetchTasks: async () => {
    const response = await api.getTasks();
    set({ tasks: response.data }); // Server data in Zustand!
  }
}));

// ✅ DO: Use TanStack Query
const { data: tasks } = useQuery(['tasks'], api.getTasks);
```

## ✅ **Best Practices**

### **1. Smart Hooks Pattern**
```typescript
// ✅ DO: Combine both in smart hooks
export const useSmartChildSelection = () => {
  // TanStack Query: Server state
  const { data: children, isLoading } = useChildrenDirect();
  
  // Zustand: Client state
  const selectedChildId = useSelectedChildId();
  
  // Computed: Derived state
  const selectedChild = children.find(c => c.id === selectedChildId);
  
  return {
    children, // Server data
    selectedChildId, // Client state
    selectedChild, // Computed
    isLoading, // Server loading
  };
};
```

### **2. Clear Documentation**
```typescript
/**
 * Smart hook that properly separates TanStack Query (server state) from Zustand (client state)
 * 
 * TANSTACK QUERY (Server State):
 * - tasks: Fetched from server, cached by TanStack Query
 * - familySettings: Fetched from server, cached by TanStack Query
 * 
 * ZUSTAND (Client State):
 * - completingTaskId: UI state for showing loading
 * - modal states: UI state for modals
 * - form data: Temporary form state
 */
```

### **3. Co-locate Stores**
```typescript
// ✅ DO: Keep stores close to components when possible
// stores/features/task-flow/useTaskFlowStore.ts
// stores/features/child-selection/useChildSelectionStore.ts
```

### **4. Use Persistence Sparingly**
```typescript
// ✅ DO: Only persist truly global client state
import { persist } from 'zustand/middleware';

const useAppStore = create(
  persist(
    (set) => ({
      soundEnabled: true, // ✅ Persist user preferences
      tutorialCompleted: false, // ✅ Persist tutorial state
    }),
    {
      name: 'app-storage',
    }
  )
);

// ❌ DON'T: Persist server data
const useAppStore = create(
  persist(
    (set) => ({
      children: [], // ❌ Don't persist server data
      tasks: [], // ❌ Don't persist server data
    }),
    { name: 'app-storage' }
  )
);
```

## 🔍 **Audit Checklist**

Before implementing any state management:

- [ ] **Is this server data?** → Use TanStack Query
- [ ] **Is this UI state?** → Use Zustand
- [ ] **Is this temporary user input?** → Use Zustand forms
- [ ] **Is this computed from server data?** → Compute in component/hook
- [ ] **Is this truly global?** → Use global Zustand store
- [ ] **Is this feature-specific?** → Use co-located Zustand store
- [ ] **Does this need persistence?** → Use Zustand persist sparingly
- [ ] **Is this async logic?** → Use TanStack Query mutations

## 📚 **Examples**

### **Task Completion Flow**
```typescript
// ✅ CORRECT: Clear separation
const useTaskCompletion = () => {
  // TanStack Query: Server state
  const { data: tasks, refetch } = useTasksDirect();
  const mutation = useOptimisticTaskCompletion();
  
  // Zustand: Client state
  const { completingTaskId, startTaskCompletion } = useTaskFlowStore();
  
  const handleComplete = async (task) => {
    startTaskCompletion(task.id); // Client state
    await mutation.mutateAsync(task); // Server state
    refetch(); // Refresh server data
  };
  
  return { tasks, completingTaskId, handleComplete };
};
```

### **Child Selection**
```typescript
// ✅ CORRECT: ID only in Zustand, full data from TanStack
const useChildSelection = () => {
  // TanStack Query: Full child data
  const { data: children } = useChildrenDirect();
  
  // Zustand: Just the ID
  const selectedChildId = useSelectedChildId();
  
  // Computed: Combine both
  const selectedChild = children.find(c => c.id === selectedChildId);
  
  return { children, selectedChildId, selectedChild };
};
```

This ensures **crystal clear separation** between server and client state, preventing the messiness that can occur when these boundaries are blurred. 