# Database Schema Reference

## Critical Tables

### `family_settings`
- `parent_id` (uuid, primary key)
- `task_verification_mode` (text: 'strict' | 'trusting')
- `updated_at` (timestamptz)
- `parent_pin` (text)

### `user_subscriptions` 
- `id` (uuid, primary key)
- `parent_id` (uuid)
- `tier` (text: 'free' | 'basic' | 'premium' | 'enterprise')
- `status` (text: 'active' | 'cancelled' | 'expired' | 'trial')
- `start_date` (timestamptz)
- `end_date` (timestamptz)

### `children`
- `id` (uuid, primary key)
- `parent_id` (uuid)
- `name` (text)
- `age` (integer)
- `avatar` (text)
- `coin_balance` (integer)
- `level` (integer)
- `xp` (integer)
- `daily_streak` (integer)
- `last_activity_date` (date)

### `tasks`
- `id` (uuid, primary key)
- `child_id` (uuid)
- `parent_id` (uuid)
- `title` (text)
- `description` (text)
- `value` (integer)
- `status` (text: 'pending' | 'pending_approval' | 'completed')
- `is_recurring` (boolean)
- `template_id` (uuid, nullable)
- `completed_at` (timestamptz, nullable)

### `rewards`
- `id` (uuid, primary key)
- `parent_id` (uuid)
- `title` (text)
- `description` (text)
- `cost` (integer)
- `category` (text)

## Important Notes

1. **ALWAYS check actual schema** before implementing database calls
2. **Use `mcp_supabase-kidscoin_list_tables`** to verify table structure
3. **Subscription data** is in `user_subscriptions`, not `family_settings`
4. **Monetization features** are controlled by `admin_monetization_config` and `admin_premium_config`
5. **Real-time access** is determined by subscription tier in `user_subscriptions`

## Common Patterns

### Check Subscription Status
```sql
SELECT tier FROM user_subscriptions 
WHERE parent_id = $1 AND status = 'active'
```

### Check Family Settings
```sql
SELECT task_verification_mode FROM family_settings 
WHERE parent_id = $1
```

### Get Child Data
```sql
SELECT name, coin_balance, level FROM children 
WHERE id = $1
``` 