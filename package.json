{"name": "app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-google-signin/google-signin": "^15.0.0", "@supabase/supabase-js": "^2.51.0", "core-js": "^3.44.0", "expo": "^53.0.20", "expo-apple-authentication": "~7.2.4", "expo-audio": "^0.4.8", "expo-auth-session": "~6.2.1", "expo-crypto": "~14.1.5", "expo-haptics": "~14.1.4", "expo-linear-gradient": "~14.1.5", "expo-status-bar": "~2.2.3", "expo-web-browser": "~14.2.0", "react": "19.0.0", "react-native": "0.79.5", "react-native-haptic-feedback": "^2.3.3", "react-native-svg": "15.11.2", "zustand": "^5.0.7"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}