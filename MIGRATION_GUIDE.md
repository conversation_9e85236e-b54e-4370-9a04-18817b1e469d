# 🔄 API MIGRATION GUIDE

## Old vs New Hook Names

### BEFORE (Confusing) → AFTER (Clean)

```typescript
// === QUERIES ===
useUserProfileDirect()      → useUserProfile()
useFamilySettingsDirect()   → useFamilySettings()
useChildrenDirect()         → useChildren()
useTasksDirect()            → useTasks()
useRewardsDirect()          → useRewards()
useUnlockedRewardsDirect()  → useUnlockedRewards()
useAchievementsDirect()     → useAchievements()
useChildAchievementsDirect() → useChildAchievements()
useTaskTemplatesDirect()    → useTaskTemplates()

useDashboardDataFast()      → useDashboardData()
useDashboardDataHybrid()    → useDashboardData()

// === SMART HOOKS (Keep as-is but import from new location) ===
useSmartTaskCompletion()    → useSmartTaskCompletion() (from hooks/smart)
useSmartChildSelection()    → useSmartChildSelection() (from hooks/smart)
useSmartRewardRedemption()  → useSmartRewardRedemption() (from hooks/smart)

// === MUTATIONS ===
Create new mutation hooks from business logic
```

## Import Migration

```typescript
// BEFORE:
import { useChildrenDirect, useFamilySettingsDirect } from '../hooks/useDirectSupabase';
import { useDashboardDataFast } from '../hooks/useSharedData';

// AFTER:
import { useChildren, useFamilySettings, useDashboardData } from '../hooks/api';
```

## Files to Remove After Migration

- `hooks/useDirectSupabase.ts` (consolidated into api/)
- `hooks/useSharedData.ts` (consolidated into api/)
- `hooks/useTaskCompletionData.ts` (consolidated into api/)
- `hooks/useChildProfileData.ts` (specialized version kept)

## Query Key Standardization

```typescript
// OLD (inconsistent):
['dashboard-hybrid']
['children-direct']
['family-settings-direct']
['tasks-direct']

// NEW (clean):
['dashboard']
['children']
['family-settings']
['tasks']
```