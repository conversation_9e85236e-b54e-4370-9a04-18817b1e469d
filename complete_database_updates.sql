-- Complete Database Updates for Kids<PERSON>oin App
-- This file contains all the database changes needed to set up the KidsCoin app

-- 1. Apply all migrations in order
\i migrations/20250117_000001_create_initial_schema.sql
\i migrations/20250117_000002_create_database_functions.sql
\i migrations/20250117_000003_add_monetization_features.sql
\i migrations/20250117_000004_remove_legacy_monetization.sql

-- 2. Insert sample data for testing
INSERT INTO users (id, email, full_name, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440000', '<EMAIL>', 'Test Parent', NOW()),
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'Test Child', NOW())
ON CONFLICT (id) DO NOTHING;

-- 3. Insert family settings
INSERT INTO family_settings (parent_id, parent_pin, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440000', '1234', NOW())
ON CONFLICT (parent_id) DO NOTHING;

-- 4. Insert sample children
INSERT INTO children (id, parent_id, name, age, avatar, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440000', 'Alex', 8, '😊', NOW()),
('550e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440000', 'Sam', 5, '🏠', NOW())
ON CONFLICT (id) DO NOTHING;

-- 5. Insert sample tasks
INSERT INTO tasks (id, parent_id, child_id, title, description, coins, category, due_date, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440002', 'Clean Room', 'Pick up toys and make bed', 10, 'chores', NOW() + INTERVAL '1 day', NOW()),
('550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440003', 'Read Book', 'Read for 20 minutes', 15, 'education', NOW() + INTERVAL '2 days', NOW())
ON CONFLICT (id) DO NOTHING;

-- 6. Insert sample rewards
INSERT INTO rewards (id, parent_id, child_id, title, description, coins, category, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440002', 'Extra Screen Time', '30 minutes of extra screen time', 25, 'entertainment', NOW()),
('550e8400-e29b-41d4-a716-446655440007', '550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440003', 'Special Treat', 'Choose a special dessert', 20, 'food', NOW())
ON CONFLICT (id) DO NOTHING;

-- 7. Insert sample task completions
INSERT INTO task_completions (id, task_id, child_id, completed_at, coins_earned, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440008', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', NOW() - INTERVAL '1 hour', 10, NOW())
ON CONFLICT (id) DO NOTHING;

-- 8. Insert sample reward redemptions
INSERT INTO reward_redemptions (id, reward_id, child_id, redeemed_at, coins_spent, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440009', '550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440002', NOW() - INTERVAL '30 minutes', 25, NOW())
ON CONFLICT (id) DO NOTHING;

-- 9. Insert sample child settings
INSERT INTO child_settings (child_id, theme, sound_enabled, notifications_enabled, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440002', 'default', true, true, NOW()),
('550e8400-e29b-41d4-a716-446655440003', 'default', true, true, NOW())
ON CONFLICT (child_id) DO NOTHING;

-- 10. Insert sample learning patterns
INSERT INTO learning_patterns (parent_id, child_id, task_category_preferences, reward_category_preferences, average_task_completion_rate, average_reward_redemption_rate, total_tasks_completed, total_rewards_redeemed, last_updated, created_at) VALUES
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440002', '{"chores": 0.6, "education": 0.4}', '{"entertainment": 0.7, "food": 0.3}', 0.85, 0.9, 15, 8, NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440003', '{"education": 0.8, "chores": 0.2}', '{"food": 0.6, "entertainment": 0.4}', 0.75, 0.8, 12, 6, NOW(), NOW())
ON CONFLICT (parent_id, child_id) DO NOTHING;

-- 11. Insert sample admin monetization config
INSERT INTO admin_monetization_config (id, monetization_enabled, free_tier_limits, tiers, feature_access, usage_limits, pricing, admin_overrides, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440010', true, '{"children": 1, "realtime": false, "ai_generation": 5, "library_access": true}', '{"basic": {"price": 4.99, "limits": {"children": 3, "realtime": false, "ai_generation": 20, "library_access": true}}, "premium": {"price": 9.99, "limits": {"children": 10, "realtime": true, "ai_generation": 100, "library_access": true}}}', '{"ai_task_generation": "premium", "ai_reward_generation": "premium", "personalized_suggestions": "premium", "advanced_analytics": "premium"}', '{"ai_generation_monthly": 100, "realtime_sync": true}', '{"currency": "USD", "display_prices": true}', '{"override_user_limits": false, "grant_premium_access": false}', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 12. Insert sample user subscriptions
INSERT INTO user_subscriptions (id, parent_id, tier, purchase_type, status, purchase_token, features, expires_at, auto_renew, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-446655440000', 'basic', 'one_time', 'active', 'test_token_123', '{"children": 3, "realtime": false, "ai_generation": 20, "library_access": true}', NULL, false, NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 13. Insert sample usage tracking
INSERT INTO usage_tracking (id, parent_id, feature_name, usage_count, last_used, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440000', 'ai_task_generation', 5, NOW(), NOW(), NOW()),
('550e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440000', 'ai_reward_generation', 3, NOW(), NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 14. Insert sample admin overrides
INSERT INTO admin_overrides (id, parent_id, feature_name, override_type, override_value, reason, expires_at, created_at, updated_at) VALUES
('550e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440000', 'ai_generation', 'limit_increase', 50, 'Beta tester bonus', NOW() + INTERVAL '30 days', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- 15. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_children_parent_id ON children(parent_id);
CREATE INDEX IF NOT EXISTS idx_tasks_parent_id ON tasks(parent_id);
CREATE INDEX IF NOT EXISTS idx_tasks_child_id ON tasks(child_id);
CREATE INDEX IF NOT EXISTS idx_rewards_parent_id ON rewards(parent_id);
CREATE INDEX IF NOT EXISTS idx_rewards_child_id ON rewards(child_id);
CREATE INDEX IF NOT EXISTS idx_task_completions_task_id ON task_completions(task_id);
CREATE INDEX IF NOT EXISTS idx_task_completions_child_id ON task_completions(child_id);
CREATE INDEX IF NOT EXISTS idx_reward_redemptions_reward_id ON reward_redemptions(reward_id);
CREATE INDEX IF NOT EXISTS idx_reward_redemptions_child_id ON reward_redemptions(child_id);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_parent_id ON learning_patterns(parent_id);
CREATE INDEX IF NOT EXISTS idx_learning_patterns_child_id ON learning_patterns(child_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_parent_id ON user_subscriptions(parent_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_parent_id ON usage_tracking(parent_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_feature ON usage_tracking(feature_name);
CREATE INDEX IF NOT EXISTS idx_admin_overrides_parent_id ON admin_overrides(parent_id);
CREATE INDEX IF NOT EXISTS idx_admin_overrides_feature ON admin_overrides(feature_name); 