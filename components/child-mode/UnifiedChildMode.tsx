/**
 * 🎯 SIMPLIFIED UNIFIED CHILD MODE
 * 
 * Uses simplified hooks instead of complex TanStack Query patterns
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../../contexts/AuthContext';
import { useAudio } from '../../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar,
  NintendoInput 
} from '../ui';
import { useChildren, useTasks, useRewards } from '../../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  parent_id: string;
  child_id: string;
  created_at: string;
  completed_at?: string;
  child_name?: string;
  child_avatar?: string;
  is_recurring?: boolean;
}

interface Reward {
  id: string;
  title: string;
  description?: string;
  cost: number;
  category?: string;
  parent_id: string;
}

interface UnifiedChildModeProps {
  onBack: () => void;
}

const UnifiedChildMode: React.FC<UnifiedChildModeProps> = ({ onBack }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // Simplified hooks
  const { children, loading: loadingChildren } = useChildren();
  const { tasks, loading: loadingTasks, completeTask } = useTasks();
  const { rewards, loading: loadingRewards } = useRewards();
  
  // Current child selection
  const [currentChild, setCurrentChild] = useState<Child | null>(null);
  const [currentTab, setCurrentTab] = useState<'tasks' | 'rewards' | 'profile'>('tasks');
  
  // Task completion state
  const [showVerificationModal, setShowVerificationModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  const [completing, setCompleting] = useState<string | null>(null);
  
  // Reward redemption state
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [showRewardModal, setShowRewardModal] = useState(false);
  const [redeeming, setRedeeming] = useState(false);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const celebrationScale = useRef(new Animated.Value(0)).current;
  
  // Auto-select first child
  useEffect(() => {
    if (children && children.length > 0 && !currentChild) {
      setCurrentChild(children[0]);
    }
  }, [children, currentChild]);
  
  // Entrance animation
  useEffect(() => {
    Animated.timing(fadeInAnim, {
      toValue: 1,
      duration: 600,
      useNativeDriver: true,
    }).start();
  }, []);
  
  const handleChildSelect = (child: Child) => {
    playSound('selection_change');
    setCurrentChild(child);
  };
  
  const handleTabSelect = (tab: 'tasks' | 'rewards' | 'profile') => {
    playSound('button_press');
    setCurrentTab(tab);
  };
  
  // Task completion logic
  const handleTaskComplete = async (task: Task) => {
    setCompleting(task.id);
    playSound('button_press');
    
    try {
      await completeTask(task.id);
      
      // Celebration animation
      Animated.spring(celebrationScale, {
        toValue: 1,
        tension: 50,
        friction: 4,
        useNativeDriver: true,
      }).start(() => {
        setTimeout(() => {
          Animated.spring(celebrationScale, {
            toValue: 0,
            tension: 80,
            friction: 6,
            useNativeDriver: true,
          }).start();
        }, 2000);
      });
      
      playSound('coin_collect');
      
      Alert.alert(
        'Task Completed! 🎉',
        `Great job! You earned ${task.value} coins for "${task.title}"!`,
        [{ text: 'Awesome!', onPress: () => {} }]
      );
      
    } catch (error) {
      console.error('Error completing task:', error);
      Alert.alert('Error', 'Failed to complete task. Please try again.');
    } finally {
      setCompleting(null);
    }
  };
  
  // Reward redemption logic
  const handleRewardPress = (reward: Reward) => {
    if (!currentChild) return;
    
    if (currentChild.coin_balance < reward.cost) {
      Alert.alert(
        'Not Enough Coins',
        `You need ${reward.cost} coins to get this reward. You have ${currentChild.coin_balance} coins.`,
        [{ text: 'OK' }]
      );
      return;
    }
    
    setSelectedReward(reward);
    setShowRewardModal(true);
    playSound('button_press');
  };
  
  const handleRewardRedeem = async () => {
    if (!selectedReward || !currentChild) return;
    
    setRedeeming(true);
    setShowRewardModal(false);
    playSound('button_press');
    
    try {
      // This would use the redeemReward function from useRewards hook
      Alert.alert('Reward Redeemed! 🎉', `You got "${selectedReward.title}"!`);
    } catch (error) {
      console.error('Error redeeming reward:', error);
      Alert.alert('Error', 'Failed to redeem reward. Please try again.');
    } finally {
      setRedeeming(false);
    }
  };
  
  const loading = loadingChildren || loadingTasks || loadingRewards;
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Loading...</Text>
      </View>
    );
  }
  
  if (!children || children.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No children found</Text>
        <NintendoButton onPress={onBack} title="Go Back" />
      </View>
    );
  }
  
  return (
    <Animated.View style={[styles.container, { opacity: fadeInAnim }]}>
      <LinearGradient
        colors={['#FF6B9D', '#FF8E53']}
        style={styles.gradient}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Child Mode</Text>
        </View>
        
        {/* Child Selection */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.childSelector}>
          {children.map((child) => (
            <TouchableOpacity
              key={child.id}
              onPress={() => handleChildSelect(child)}
              style={[
                styles.childCard,
                currentChild?.id === child.id && styles.selectedChildCard
              ]}
            >
              <Text style={styles.childName}>{child.name}</Text>
              <Text style={styles.childCoins}>{child.coin_balance} coins</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        {/* Content */}
        {currentChild && (
          <View style={styles.content}>
            {/* Tab Navigation */}
            <View style={styles.tabContainer}>
              {(['tasks', 'rewards', 'profile'] as const).map((tab) => (
                <TouchableOpacity
                  key={tab}
                  onPress={() => handleTabSelect(tab)}
                  style={[
                    styles.tab,
                    currentTab === tab && styles.activeTab
                  ]}
                >
                  <Text style={[
                    styles.tabText,
                    currentTab === tab && styles.activeTabText
                  ]}>
                    {tab.charAt(0).toUpperCase() + tab.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
            
            {/* Tab Content */}
            <ScrollView style={styles.tabContent}>
              {currentTab === 'tasks' && (
                <View>
                  <Text style={styles.sectionTitle}>Available Tasks</Text>
                  {tasks
                    .filter(task => task.child_id === currentChild.id && task.status === 'pending')
                    .map((task) => (
                      <NintendoCard key={task.id} style={styles.taskCard}>
                        <Text style={styles.taskTitle}>{task.title}</Text>
                        <Text style={styles.taskDescription}>{task.description}</Text>
                        <Text style={styles.taskValue}>{task.value} coins</Text>
                        <NintendoButton
                          onPress={() => handleTaskComplete(task)}
                          title="Complete"
                          disabled={completing === task.id}
                        />
                      </NintendoCard>
                    ))}
                </View>
              )}
              
              {currentTab === 'rewards' && (
                <View>
                  <Text style={styles.sectionTitle}>Available Rewards</Text>
                  {rewards.map((reward) => (
                    <NintendoCard key={reward.id} style={styles.rewardCard}>
                      <Text style={styles.rewardTitle}>{reward.title}</Text>
                      <Text style={styles.rewardDescription}>{reward.description}</Text>
                      <Text style={styles.rewardCost}>{reward.cost} coins</Text>
                      <NintendoButton
                        onPress={() => handleRewardPress(reward)}
                        title="Redeem"
                        disabled={currentChild.coin_balance < reward.cost}
                      />
                    </NintendoCard>
                  ))}
                </View>
              )}
              
              {currentTab === 'profile' && (
                <View>
                  <Text style={styles.sectionTitle}>Child Profile</Text>
                  <NintendoCard style={styles.profileCard}>
                    <Text style={styles.profileName}>{currentChild.name}</Text>
                    <Text style={styles.profileStats}>
                      Level: {currentChild.level} | XP: {currentChild.xp}
                    </Text>
                    <Text style={styles.profileStats}>
                      Coins: {currentChild.coin_balance} | Streak: {currentChild.daily_streak} days
                    </Text>
                  </NintendoCard>
                </View>
              )}
            </ScrollView>
          </View>
        )}
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
  },
  title: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  childSelector: {
    paddingHorizontal: 20,
  },
  childCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    padding: 15,
    marginRight: 10,
    borderRadius: 10,
    minWidth: 100,
  },
  selectedChildCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  childName: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  childCoins: {
    color: 'white',
    fontSize: 14,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  tabContainer: {
    flexDirection: 'row',
    marginBottom: 20,
  },
  tab: {
    flex: 1,
    padding: 15,
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginHorizontal: 5,
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  tabText: {
    color: 'white',
    fontSize: 16,
  },
  activeTabText: {
    fontWeight: 'bold',
  },
  tabContent: {
    flex: 1,
  },
  sectionTitle: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  taskCard: {
    marginBottom: 15,
    padding: 15,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  taskValue: {
    fontSize: 16,
    color: '#FF6B9D',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  rewardCard: {
    marginBottom: 15,
    padding: 15,
  },
  rewardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  rewardCost: {
    fontSize: 16,
    color: '#FF6B9D',
    fontWeight: 'bold',
    marginBottom: 10,
  },
  profileCard: {
    padding: 20,
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  profileStats: {
    fontSize: 16,
    marginBottom: 5,
  },
});

export default UnifiedChildMode;
