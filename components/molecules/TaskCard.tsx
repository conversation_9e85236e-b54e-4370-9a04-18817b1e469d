import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Button } from '../atoms/Button';
import { Card } from '../atoms/Card';

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: string;
}

interface TaskCardProps {
  task: Task;
  onComplete: (task: Task) => void;
  isCompleting?: boolean;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onComplete,
  isCompleting = false,
}) => (
  <Card>
    <View style={styles.content}>
      <View style={styles.header}>
        <Text style={styles.title}>{task.title}</Text>
        <Text style={styles.value}>+{task.value} coins</Text>
      </View>
      {task.description && (
        <Text style={styles.description}>{task.description}</Text>
      )}
      <Button
        title={isCompleting ? "Completing..." : "Complete"}
        onPress={() => onComplete(task)}
        disabled={isCompleting}
        size="small"
      />
    </View>
  </Card>
);

const styles = StyleSheet.create({
  content: { padding: 16 },
  header: { flexDirection: 'row', justifyContent: 'space-between', marginBottom: 8 },
  title: { fontSize: 16, fontWeight: '600' },
  value: { fontSize: 14, color: '#FF6B9D', fontWeight: '600' },
  description: { fontSize: 14, color: '#666', marginBottom: 12 },
}); 