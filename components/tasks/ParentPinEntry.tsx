import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Modal,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface ParentPinEntryProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  taskTitle: string;
  childName: string;
  coinValue: number;
  correctPin: string; // In production, this would come from family settings
}

const ParentPinEntry: React.FC<ParentPinEntryProps> = ({
  visible,
  onClose,
  onSuccess,
  taskTitle,
  childName,
  coinValue,
  correctPin = '1234', // Default PIN for demo
}) => {
  const [enteredPin, setEnteredPin] = useState('');
  const [isCompleting, setIsCompleting] = useState(false);
  const [showCelebration, setShowCelebration] = useState(false);
  const [error, setError] = useState(false);

  // Animation values
  const slideAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const celebrationScale = useRef(new Animated.Value(0)).current;
  const coinBounce = useRef(new Animated.Value(1)).current;
  const sparkleRotation = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      setEnteredPin('');
      setError(false);
      setIsCompleting(false);
      setShowCelebration(false);
      
      // Slide up animation
      Animated.spring(slideAnim, {
        toValue: 0,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();

      // Continuous sparkle rotation
      Animated.loop(
        Animated.timing(sparkleRotation, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        })
      ).start();
    } else {
      // Reset animations
      slideAnim.setValue(SCREEN_HEIGHT);
      celebrationScale.setValue(0);
      coinBounce.setValue(1);
    }
  }, [visible]);

  const handleNumberPress = (number: string) => {
    if (enteredPin.length < 4) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      const newPin = enteredPin + number;
      setEnteredPin(newPin);
      setError(false);

      // Check if complete
      if (newPin.length === 4) {
        if (newPin === correctPin) {
          handleCorrectPin();
        } else {
          handleWrongPin();
        }
      }
    }
  };

  const handleCorrectPin = async () => {
    setIsCompleting(true);
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

    // Start celebration animation
    setShowCelebration(true);
    Animated.parallel([
      Animated.spring(celebrationScale, {
        toValue: 1,
        useNativeDriver: true,
        tension: 100,
        friction: 6,
      }),
      Animated.sequence([
        Animated.timing(coinBounce, {
          toValue: 1.2,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(coinBounce, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]),
    ]).start();

    // Multiple celebration haptics
    setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy), 300);
    setTimeout(() => Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium), 600);

    // Close after celebration
    setTimeout(() => {
      onSuccess();
      setTimeout(onClose, 500);
    }, 2000);
  };

  const handleWrongPin = () => {
    setError(true);
    setEnteredPin('');
    Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

    // Shake animation for error
    Animated.sequence([
      Animated.timing(slideAnim, { toValue: -10, duration: 50, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: 10, duration: 50, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: -5, duration: 50, useNativeDriver: true }),
      Animated.timing(slideAnim, { toValue: 0, duration: 50, useNativeDriver: true }),
    ]).start();

    setTimeout(() => setError(false), 1000);
  };

  const handleDelete = () => {
    if (enteredPin.length > 0) {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      setEnteredPin(enteredPin.slice(0, -1));
      setError(false);
    }
  };

  const renderNumberPad = () => {
    const numbers = [
      ['1', '2', '3'],
      ['4', '5', '6'],
      ['7', '8', '9'],
      ['', '0', '⌫'],
    ];

    return (
      <View style={styles.numberPad}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((num, colIndex) => (
              <TouchableOpacity
                key={colIndex}
                style={[
                  styles.numberButton,
                  num === '' && styles.numberButtonEmpty,
                ]}
                onPress={() => {
                  if (num === '⌫') {
                    handleDelete();
                  } else if (num !== '') {
                    handleNumberPress(num);
                  }
                }}
                disabled={num === '' || isCompleting}
              >
                <Text style={styles.numberButtonText}>{num}</Text>
              </TouchableOpacity>
            ))}
          </View>
        ))}
      </View>
    );
  };

  const renderPinDots = () => {
    return (
      <View style={styles.pinContainer}>
        {[0, 1, 2, 3].map((index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < enteredPin.length && styles.pinDotFilled,
              error && styles.pinDotError,
            ]}
          />
        ))}
      </View>
    );
  };

  const sparkleRotationInterpolate = sparkleRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <Modal visible={visible} transparent animationType="none">
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.container,
            { transform: [{ translateY: slideAnim }] },
          ]}
        >
          <LinearGradient
            colors={['#667eea', '#764ba2', '#f093fb']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.gradient}
          >
            {!showCelebration ? (
              <>
                {/* Header */}
                <View style={styles.header}>
                  <Animated.Text
                    style={[
                      styles.sparkles,
                      { transform: [{ rotate: sparkleRotationInterpolate }] },
                    ]}
                  >
                    ✨
                  </Animated.Text>
                  <Text style={styles.title}>Parent Approval</Text>
                  <View style={styles.taskInfo}>
                    <Text style={styles.taskTitle}>{taskTitle}</Text>
                    <Text style={styles.childName}>by {childName}</Text>
                    <View style={styles.coinInfo}>
                      <Text style={styles.coinValue}>+{coinValue}</Text>
                      <Text style={styles.coinLabel}>coins</Text>
                    </View>
                  </View>
                </View>

                {/* PIN Entry */}
                <View style={styles.pinSection}>
                  <Text style={styles.pinInstruction}>
                    Enter your PIN to approve this task
                  </Text>
                  {renderPinDots()}
                  {error && (
                    <Text style={styles.errorText}>
                      Wrong PIN! Try again 🔒
                    </Text>
                  )}
                </View>

                {/* Number Pad */}
                {renderNumberPad()}

                {/* Cancel Button */}
                <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                  <Text style={styles.cancelButtonText}>Cancel</Text>
                </TouchableOpacity>
              </>
            ) : (
              /* Celebration Screen */
              <Animated.View
                style={[
                  styles.celebrationContainer,
                  { transform: [{ scale: celebrationScale }] },
                ]}
              >
                <Text style={styles.celebrationEmoji}>
                  {taskTitle === 'Parent Mode Access' ? '🔓' : 
                   taskTitle.includes('Settings') ? '⚙️' : '🎉'}
                </Text>
                <Text style={styles.celebrationTitle}>
                  {taskTitle === 'Parent Mode Access' ? 'Access Granted!' :
                   taskTitle.includes('Settings') ? 'Settings Updated!' :
                   'Task Completed!'}
                </Text>
                <Text style={styles.celebrationSubtitle}>
                  {taskTitle === 'Parent Mode Access' ? 'Switching to Parent Mode...' :
                   taskTitle.includes('Settings') ? 'Your changes have been saved' :
                   `${childName} earned ${coinValue} coins! 🪙`}
                </Text>
                {coinValue > 0 && (
                  <Animated.Text
                    style={[
                      styles.celebrationCoins,
                      { transform: [{ scale: coinBounce }] },
                    ]}
                  >
                    +{coinValue} 🪙
                  </Animated.Text>
                )}
                <Text style={styles.celebrationMessage}>
                  {taskTitle === 'Parent Mode Access' ? 'Welcome back, Parent! 👋' :
                   taskTitle.includes('Settings') ? 'Changes applied successfully! ✅' :
                   'Great job! Keep up the awesome work! ⭐'}
                </Text>
              </Animated.View>
            )}
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    width: SCREEN_WIDTH * 0.9,
    maxHeight: SCREEN_HEIGHT * 0.8,
    borderRadius: 24,
    overflow: 'hidden',
  },
  gradient: {
    padding: 24,
    alignItems: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  sparkles: {
    fontSize: 32,
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  taskInfo: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 16,
    minWidth: 200,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 4,
  },
  childName: {
    fontSize: 14,
    color: '#E0E0E0',
    marginBottom: 8,
  },
  coinInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  coinValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4ECDC4',
  },
  coinLabel: {
    fontSize: 16,
    color: '#4ECDC4',
  },
  pinSection: {
    alignItems: 'center',
    marginBottom: 32,
  },
  pinInstruction: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  pinContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  pinDot: {
    width: 16,
    height: 16,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.5)',
  },
  pinDotFilled: {
    backgroundColor: '#4ECDC4',
    borderColor: '#4ECDC4',
  },
  pinDotError: {
    backgroundColor: '#FF6B9D',
    borderColor: '#FF6B9D',
  },
  errorText: {
    fontSize: 14,
    color: '#FF6B9D',
    fontWeight: '600',
  },
  numberPad: {
    gap: 16,
    marginBottom: 24,
  },
  numberRow: {
    flexDirection: 'row',
    gap: 24,
  },
  numberButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  numberButtonEmpty: {
    backgroundColor: 'transparent',
    borderColor: 'transparent',
  },
  numberButtonText: {
    fontSize: 24,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  cancelButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  celebrationContainer: {
    alignItems: 'center',
    padding: 40,
  },
  celebrationEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  celebrationTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  celebrationSubtitle: {
    fontSize: 18,
    color: '#E0E0E0',
    marginBottom: 24,
    textAlign: 'center',
  },
  celebrationCoins: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#4ECDC4',
    marginBottom: 16,
  },
  celebrationMessage: {
    fontSize: 16,
    color: '#FFFFFF',
    textAlign: 'center',
         lineHeight: 24,
   },
 });

export default ParentPinEntry; 