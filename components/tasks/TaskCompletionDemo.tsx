import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import ParentPinEntry from './ParentPinEntry';
import { useRealtime } from '../../contexts/RealtimeContext';

interface Task {
  id: string;
  title: string;
  description: string;
  value: number;
  status: 'pending' | 'completed';
  child: {
    id: string;
    name: string;
    avatar: string;
  };
}

const TaskCompletionDemo: React.FC = () => {
  const { taskUpdates, isConnected } = useRealtime();
  const [showPinEntry, setShowPinEntry] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);

  // Demo tasks
  const [tasks, setTasks] = useState<Task[]>([
    {
      id: '1',
      title: 'Make the bed',
      description: 'Tidy up bedroom and make bed neat',
      value: 5,
      status: 'pending',
      child: { id: 'child1', name: '<PERSON>', avatar: '😊' },
    },
    {
      id: '2',
      title: 'Feed the dog',
      description: 'Give Buddy his morning meal',
      value: 3,
      status: 'pending',
      child: { id: 'child1', name: 'Emma', avatar: '😊' },
    },
    {
      id: '3',
      title: 'Set the table',
      description: 'Set plates and utensils for dinner',
      value: 4,
      status: 'pending',
      child: { id: 'child2', name: 'Alex', avatar: '😎' },
    },
  ]);

  const handleTaskPress = (task: Task) => {
    if (task.status === 'completed') return;
    
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    setSelectedTask(task);
    setShowPinEntry(true);
  };

  const handlePinSuccess = () => {
    if (!selectedTask) return;

    // Simulate task completion
    setTasks(prev => 
      prev.map(task => 
        task.id === selectedTask.id 
          ? { ...task, status: 'completed' as const }
          : task
      )
    );

    // Simulate celebration broadcast (in real app, this comes from the API)
    setTimeout(() => {
      Alert.alert(
        '🎉 Task Completed!',
        `${selectedTask.child.name} earned ${selectedTask.value} coins for "${selectedTask.title}"!`,
        [{ text: 'Awesome!', style: 'default' }]
      );
    }, 100);

    setSelectedTask(null);
  };

  const renderTask = (task: Task) => (
    <TouchableOpacity
      key={task.id}
      style={[
        styles.taskCard,
        task.status === 'completed' && styles.taskCardCompleted
      ]}
      onPress={() => handleTaskPress(task)}
      disabled={task.status === 'completed'}
    >
      <View style={styles.taskHeader}>
        <View style={styles.taskInfo}>
          <Text style={[
            styles.taskTitle,
            task.status === 'completed' && styles.taskTitleCompleted
          ]}>
            {task.title}
          </Text>
          <Text style={styles.taskChild}>
            {task.child.avatar} {task.child.name}
          </Text>
        </View>
        <View style={styles.taskValue}>
          <Text style={styles.coinValue}>+{task.value}</Text>
          <Text style={styles.coinLabel}>coins</Text>
        </View>
      </View>
      
      {task.description && (
        <Text style={styles.taskDescription}>{task.description}</Text>
      )}
      
      <View style={styles.taskStatus}>
        {task.status === 'pending' ? (
          <Text style={styles.statusPending}>Tap to complete with PIN 📱</Text>
        ) : (
          <Text style={styles.statusCompleted}>✅ Completed!</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderRealtimeUpdates = () => {
    if (taskUpdates.length === 0) return null;

    return (
      <View style={styles.updatesSection}>
        <Text style={styles.updatesTitle}>
          🔄 Real-time Updates {isConnected ? '🟢' : '🔴'}
        </Text>
        {taskUpdates.slice(0, 3).map((update) => (
          <View key={update.id} style={styles.updateCard}>
            <Text style={styles.updateText}>
              {update.type === 'task_completed' ? '🎉' : '📝'} {update.childName} {
                update.type === 'task_completed' 
                  ? `completed "${update.taskTitle}" (+${update.coinValue} coins)`
                  : `was assigned "${update.taskTitle}"`
              }
            </Text>
            <Text style={styles.updateTime}>
              {update.timestamp.toLocaleTimeString()}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Family Tasks</Text>
        <Text style={styles.headerSubtitle}>
          Strict Mode Demo - Parent PIN Required 🔒
        </Text>
      </LinearGradient>

      <View style={styles.content}>
        {/* Connection Status */}
        <View style={styles.statusBar}>
          <Text style={styles.statusText}>
            Real-time sync: {isConnected ? '🟢 Connected' : '🔴 Disconnected'}
          </Text>
        </View>

        {/* Real-time Updates */}
        {renderRealtimeUpdates()}

        {/* Tasks List */}
        <View style={styles.tasksSection}>
          <Text style={styles.sectionTitle}>Pending Tasks</Text>
          {tasks.filter(task => task.status === 'pending').map(renderTask)}
        </View>

        {tasks.some(task => task.status === 'completed') && (
          <View style={styles.tasksSection}>
            <Text style={styles.sectionTitle}>Completed Tasks</Text>
            {tasks.filter(task => task.status === 'completed').map(renderTask)}
          </View>
        )}

        {/* Demo Instructions */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🎮 How it works:</Text>
          <Text style={styles.infoText}>• Tap any pending task</Text>
          <Text style={styles.infoText}>• Enter PIN: 1234</Text>
          <Text style={styles.infoText}>• Watch the celebration!</Text>
          <Text style={styles.infoText}>• Updates sync across all devices instantly</Text>
        </View>
      </View>

      {/* PIN Entry Modal */}
      {selectedTask && (
        <ParentPinEntry
          visible={showPinEntry}
          onClose={() => {
            setShowPinEntry(false);
            setSelectedTask(null);
          }}
          onSuccess={handlePinSuccess}
          taskTitle={selectedTask.title}
          childName={selectedTask.child.name}
          coinValue={selectedTask.value}
          correctPin="1234"
        />
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  header: {
    padding: 24,
    paddingTop: 60,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#E8E8E8',
    textAlign: 'center',
  },
  content: {
    padding: 16,
  },
  statusBar: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  updatesSection: {
    marginBottom: 24,
  },
  updatesTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  updateCard: {
    backgroundColor: '#E3F2FD',
    borderRadius: 8,
    padding: 12,
    marginBottom: 8,
  },
  updateText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  updateTime: {
    fontSize: 12,
    color: '#666',
  },
  tasksSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 16,
  },
  taskCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  taskCardCompleted: {
    backgroundColor: '#F0F8F0',
    opacity: 0.8,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  taskInfo: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  taskTitleCompleted: {
    textDecorationLine: 'line-through',
    color: '#666',
  },
  taskChild: {
    fontSize: 14,
    color: '#666',
  },
  taskValue: {
    alignItems: 'center',
  },
  coinValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4ECDC4',
  },
  coinLabel: {
    fontSize: 12,
    color: '#4ECDC4',
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  taskStatus: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  statusPending: {
    fontSize: 14,
    color: '#FF6B9D',
    fontWeight: '600',
  },
  statusCompleted: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
  },
  infoSection: {
    backgroundColor: '#FFF3E0',
    borderRadius: 16,
    padding: 20,
    marginTop: 16,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
});

export default TaskCompletionDemo; 