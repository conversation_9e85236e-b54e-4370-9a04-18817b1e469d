import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  StatusBar,
  Platform,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAudio } from '../contexts/AudioContext';
import { useTutorial } from '../contexts/TutorialContext';
import { NintendoButton, NintendoCard } from './ui';
import { TutorialStep } from '../utils/tutorialSteps';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface GuidedTutorialOverlayProps {
  visible: boolean;
  currentStep: TutorialStep | null;
  onNext: () => void;
  onComplete: () => void;
  onNavigate?: (targetScreen: string) => void;
  totalSteps: number;
  currentStepIndex: number;
  scrollViewRef?: React.RefObject<ScrollView>;
}

export const GuidedTutorialOverlay: React.FC<GuidedTutorialOverlayProps> = ({
  visible,
  currentStep,
  onNext,
  onComplete,
  onNavigate,
  totalSteps,
  currentStepIndex,
  scrollViewRef,
}) => {
  const { playSound } = useAudio();
  const { isPersisted } = useTutorial();
  const [isAnimating, setIsAnimating] = useState(false);
  const [localVisible, setLocalVisible] = useState(false);
  
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const cardSlideAnim = useRef(new Animated.Value(50)).current;

  // Function to scroll to the highlighted area
  const scrollToHighlightedArea = () => {
    if (!currentStep || !scrollViewRef?.current || currentStep.noHighlight) return;
    
    // Calculate center of screen and desired position
    const screenCenter = SCREEN_HEIGHT / 2;
    const elementCenter = currentStep.highlightArea.y + (currentStep.highlightArea.height / 2);
    
    // Try to center the element in the middle of the screen with some offset
    // to account for the instruction card that will appear below
    const scrollToY = Math.max(0, elementCenter - screenCenter + 50);
    
    // Use scrollTo with animation
    scrollViewRef.current.scrollTo({
      y: scrollToY,
      animated: true
    });
  };

  // Handle visibility with persistence support
  useEffect(() => {
    if (visible || isPersisted) {
      if (isPersisted) {
        // Show immediately when persisted (e.g., returning from task screen)
        setLocalVisible(true);
        setTimeout(() => {
          scrollToHighlightedArea();
        }, 100);
      } else {
        // Normal delay for first appearance
        const timer = setTimeout(() => {
          setLocalVisible(true);
          setTimeout(() => {
            scrollToHighlightedArea();
          }, 300);
        }, 1000);
        
        return () => clearTimeout(timer);
      }
    } else {
      setLocalVisible(false);
    }
  }, [visible, isPersisted, scrollViewRef]);

  // Scroll to highlighted area when step changes
  useEffect(() => {
    if (localVisible && currentStep && !isPersisted) {
      setTimeout(() => {
        scrollToHighlightedArea();
      }, 300);
    }
  }, [currentStep, localVisible, scrollViewRef, isPersisted]);

  useEffect(() => {
    if (localVisible && currentStep) {
      setIsAnimating(true);
      
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        Animated.timing(cardSlideAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsAnimating(false);
      });

      // Pulse animation for highlight
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Reset animations when hidden
      fadeAnim.setValue(0);
      cardSlideAnim.setValue(50);
    }
  }, [localVisible, currentStep]);

  const handleNext = () => {
    if (isAnimating) return;
    
    playSound('button_press');
    
    // Handle navigation steps
    if (currentStep?.action === 'navigate' && currentStep.targetScreen && onNavigate) {
      onNavigate(currentStep.targetScreen);
    }
    
    if (currentStepIndex === totalSteps - 1) {
      onComplete();
    } else {
      onNext();
    }
  };

  const handleHighlightPress = () => {
    playSound('button_success');
    handleNext();
  };

  if (!localVisible || !currentStep) return null;

  // Use step-defined coordinates with status bar adjustment
  const statusBarHeight = Platform.OS === 'ios' ? (StatusBar.currentHeight || 44) : 0;
  const adjustedHighlight = {
    ...currentStep.highlightArea,
    y: currentStep.highlightArea.y + statusBarHeight,
  };

  // Determine best position for instruction card
  const cardPosition = adjustedHighlight.y > SCREEN_HEIGHT / 2 ? 'top' : 'bottom';
  const cardTop = cardPosition === 'top' ? 100 : undefined;
  const cardBottom = cardPosition === 'bottom' ? 100 : undefined;

  return (
    <Modal
      visible={localVisible}
      animationType="none"
      transparent={true}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        {/* Semi-transparent backdrop */}
        <Animated.View
          style={[
            styles.backdrop,
            {
              opacity: fadeAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 0.3],
              }),
            },
          ]}
        />

        {/* Highlight cutout - only shown if noHighlight is false */}
        {!currentStep.noHighlight && (
          <View style={styles.highlightContainer}>
            {/* Top mask */}
            <View
              style={[
                styles.maskSection,
                {
                  height: adjustedHighlight.y,
                },
              ]}
            />
            
            {/* Middle section with highlight cutout */}
            <View style={styles.middleSection}>
              {/* Left mask */}
              <View
                style={[
                  styles.maskSection,
                  {
                    width: adjustedHighlight.x,
                    height: adjustedHighlight.height,
                  },
                ]}
              />
              
              {/* Highlight area */}
              <Animated.View
                style={[
                  styles.highlightArea,
                  {
                    width: adjustedHighlight.width,
                    height: adjustedHighlight.height,
                    transform: [{ scale: pulseAnim }],
                  },
                ]}
              >
                <TouchableOpacity
                  style={styles.highlightTouchable}
                  onPress={handleHighlightPress}
                  activeOpacity={0.8}
                />
              </Animated.View>
              
              {/* Right mask */}
              <View
                style={[
                  styles.maskSection,
                  {
                    flex: 1,
                    height: adjustedHighlight.height,
                  },
                ]}
              />
            </View>
            
            {/* Bottom mask */}
            <View
              style={[
                styles.maskSection,
                {
                  flex: 1,
                },
              ]}
            />
          </View>
        )}

        {/* Instruction card */}
        <Animated.View
          style={[
            styles.instructionCard,
            {
              top: cardTop,
              bottom: cardBottom,
              transform: [{ translateY: cardSlideAnim }],
            },
          ]}
        >
          <LinearGradient
            colors={['#FF6B9D', '#C44569']}
            style={styles.cardGradient}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          >
            <Text style={styles.instructionTitle}>{currentStep.title}</Text>
            <Text style={styles.instructionText}>{currentStep.description}</Text>
            
            <View style={styles.cardActions}>
              <Text style={styles.stepCounter}>
                Step {currentStepIndex + 1} of {totalSteps}
              </Text>
              
              <NintendoButton
                title={currentStepIndex === totalSteps - 1 ? "Complete!" : "Next"}
                onPress={handleNext}
                variant="secondary"
                style={styles.nextButton}
              />
            </View>
          </LinearGradient>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    position: 'relative',
  },
  backdrop: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#000',
  },
  highlightContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
  },
  maskSection: {
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
  },
  middleSection: {
    flexDirection: 'row',
  },
  highlightArea: {
    borderRadius: 12,
    borderWidth: 3,
    borderColor: '#FFDD00',
    shadowColor: '#FFDD00',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 10,
    elevation: 10,
  },
  highlightTouchable: {
    flex: 1,
    borderRadius: 9,
  },
  instructionCard: {
    position: 'absolute',
    left: 20,
    right: 20,
    maxHeight: 200,
  },
  cardGradient: {
    borderRadius: 20,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  instructionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  instructionText: {
    fontSize: 16,
    color: '#FFF',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  cardActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  stepCounter: {
    fontSize: 14,
    color: '#FFF',
    opacity: 0.8,
  },
  nextButton: {
    minWidth: 100,
  },
});

export default GuidedTutorialOverlay; 