import React from 'react';
import { View, StyleSheet, Animated, Dimensions } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

// Base skeleton component with shimmer animation
export const SkeletonCard: React.FC<{
  width?: number | string;
  height: number;
  borderRadius?: number;
  style?: any;
}> = ({ width = '100%', height, borderRadius = 12, style }) => {
  const shimmerValue = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    const shimmerAnimation = Animated.loop(
      Animated.sequence([
        Animated.timing(shimmerValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ])
    );
    shimmerAnimation.start();
    return () => shimmerAnimation.stop();
  }, []);

  const shimmerOpacity = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={[{ width, height, borderRadius }, style]}>
      <Animated.View
        style={[
          StyleSheet.absoluteFill,
          {
            borderRadius,
            opacity: shimmerOpacity,
          },
        ]}
      >
        <LinearGradient
          colors={['#E1E9FC', '#F3F4F6', '#E1E9FC']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={StyleSheet.absoluteFill}
        />
      </Animated.View>
    </View>
  );
};

// Dashboard Skeleton Screen
export const DashboardSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Header Skeleton */}
      <View style={styles.header}>
        <SkeletonCard width="60%" height={28} style={styles.headerTitle} />
        <SkeletonCard width="45%" height={20} style={styles.headerSubtitle} />
      </View>

      {/* Family Coins Skeleton */}
      <View style={styles.coinsSection}>
        <SkeletonCard width="100%" height={120} style={styles.coinsCard} />
      </View>

      {/* Children Section Skeleton */}
      <View style={styles.section}>
        <SkeletonCard width="30%" height={24} style={styles.sectionTitle} />
        <View style={styles.horizontalScroll}>
          {[1, 2, 3].map((i) => (
            <SkeletonCard 
              key={i} 
              width={140} 
              height={180} 
              style={styles.childCard} 
            />
          ))}
        </View>
      </View>

      {/* Action Buttons Skeleton */}
      <View style={styles.section}>
        <SkeletonCard width="40%" height={24} style={styles.sectionTitle} />
        <View style={styles.buttonGrid}>
          {[1, 2, 3, 4].map((i) => (
            <SkeletonCard 
              key={i} 
              width="48%" 
              height={120} 
              style={styles.actionButton} 
            />
          ))}
        </View>
      </View>

      {/* Recent Tasks Skeleton */}
      <View style={styles.section}>
        <SkeletonCard width="35%" height={24} style={styles.sectionTitle} />
        {[1, 2, 3].map((i) => (
          <SkeletonCard 
            key={i} 
            width="100%" 
            height={80} 
            style={styles.taskCard} 
          />
        ))}
      </View>
    </View>
  );
};

// Child Profile Skeleton
export const ChildProfileSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <SkeletonCard width={100} height={100} borderRadius={50} style={styles.avatar} />
        <SkeletonCard width="50%" height={32} style={styles.profileName} />
        <SkeletonCard width="30%" height={24} style={styles.profileLevel} />
      </View>

      {/* Stats Cards */}
      <View style={styles.statsGrid}>
        {[1, 2, 3, 4].map((i) => (
          <SkeletonCard 
            key={i} 
            width="48%" 
            height={100} 
            style={styles.statCard} 
          />
        ))}
      </View>

      {/* Progress Bars */}
      <View style={styles.section}>
        <SkeletonCard width="40%" height={24} style={styles.sectionTitle} />
        <SkeletonCard width="100%" height={20} style={styles.progressBar} />
        <SkeletonCard width="100%" height={20} style={styles.progressBar} />
      </View>

      {/* Action Buttons */}
      <View style={styles.section}>
        {[1, 2, 3].map((i) => (
          <SkeletonCard 
            key={i} 
            width="100%" 
            height={50} 
            style={styles.actionButton} 
          />
        ))}
      </View>
    </View>
  );
};

// Task List Skeleton
export const TaskListSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <SkeletonCard width="50%" height={28} style={styles.headerTitle} />
        <SkeletonCard width="30%" height={20} style={styles.headerSubtitle} />
      </View>

      {/* Filter/Child Selector */}
      <View style={styles.filterSection}>
        <SkeletonCard width="100%" height={50} style={styles.filterCard} />
      </View>

      {/* Task Items */}
      <View style={styles.section}>
        {[1, 2, 3, 4, 5].map((i) => (
          <SkeletonCard 
            key={i} 
            width="100%" 
            height={100} 
            style={styles.taskCard} 
          />
        ))}
      </View>
    </View>
  );
};

// Reward List Skeleton
export const RewardListSkeleton: React.FC = () => {
  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFill}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <SkeletonCard width="45%" height={28} style={styles.headerTitle} />
        <SkeletonCard width="35%" height={20} style={styles.headerSubtitle} />
      </View>

      {/* Rewards Grid */}
      <View style={styles.section}>
        <View style={styles.rewardGrid}>
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <SkeletonCard 
              key={i} 
              width="48%" 
              height={150} 
              style={styles.rewardCard} 
            />
          ))}
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 60,
    paddingHorizontal: 20,
  },
  header: {
    marginBottom: 30,
  },
  headerTitle: {
    marginBottom: 8,
  },
  headerSubtitle: {
    marginBottom: 20,
  },
  coinsSection: {
    marginBottom: 25,
  },
  coinsCard: {
    marginBottom: 10,
  },
  section: {
    marginBottom: 25,
  },
  sectionTitle: {
    marginBottom: 15,
  },
  horizontalScroll: {
    flexDirection: 'row',
    gap: 12,
  },
  childCard: {
    marginRight: 12,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 10,
  },
  actionButton: {
    marginBottom: 10,
  },
  taskCard: {
    marginBottom: 12,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatar: {
    marginBottom: 15,
  },
  profileName: {
    marginBottom: 8,
  },
  profileLevel: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 25,
    gap: 10,
  },
  statCard: {
    marginBottom: 10,
  },
  progressBar: {
    marginBottom: 10,
  },
  filterSection: {
    marginBottom: 20,
  },
  filterCard: {
    marginBottom: 10,
  },
  rewardGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 10,
  },
  rewardCard: {
    marginBottom: 12,
  },
});