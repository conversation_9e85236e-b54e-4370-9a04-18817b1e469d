import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useAppStore } from '../stores/useAppStore';

export const TestSetup: React.FC = () => {
  const { currentMode, selectedChildId, setMode, selectChild } = useAppStore();
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>Architecture Test</Text>
      <Text style={styles.text}>Current Mode: {currentMode}</Text>
      <Text style={styles.text}>Selected Child: {selectedChildId || 'None'}</Text>
      <Text style={styles.text}>✅ Zustand Store Working</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  text: {
    fontSize: 14,
    marginBottom: 5,
  },
}); 