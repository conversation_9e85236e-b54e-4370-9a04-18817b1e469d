import React from 'react';
import { FlatList, StyleSheet, ActivityIndicator, View, Text } from 'react-native';
import { TaskCard } from '../molecules/TaskCard';
import { useSmartTaskCompletion } from '../../hooks';

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: string;
}

export const TaskList: React.FC = () => {
  const { tasks, handleCompleteTask, loading, isCompleting } = useSmartTaskCompletion();
  
  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#FF6B9D" />
        <Text style={styles.loadingText}>Loading tasks...</Text>
      </View>
    );
  }
  
  if (!tasks || tasks.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No tasks available</Text>
      </View>
    );
  }
  
  return (
    <FlatList
      data={tasks}
      keyExtractor={(item) => item.id}
      renderItem={({ item }) => (
        <TaskCard
          task={item}
          onComplete={handleCompleteTask}
          isCompleting={isCompleting(item.id)}
        />
      )}
      style={styles.list}
      contentContainerStyle={styles.contentContainer}
    />
  );
};

const styles = StyleSheet.create({
  list: { flex: 1 },
  contentContainer: { paddingVertical: 8 },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
}); 