import React, { useState } from 'react';
import { View, ActivityIndicator } from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import FamilyDashboard from '../screens/FamilyDashboard';
import SettingsScreen from '../screens/SettingsScreen';
import OnboardingNavigator, { OnboardingData } from '../screens/onboarding/OnboardingNavigator';
import { supabase } from '../utils/supabase';

type AppScreen = 'loading' | 'onboarding' | 'dashboard' | 'settings';

const AppRouter: React.FC = () => {
  const { user, userProfile, loading: authLoading } = useAuth();
  const [currentScreen, setCurrentScreen] = useState<AppScreen>('settings');

  // Determine which screen to show based on auth state (synchronously during render)
  const getTargetScreen = (): AppScreen => {
    if (authLoading) {
      return 'loading';
    }

    if (!user) {
      // No user logged in - start onboarding (which includes auth)
      return 'onboarding';
    }

    if (!userProfile?.onboarding_completed) {
      // User exists but hasn't completed onboarding
      return 'onboarding';
    }

    // User is authenticated and onboarded - show dashboard
    return 'dashboard';
  };

  const targetScreen = getTargetScreen();

  // Update current screen only when target changes (prevents flash)
  React.useEffect(() => {
    if (currentScreen !== targetScreen) {
      setCurrentScreen(targetScreen);
    }
  }, [targetScreen, currentScreen]);

  const handleOnboardingComplete = async (onboardingData: OnboardingData) => {
    try {
      if (!user) {
        console.error('No user found after onboarding');
        return;
      }

      // Save family settings
      const { familySettings, children, preferences } = onboardingData;
      
      // Update user with onboarding completion
      const { error: profileError } = await supabase
        .from('users')
        .update({
          onboarding_completed: true,
          family_name: familySettings.familyName,
          full_name: familySettings.parentName,
          onboarding_completed_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (profileError) {
        console.error('Error updating user profile:', profileError);
        return;
      }

      // Create or update family settings entry
      const { error: familySettingsError } = await supabase
        .from('family_settings')
        .upsert({
          parent_id: user.id,
          task_verification_mode: familySettings.taskVerificationMode,
        });

      if (familySettingsError) {
        console.error('Error creating family settings:', familySettingsError);
        return;
      }

      // Create children (only if they don't already exist)
      if (children.length > 0) {
        // Check if user already has children
        const { data: existingChildren } = await supabase
          .from('children')
          .select('id')
          .eq('parent_id', user.id);

        // Only create children if none exist yet
        if (!existingChildren || existingChildren.length === 0) {
          const childrenData = children.map(child => ({
            parent_id: user.id,
            name: child.name,
            age: child.age,
            avatar: child.avatar,
            coin_balance: child.startingCoins,
            level: child.level,
          }));

          const { error: childrenError } = await supabase
            .from('children')
            .insert(childrenData);

          if (childrenError) {
            console.error('Error creating children:', childrenError);
            return;
          }
        } else {
          console.log('Children already exist for this user, skipping creation');
        }
      }

      // Navigate to dashboard
      setCurrentScreen('dashboard');
    } catch (error) {
      console.error('Error completing onboarding:', error);
    }
  };

  const showSettings = () => {
    setCurrentScreen('settings');
  };

  const showDashboard = () => {
    setCurrentScreen('dashboard');
  };

  // Use targetScreen for immediate rendering (no flash)
  const screenToRender = currentScreen === 'settings' ? 'settings' : targetScreen;

  // Loading screen
  if (screenToRender === 'loading') {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}>
        <ActivityIndicator size="large" color="#FF6B9D" />
      </View>
    );
  }

  // Onboarding flow
  if (screenToRender === 'onboarding') {
    return <OnboardingNavigator onComplete={handleOnboardingComplete} />;
  }

  // Settings screen
  if (screenToRender === 'settings') {
    return <SettingsScreen onBack={showDashboard} />;
  }

  // Main dashboard (default)
  return <FamilyDashboard onShowSettings={showSettings} />;
};

export default AppRouter;