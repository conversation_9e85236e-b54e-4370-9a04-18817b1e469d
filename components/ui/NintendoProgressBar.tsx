import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  Animated,
  StyleSheet,
  ViewStyle,
  TextStyle,
} from 'react-native';

interface NintendoProgressBarProps {
  progress: number; // 0 to 1
  totalSteps?: number;
  currentStep?: number;
  showStepText?: boolean;
  style?: ViewStyle;
  fillStyle?: ViewStyle;
  textStyle?: TextStyle;
  color?: string;
  backgroundColor?: string;
  height?: number;
}

const NintendoProgressBar: React.FC<NintendoProgressBarProps> = ({
  progress,
  totalSteps,
  currentStep,
  showStepText = true,
  style,
  fillStyle,
  textStyle,
  color = '#FFFFFF',
  backgroundColor = 'rgba(255, 255, 255, 0.2)',
  height = 10,
}) => {
  const progressAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: progress,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  const getStepText = () => {
    if (totalSteps && currentStep) {
      return `Step ${currentStep} of ${totalSteps}`;
    }
    return `${Math.round(progress * 100)}%`;
  };

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 1],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <View style={[styles.container, style]}>
      <View style={[
        styles.progressBackground,
        { backgroundColor, height },
      ]}>
        <Animated.View
          style={[
            styles.progressFill,
            { backgroundColor: color, height, width: progressWidth },
            fillStyle,
          ]}
        />
      </View>
      {showStepText && (
        <Text style={[styles.progressText, textStyle]}>
          {getStepText()}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    marginBottom: 20,
  },
  progressBackground: {
    borderRadius: 5,
    overflow: 'hidden',
  },
  progressFill: {
    borderRadius: 5,
  },
  progressText: {
    fontSize: 14,
    color: '#FFFFFF',
    textAlign: 'center',
    marginTop: 5,
  },
});

export default NintendoProgressBar; 