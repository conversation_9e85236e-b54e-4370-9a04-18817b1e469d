import React from 'react';
import { StyleProp, ViewStyle } from 'react-native';
import NintendoButton from './NintendoButton';
import TutorialHighlight from './TutorialHighlight';

interface TutorialButtonProps {
  title: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  tutorialStep?: string; // Optional step ID for tutorial
}

/**
 * A button component that can be highlighted during tutorial steps
 */
const TutorialButton: React.FC<TutorialButtonProps> = ({
  title,
  onPress,
  variant = 'primary',
  style,
  disabled = false,
  tutorialStep,
}) => {
  // If no tutorial step provided, just render the regular button
  if (!tutorialStep) {
    return (
      <NintendoButton 
        title={title} 
        onPress={onPress} 
        variant={variant} 
        style={style}
        disabled={disabled}
      />
    );
  }

  // Wrap with tutorial highlight if tutorial step is provided
  return (
    <TutorialHighlight stepId={tutorialStep}>
      <NintendoButton 
        title={title} 
        onPress={onPress} 
        variant={variant} 
        style={[style, { width: '100%' }]}
        disabled={disabled}
      />
    </TutorialHighlight>
  );
};

export default TutorialButton; 