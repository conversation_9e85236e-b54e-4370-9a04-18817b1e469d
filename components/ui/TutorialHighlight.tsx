import React, { useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  Animated,
  ViewStyle,
  StyleProp,
  findNodeHandle,
  UIManager,
  Platform,
} from 'react-native';
import { useTutorial } from '../../contexts/TutorialContext';

interface TutorialHighlightProps {
  children: React.ReactNode;
  stepId: string;
  style?: StyleProp<ViewStyle>;
  active?: boolean;
}

interface MeasureResult {
  x: number;
  y: number;
  width: number;
  height: number;
  pageX: number;
  pageY: number;
}

const TutorialHighlight: React.FC<TutorialHighlightProps> = ({
  children,
  stepId,
  style,
  active = true,
}) => {
  const { tutorialState, currentStep } = useTutorial();
  const viewRef = useRef<View>(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  // Determine if this element should be highlighted
  const isHighlighted = 
    active && 
    tutorialState.isActive && 
    currentStep?.id === stepId;

  useEffect(() => {
    if (isHighlighted) {
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();

      // Measure and update position for the tutorial context
      if (viewRef.current) {
        const handle = findNodeHandle(viewRef.current);
        if (handle) {
          UIManager.measure(handle, (x, y, width, height, pageX, pageY) => {
            // Report measurements to tutorial context for overlay positioning
            console.log('Element measured:', { x, y, width, height, pageX, pageY });
          });
        }
      }
    }

    return () => {
      pulseAnim.stopAnimation();
    };
  }, [isHighlighted, pulseAnim]);

  if (!active) {
    return <View style={style}>{children}</View>;
  }

  // Convert StyleProp<ViewStyle> to ViewStyle
  const containerStyle: ViewStyle[] = [styles.container];
  if (style) {
    if (Array.isArray(style)) {
      containerStyle.push(...(style as ViewStyle[]));
    } else {
      containerStyle.push(style as ViewStyle);
    }
  }

  return (
    <View
      ref={viewRef}
      style={containerStyle}
      collapsable={false} // Required for measurements to work
    >
      {children}
      
      {isHighlighted && (
        <Animated.View
          style={[
            styles.highlightBorder,
            {
              transform: [{ scale: pulseAnim }],
            },
          ]}
          pointerEvents="none"
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  highlightBorder: {
    position: 'absolute',
    top: -10,
    left: -10,
    right: -10,
    bottom: -10,
    borderRadius: 12,
    borderWidth: 5,
    borderColor: '#FFDD00',
    shadowColor: '#FFDD00',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 1,
    shadowRadius: 15,
    zIndex: 9999,
    pointerEvents: 'none',
  },
});

export default TutorialHighlight; 