import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  Dimensions,
} from 'react-native';
import { useAudio } from '../../contexts/AudioContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface SuggestionCardProps {
  title: string;
  coins: number;
  category: string;
  selected?: boolean;
  onPress: () => void;
  style?: ViewStyle;
}

const SuggestionCard: React.FC<SuggestionCardProps> = ({
  title,
  coins,
  category,
  selected = false,
  onPress,
  style,
}) => {
  const { playInteraction } = useAudio();

  const handlePress = async () => {
    await playInteraction('card_select');
    onPress();
  };

  return (
    <TouchableOpacity
      style={[
        styles.card,
        selected && styles.cardSelected,
        style,
      ]}
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.coins}>{coins} coins</Text>
      <Text style={styles.category}>{category}</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 10,
    padding: 12,
    margin: 6,
    width: (SCREEN_WIDTH - 120) / 2,
    alignItems: 'center',
    minHeight: 80,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  cardSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: '#4ECDC4',
  },
  title: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 3,
  },
  coins: {
    fontSize: 12,
    color: '#fff',
    marginBottom: 2,
  },
  category: {
    fontSize: 10,
    color: '#fff',
    opacity: 0.7,
  },
});

export default SuggestionCard; 