import React from 'react';
import {
  TextInput,
  Text,
  View,
  StyleSheet,
  ViewStyle,
  TextStyle,
  TextInputProps,
} from 'react-native';

interface NintendoInputProps extends Omit<TextInputProps, 'style'> {
  label?: string;
  error?: string;
  style?: ViewStyle;
  inputStyle?: TextStyle;
  labelStyle?: TextStyle;
  variant?: 'default' | 'transparent';
  required?: boolean;
}

const NintendoInput: React.FC<NintendoInputProps> = ({
  label,
  error,
  style,
  inputStyle,
  labelStyle,
  variant = 'default',
  required = false,
  ...props
}) => {
  const hasError = !!error;

  const getInputStyle = () => {
    const baseStyle: any[] = [styles.input];
    
    if (variant === 'transparent') {
      baseStyle.push(styles.inputTransparent);
    } else {
      baseStyle.push(styles.inputDefault);
    }
    
    if (hasError) {
      baseStyle.push(styles.inputError);
    }
    
    if (props.editable === false) {
      baseStyle.push(styles.inputDisabled);
    }
    
    return baseStyle;
  };

  return (
    <View style={[styles.container, style]}>
      {label && (
        <Text style={[styles.label, labelStyle]}>
          {label}
          {required && <Text style={styles.required}> *</Text>}
        </Text>
      )}
      <TextInput
        style={[getInputStyle(), inputStyle]}
        placeholderTextColor="#A0A0A0"
        {...props}
      />
      {hasError && (
        <Text style={styles.errorText}>{error}</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  required: {
    color: '#FF6B9D',
  },
  input: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  inputDefault: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    color: '#333333',
  },
  inputTransparent: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
    color: '#FFFFFF',
  },
  inputError: {
    borderColor: '#FF6B6B',
    borderWidth: 2,
  },
  inputDisabled: {
    opacity: 0.6,
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  errorText: {
    fontSize: 12,
    color: '#FF6B6B',
    marginTop: 4,
    marginLeft: 4,
  },
});

export default NintendoInput; 