import React from 'react';
import { View, Text, Switch, StyleSheet, Dimensions } from 'react-native';
import { useAudio } from '../../contexts/AudioContext';
import NintendoCard from './NintendoCard';

export const AudioSettings: React.FC = () => {
  const {
    backgroundMusicEnabled,
    soundEffectsEnabled,
    masterVolume,
    setBackgroundMusicEnabled,
    setSoundEffectsEnabled,
    setMasterVolume,
    playSound,
    playBackgroundMusic,
    stopBackgroundMusic,
  } = useAudio();

  const handleTestSound = (soundType: any) => {
    playSound(soundType);
  };

  const handleTestBackgroundMusic = async () => {
    if (backgroundMusicEnabled) {
      await playBackgroundMusic('onboarding');
    }
  };

  return (
    <NintendoCard style={styles.container}>
      <Text style={styles.title}>🎵 Audio Settings</Text>
      
      {/* Master Volume */}
      <View style={styles.settingRow}>
        <Text style={styles.label}>Master Volume</Text>
        <View style={styles.volumeContainer}>
          <Text 
            style={styles.volumeButton} 
            onPress={() => setMasterVolume(Math.max(0, masterVolume - 0.1))}
          >
            ➖
          </Text>
          <Text style={styles.volumeDisplay}>{Math.round(masterVolume * 100)}%</Text>
          <Text 
            style={styles.volumeButton} 
            onPress={() => setMasterVolume(Math.min(1, masterVolume + 0.1))}
          >
            ➕
          </Text>
        </View>
      </View>

      {/* Background Music Toggle */}
      <View style={styles.settingRow}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>
            {backgroundMusicEnabled ? '🎵' : '🔇'} Background Music
          </Text>
          <Text style={styles.labelSubtext}>
            {backgroundMusicEnabled ? 'Playing' : 'Muted'}
          </Text>
        </View>
        <Switch
          value={backgroundMusicEnabled}
          onValueChange={setBackgroundMusicEnabled}
          trackColor={{ false: '#E5E7EB', true: '#4F46E5' }}
          thumbColor={backgroundMusicEnabled ? '#FFFFFF' : '#9CA3AF'}
        />
      </View>

      {/* Sound Effects Toggle */}
      <View style={styles.settingRow}>
        <View style={styles.labelContainer}>
          <Text style={styles.label}>
            {soundEffectsEnabled ? '🔊' : '🔇'} Sound Effects
          </Text>
          <Text style={styles.labelSubtext}>
            Button clicks, coins, etc.
          </Text>
        </View>
        <Switch
          value={soundEffectsEnabled}
          onValueChange={setSoundEffectsEnabled}
          trackColor={{ false: '#E5E7EB', true: '#4F46E5' }}
          thumbColor={soundEffectsEnabled ? '#FFFFFF' : '#9CA3AF'}
        />
      </View>

      {/* Test Buttons */}
      <View style={styles.testSection}>
        <Text style={styles.sectionTitle}>🧪 Test Audio</Text>
        
        <View style={styles.buttonRow}>
          <Text 
            style={[styles.testButton, !backgroundMusicEnabled && styles.disabledButton]} 
            onPress={backgroundMusicEnabled ? handleTestBackgroundMusic : undefined}
          >
            {backgroundMusicEnabled ? '🎼 Play Music' : '🔇 Music Muted'}
          </Text>
          <Text 
            style={styles.testButton} 
            onPress={() => stopBackgroundMusic()}
          >
            ⏹️ Stop Music
          </Text>
        </View>

        <View style={styles.buttonGrid}>
          <Text 
            style={styles.testButton} 
            onPress={() => handleTestSound('button_press')}
          >
            🔘 Button
          </Text>
          <Text 
            style={styles.testButton} 
            onPress={() => handleTestSound('coin_collect')}
          >
            🪙 Coin
          </Text>
          <Text 
            style={styles.testButton} 
            onPress={() => handleTestSound('achievement_unlock')}
          >
            🏆 Achievement
          </Text>
          <Text 
            style={styles.testButton} 
            onPress={() => handleTestSound('celebration')}
          >
            🎉 Celebration
          </Text>
        </View>
      </View>

      {/* Silent Mode Testing */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>🔇 Silent Mode & Mute Control</Text>
        <Text style={styles.infoText}>📱 Put your phone in silent mode and test the audio!</Text>
        <Text style={styles.infoText}>✅ Audio should play even when ringer is off (like Spotify)</Text>
        <Text style={styles.infoText}>🎵 Toggle background music on/off while keeping button sounds</Text>
        <Text style={styles.infoText}>🚧 Silent mode bypass works in production builds, not Expo Go</Text>
      </View>

      {/* Volume Levels Display */}
      <View style={styles.infoSection}>
        <Text style={styles.sectionTitle}>📊 Current Levels</Text>
        <Text style={styles.infoText}>🎼 Background Music: 30% base volume</Text>
        <Text style={styles.infoText}>🔊 Sound Effects: 80% base volume</Text>
        <Text style={styles.infoText}>🎛️ Master Volume: {Math.round(masterVolume * 100)}%</Text>
        <Text style={styles.infoTextSmall}>
          💡 Tip: Edit AUDIO_CONFIG in AudioContext.tsx to adjust individual sound volumes
        </Text>
      </View>
    </NintendoCard>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 16,
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingVertical: 8,
  },
  label: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '500',
  },
  labelContainer: {
    flex: 1,
  },
  labelSubtext: {
    fontSize: 12,
    color: '#6B7280',
    marginTop: 2,
  },
  volumeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  volumeButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 8,
    fontSize: 16,
    textAlign: 'center',
    minWidth: 32,
  },
  volumeDisplay: {
    fontSize: 16,
    color: '#374151',
    fontWeight: '600',
    minWidth: 50,
    textAlign: 'center',
  },
  testSection: {
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  buttonGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  testButton: {
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    textAlign: 'center',
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
    minWidth: '48%',
  },
  disabledButton: {
    backgroundColor: '#F9FAFB',
    borderColor: '#E5E7EB',
    color: '#9CA3AF',
  },
  infoSection: {
    marginTop: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  infoText: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 4,
  },
  infoTextSmall: {
    fontSize: 12,
    color: '#9CA3AF',
    marginTop: 8,
    fontStyle: 'italic',
  },
}); 