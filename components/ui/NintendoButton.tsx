import React from 'react';
import { StyleProp,
  TouchableOpacity,
  Text,
  StyleSheet,
  ViewStyle,
  TextStyle,
  View,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAudio } from '../../contexts/AudioContext';

interface NintendoButtonProps {
  title: string;
  subtitle?: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'back' | 'danger';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: TextStyle;
}

const GRADIENT_COLORS = {
  primary: ['#FF6B9D', '#C44569'] as const,
  secondary: ['#667eea', '#764ba2'] as const,
  success: ['#32CD32', '#228B22'] as const,
  back: ['rgba(255, 255, 255, 0.1)', 'rgba(255, 255, 255, 0.05)'] as const,
  danger: ['#ff6b6b', '#ee5a52'] as const,
  disabled: ['#A0A0A0', '#808080'] as const,
};

const NintendoButton: React.FC<NintendoButtonProps> = ({
  title,
  subtitle,
  onPress,
  variant = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  fullWidth = true,
  style,
  textStyle,
}) => {
  const { playInteraction } = useAudio();
  const isDisabled = disabled || loading;
  const gradientColors = isDisabled ? GRADIENT_COLORS.disabled : GRADIENT_COLORS[variant];

  const handlePress = async () => {
    if (isDisabled) return;
    
    // Play audio + haptic feedback based on button variant
    let interactionType: 'button_primary' | 'button_secondary' | 'button_success' | 'button_back' | 'button_danger';
    
    switch (variant) {
      case 'primary':
        interactionType = 'button_primary';
        break;
      case 'secondary':
        interactionType = 'button_secondary';
        break;
      case 'success':
        interactionType = 'button_success';
        break;
      case 'back':
        interactionType = 'button_back';
        break;
      case 'danger':
        interactionType = 'button_danger';
        break;
      default:
        interactionType = 'button_primary';
        break;
    }
    
    await playInteraction(interactionType);
    onPress();
  };

  const getSizeStyles = () => {
    switch (size) {
      case 'small':
        return {
          paddingVertical: 12,
          paddingHorizontal: 24,
          borderRadius: 20,
        };
      case 'large':
        return {
          paddingVertical: 20,
          paddingHorizontal: 50,
          borderRadius: 30,
        };
      default: // medium
        return {
          paddingVertical: 16,
          paddingHorizontal: 32,
          borderRadius: 25,
        };
    }
  };

  const getTextSize = () => {
    switch (size) {
      case 'small':
        return { fontSize: 14 };
      case 'large':
        return { fontSize: 20 };
      default: // medium
        return { fontSize: 18 };
    }
  };

  const getSubtitleSize = () => {
    switch (size) {
      case 'small':
        return { fontSize: 10 };
      case 'large':
        return { fontSize: 14 };
      default: // medium
        return { fontSize: 12 };
    }
  };

  if (variant === 'back') {
    return (
      <TouchableOpacity
        style={[
          styles.backButton,
          getSizeStyles(),
          isDisabled && styles.disabled,
          style,
        ]}
        onPress={handlePress}
        disabled={isDisabled}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.backButtonText,
          getTextSize(),
          isDisabled && styles.disabledText,
          textStyle,
        ]}>
          {loading ? 'Loading...' : title}
        </Text>
        {subtitle && (
          <Text style={[
            styles.subtitleText,
            getSubtitleSize(),
            isDisabled && styles.disabledText,
          ]}>
            {subtitle}
          </Text>
        )}
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[
        styles.button,
        fullWidth && styles.fullWidth,
        styles.shadow,
        isDisabled && styles.disabled,
        style,
      ]}
              onPress={handlePress}
        disabled={isDisabled}
        activeOpacity={0.8}
    >
      <LinearGradient
        colors={gradientColors}
        style={[
          styles.buttonGradient,
          getSizeStyles(),
        ]}
      >
        <Text style={[
          styles.buttonText,
          getTextSize(),
          isDisabled && styles.disabledText,
          textStyle,
        ]}>
          {loading ? 'Loading...' : title}
        </Text>
        {subtitle && (
          <Text style={[
            styles.subtitleText,
            getSubtitleSize(),
            isDisabled && styles.disabledText,
          ]}>
            {subtitle}
          </Text>
        )}
      </LinearGradient>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    marginBottom: 16,
  },
  fullWidth: {
    width: '100%',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  buttonGradient: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontWeight: 'bold',
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  subtitleText: {
    color: '#FFFFFF',
    marginTop: 4,
    opacity: 0.9,
    textAlign: 'center',
  },
  backButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'transparent',
  },
  backButtonText: {
    color: '#FFFFFF',
    opacity: 0.8,
  },
  disabled: {
    opacity: 0.7,
  },
  disabledText: {
    color: '#C8C8C8',
  },
});

export default NintendoButton; 