import React from 'react';
import {
  View,
  StyleSheet,
  ViewStyle,
} from 'react-native';

interface NintendoCardProps {
  children: React.ReactNode;
  variant?: 'default' | 'transparent' | 'elevated';
  style?: ViewStyle;
  withShadow?: boolean;
}

const NintendoCard: React.FC<NintendoCardProps> = ({
  children,
  variant = 'default',
  style,
  withShadow = false,
}) => {
  const getCardStyle = () => {
    const baseStyle: any[] = [styles.card];
    
    switch (variant) {
      case 'transparent':
        baseStyle.push(styles.cardTransparent);
        break;
      case 'elevated':
        baseStyle.push(styles.cardElevated);
        break;
      default:
        baseStyle.push(styles.cardDefault);
        break;
    }
    
    if (withShadow) {
      baseStyle.push(styles.shadow);
    }
    
    return baseStyle;
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  cardDefault: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  cardTransparent: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  cardElevated: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
  shadow: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
});

export default NintendoCard; 