import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAudio } from '../../contexts/AudioContext';
import { userApi } from '../../utils/api';
import { useTutorial } from '../../contexts/TutorialContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface DevMenuProps {
  onShowSettings: () => void;
  onResetOnboarding: () => void;
  onSignOut: () => void;
}

const DevMenu: React.FC<DevMenuProps> = ({
  onShowSettings,
  onResetOnboarding,
  onSignOut,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [tapCount, setTapCount] = useState(0);
  const [lastTapTime, setLastTapTime] = useState(0);
  const [isResetting, setIsResetting] = useState(false);
  const { playInteraction } = useAudio();
  const { startTutorial } = useTutorial();
  
  const modalOpacity = useRef(new Animated.Value(0)).current;
  const modalScale = useRef(new Animated.Value(0.8)).current;

  // Reset tap count after 2 seconds of inactivity
  useEffect(() => {
    const timer = setTimeout(() => {
      setTapCount(0);
    }, 2000);

    return () => clearTimeout(timer);
  }, [tapCount]);

  const handleCornerTap = () => {
    const now = Date.now();
    
    // Reset count if more than 1 second between taps
    if (now - lastTapTime > 1000) {
      setTapCount(1);
    } else {
      setTapCount(prev => prev + 1);
    }
    
    setLastTapTime(now);

    // Show dev menu after 3 taps
    if (tapCount === 2) {
      setTapCount(0);
      showDevMenu();
    }
  };

  const showDevMenu = () => {
    setIsVisible(true);
    playInteraction('button_success');
    
    Animated.parallel([
      Animated.timing(modalOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(modalScale, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const hideDevMenu = () => {
    Animated.parallel([
      Animated.timing(modalOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(modalScale, {
        toValue: 0.8,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      setIsVisible(false);
    });
  };

  const handleDevAction = async (action: () => void, title: string) => {
    hideDevMenu();
    playInteraction('button_primary');
    
    // Small delay to let modal close
    setTimeout(() => {
      action();
    }, 250);
  };

  const handleComprehensiveReset = async () => {
    Alert.alert(
      'Complete Reset',
      'This will delete ALL your data:\n\n• All children profiles\n• All tasks and completions\n• All rewards and redemptions\n• All achievements\n• Family settings\n• Subscriptions\n• Tutorial progress\n\nYou\'ll start completely fresh. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset Everything', 
          style: 'destructive',
          onPress: async () => {
            setIsResetting(true);
            hideDevMenu();
            
            try {
              await userApi.resetDevData();
              
              Alert.alert(
                'Reset Complete! 🎉',
                'All data has been cleared. The app will now restart to show the fresh onboarding flow.',
                [
                  { 
                    text: 'Restart App', 
                    onPress: () => {
                      // Trigger the onboarding reset which will refresh the app state
                      onResetOnboarding();
                    }
                  }
                ]
              );
            } catch (error) {
              console.error('Error during comprehensive reset:', error);
              Alert.alert(
                'Reset Failed',
                'There was an error resetting your data. Please try again or contact support.',
                [{ text: 'OK' }]
              );
            } finally {
              setIsResetting(false);
            }
          }
        },
      ]
    );
  };

  const handleResetOnboarding = () => {
    Alert.alert(
      'Reset Onboarding Only',
      'This will only reset the onboarding flow without deleting your data. You\'ll go through setup again but keep your existing children, tasks, and rewards.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset Onboarding', 
          style: 'destructive',
          onPress: () => handleDevAction(onResetOnboarding, 'Reset Onboarding')
        },
      ]
    );
  };

  const handleStartTutorial = () => {
    Alert.alert(
      'Start Tutorial',
      'This will start the guided tutorial overlay. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Start Tutorial', 
          onPress: () => {
            startTutorial();
            setIsVisible(false);
          }
        },
      ]
    );
  };

  const handleResetTutorial = () => {
    Alert.alert(
      'Reset Tutorial Progress',
      'This will reset your tutorial progress so you can go through it again. Are you sure?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset Tutorial', 
          onPress: async () => {
            try {
              await userApi.updateTutorialProgress({
                completed: false,
                currentStep: 0,
              });
              Alert.alert('Success', 'Tutorial progress has been reset. Please restart the app to see the tutorial.');
              setIsVisible(false);
            } catch (error) {
              console.error('Error resetting tutorial:', error);
              Alert.alert('Error', 'Failed to reset tutorial progress.');
            }
          }
        },
      ]
    );
  };

  const handleSignOut = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Sign Out', 
          style: 'destructive',
          onPress: () => handleDevAction(onSignOut, 'Sign Out')
        },
      ]
    );
  };

  return (
    <>
      {/* Hidden corner tap area */}
      <TouchableOpacity
        style={styles.cornerTapArea}
        onPress={handleCornerTap}
        activeOpacity={1}
      />

      {/* Dev Menu Modal */}
      <Modal
        visible={isVisible}
        transparent
        animationType="none"
        onRequestClose={hideDevMenu}
      >
        <TouchableOpacity
          style={styles.modalOverlay}
          activeOpacity={1}
          onPress={hideDevMenu}
        >
          <Animated.View
            style={[
              styles.modalContent,
              {
                opacity: modalOpacity,
                transform: [{ scale: modalScale }],
              },
            ]}
          >
            <TouchableOpacity
              activeOpacity={1}
              onPress={(e) => e.stopPropagation()}
            >
              <LinearGradient
                colors={['#667eea', '#764ba2', '#f093fb']}
                style={styles.menuContainer}
              >
                <Text style={styles.menuTitle}>🔧 Dev Menu</Text>
                
                <View style={styles.menuItems}>
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => handleDevAction(onShowSettings, 'Settings')}
                  >
                    <Text style={styles.menuItemText}>⚙️ Settings & Audio</Text>
                    <Text style={styles.menuItemSubtext}>Test audio controls</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={handleResetOnboarding}
                  >
                    <Text style={styles.menuItemText}>🔄 Reset Onboarding Only</Text>
                    <Text style={styles.menuItemSubtext}>Keep data, reset flow</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={handleStartTutorial}
                  >
                    <Text style={styles.menuItemText}>🎓 Start Tutorial</Text>
                    <Text style={styles.menuItemSubtext}>Test guided tutorial overlay</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={handleResetTutorial}
                  >
                    <Text style={styles.menuItemText}>🔄 Reset Tutorial</Text>
                    <Text style={styles.menuItemSubtext}>Reset tutorial progress</Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={[styles.menuItem, styles.dangerItem]}
                    onPress={handleComprehensiveReset}
                    disabled={isResetting}
                  >
                    <Text style={styles.menuItemText}>
                      {isResetting ? '🔄 Resetting...' : '🗑️ Complete Reset'}
                    </Text>
                    <Text style={styles.menuItemSubtext}>
                      {isResetting ? 'Clearing all data...' : 'Delete ALL data & start fresh'}
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={handleSignOut}
                  >
                    <Text style={styles.menuItemText}>🚪 Sign Out</Text>
                    <Text style={styles.menuItemSubtext}>Test Apple login</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={styles.closeButton}
                  onPress={hideDevMenu}
                >
                  <Text style={styles.closeButtonText}>✕ Close</Text>
                </TouchableOpacity>
              </LinearGradient>
            </TouchableOpacity>
          </Animated.View>
        </TouchableOpacity>
      </Modal>
    </>
  );
};

const styles = StyleSheet.create({
  cornerTapArea: {
    position: 'absolute',
    top: 0,
    right: 0,
    width: 120,
    height: 120,
    zIndex: 1000,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: SCREEN_WIDTH * 0.85,
    maxWidth: 400,
  },
  menuContainer: {
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  menuTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 20,
  },
  menuItems: {
    gap: 12,
  },
  menuItem: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  menuItemSubtext: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.8)',
  },
  closeButton: {
    marginTop: 20,
    paddingVertical: 12,
    paddingHorizontal: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  dangerItem: {
    backgroundColor: 'rgba(255, 107, 107, 0.15)',
    borderColor: 'rgba(255, 107, 107, 0.3)',
  },
});

export default DevMenu; 