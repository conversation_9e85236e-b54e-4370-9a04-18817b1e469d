import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { useSmartChildSelection } from '../hooks';

export const TestGraphQL: React.FC = () => {
  const { children, loading, error, selectedChildId, selectChild } = useSmartChildSelection();
  
  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Loading children...</Text>
      </View>
    );
  }
  
  if (error) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>Error: {error}</Text>
      </View>
    );
  }
  
  return (
    <View style={styles.container}>
      <Text style={styles.title}>GraphQL Test</Text>
      <Text style={styles.text}>Children loaded: {children.length}</Text>
      {children.map(child => (
        <Text key={child.id} style={styles.childText}>
          {child.name} - Level {child.level} - {child.coin_balance} coins
        </Text>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: '#f0f0f0',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  text: {
    fontSize: 14,
    marginBottom: 5,
  },
  childText: {
    fontSize: 12,
    marginBottom: 3,
    color: '#666',
  },
}); 