import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar 
} from '../components/ui';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
}

interface KioskChildSelectorProps {
  visible: boolean;
  onClose: () => void;
  onChildSelect: (childId: string) => void;
  currentChildId?: string;
  children?: Child[]; // Accept pre-loaded children to avoid redundant API calls
}

const KioskChildSelector: React.FC<KioskChildSelectorProps> = ({
  visible,
  onClose,
  onChildSelect,
  currentChildId,
  children = [] // Use pre-loaded children ⚡
}) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // State
  const [selectedChildId, setSelectedChildId] = useState<string | null>(currentChildId || null);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  // Handle visibility changes
  useEffect(() => {
    if (visible) {
      setSelectedChildId(currentChildId || null);
      
      // Entrance animations
      Animated.parallel([
        Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(slideInAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
      ]).start();

      // Sparkle animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(sparkleAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(sparkleAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      // Exit animations
      Animated.parallel([
        Animated.timing(fadeInAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(slideInAnim, {
          toValue: SCREEN_HEIGHT,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  // Auto-select first child if none is selected
  useEffect(() => {
    if (!currentChildId && children.length > 0) {
      setSelectedChildId(children[0].id);
    }
  }, [children, currentChildId]);

  const handleChildSelect = (childId: string) => {
    playSound('button_press');
    setSelectedChildId(childId);
  };

  const handleConfirmSelection = () => {
    if (selectedChildId) {
      playSound('button_success');
      onChildSelect(selectedChildId);
      onClose();
    }
  };

  const handleCancel = () => {
    playSound('button_press');
    onClose();
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="none"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        
        {/* Overlay */}
        <Animated.View 
          style={[
            styles.overlay,
            { opacity: fadeInAnim }
          ]}
        />
        
        {/* Content */}
        <Animated.View 
          style={[
            styles.content,
            {
              opacity: fadeInAnim,
              transform: [
                { translateY: slideInAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <Text style={styles.headerTitle}>👶 Choose Your Profile</Text>
            <Text style={styles.headerSubtitle}>
              Select which child you are to continue
            </Text>
          </View>

          {/* Children Grid */}
          <View style={styles.childrenGrid}>
            {children.map((child) => (
              <TouchableOpacity
                key={child.id}
                style={[
                  styles.childCard,
                  selectedChildId === child.id && styles.childCardSelected
                ]}
                onPress={() => handleChildSelect(child.id)}
              >
                <NintendoCard 
                  variant="elevated" 
                  withShadow 
                  style={{
                    ...styles.childCardContent,
                    ...(selectedChildId === child.id ? styles.childCardContentSelected : {})
                  }}
                >
                  <View style={styles.childAvatarContainer}>
                    <Text style={styles.childAvatar}>{child.avatar}</Text>
                    {selectedChildId === child.id && (
                      <Animated.View 
                        style={[
                          styles.selectionIndicator,
                          { opacity: sparkleAnim }
                        ]}
                      >
                        <Text style={styles.selectionIndicatorText}>✨</Text>
                      </Animated.View>
                    )}
                  </View>
                  
                  <Text style={styles.childName}>{child.name}</Text>
                  
                  <View style={styles.childStats}>
                    <Text style={styles.childCoins}>{child.coin_balance} 🪙</Text>
                    <Text style={styles.childLevel}>Level {child.level}</Text>
                  </View>
                  
                  <View style={styles.childStreak}>
                    <Text style={styles.streakText}>
                      🔥 {child.daily_streak} day streak
                    </Text>
                  </View>
                </NintendoCard>
              </TouchableOpacity>
            ))}
          </View>

          {/* Action Buttons */}
          <View style={styles.actions}>
            <NintendoButton
              title="Cancel"
              onPress={handleCancel}
              variant="secondary"
              style={styles.actionButton}
            />
            <NintendoButton
              title={selectedChildId ? "Continue" : "Select a Child"}
              onPress={handleConfirmSelection}
              variant="success"
              disabled={!selectedChildId}
              style={styles.actionButton}
            />
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  content: {
    width: '90%',
    maxWidth: 400,
    maxHeight: '80%',
  },
  loadingContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 18,
    marginTop: 20,
    textAlign: 'center',
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  headerTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  headerSubtitle: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
  },
  childrenGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 15,
    marginBottom: 30,
  },
  childCard: {
    width: '45%',
    minWidth: 150,
  },
  childCardSelected: {
    transform: [{ scale: 1.05 }],
  },
  childCardContent: {
    padding: 20,
    alignItems: 'center',
  },
  childCardContentSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderColor: '#FFD700',
    borderWidth: 2,
  },
  childAvatarContainer: {
    position: 'relative',
    marginBottom: 10,
  },
  childAvatar: {
    fontSize: 48,
  },
  selectionIndicator: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: '#FFD700',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectionIndicatorText: {
    fontSize: 16,
  },
  childName: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  childStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 8,
  },
  childCoins: {
    color: '#FFD700',
    fontSize: 14,
    fontWeight: 'bold',
  },
  childLevel: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.8,
  },
  childStreak: {
    alignItems: 'center',
  },
  streakText: {
    color: '#ffffff',
    fontSize: 12,
    opacity: 0.7,
  },
  actions: {
    flexDirection: 'row',
    gap: 15,
  },
  actionButton: {
    flex: 1,
  },
});

export default KioskChildSelector; 