import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { NintendoButton, NintendoCard } from './ui';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface PostOnboardingTutorialProps {
  visible: boolean;
  onComplete: () => void;
  onNavigateToTaskCreation: () => void;
  onNavigateToRewardCreation: () => void;
  tutorialStep: number;
  hasTasks: boolean;
  hasRewards: boolean;
}

const TUTORIAL_STEPS = [
  {
    title: 'Welcome to KidsCoin! 🎉',
    description: 'Let\'s create your first task and reward to get started. This will help you learn the actual interface while setting up your family!',
    action: 'continue',
    buttonText: 'Let\'s Start!',
  },
  {
    title: 'Create Your First Task 📋',
    description: 'Tasks are the foundation of KidsCoin. Let\'s create one using the real task creation interface.',
    action: 'navigate_tasks',
    buttonText: 'Create Task',
  },
  {
    title: 'Great Job! Now Create a Reward 🎁',
    description: 'Now let\'s set up something exciting for your child to earn with their coins!',
    action: 'navigate_rewards',
    buttonText: 'Create Reward',
  },
  {
    title: 'Perfect! You\'re All Set! ✨',
    description: 'You\'ve successfully created your first task and reward. Your child can now start earning coins and unlocking rewards!',
    action: 'complete',
    buttonText: 'Start Using KidsCoin!',
  },
];

export const PostOnboardingTutorial: React.FC<PostOnboardingTutorialProps> = ({
  visible,
  onComplete,
  onNavigateToTaskCreation,
  onNavigateToRewardCreation,
  tutorialStep,
  hasTasks,
  hasRewards,
}) => {
  const { playSound } = useAudio();
  const [currentStep, setCurrentStep] = useState(tutorialStep);
  const [isAnimating, setIsAnimating] = useState(false);
  
  // Animation refs
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;

  useEffect(() => {
    if (visible) {
      setIsAnimating(true);
      
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start(() => {
        setIsAnimating(false);
      });
    }
  }, [visible]);

  useEffect(() => {
    // Update current step based on progress
    if (tutorialStep === 1 && hasTasks) {
      setCurrentStep(2); // Move to reward creation
    } else if (tutorialStep === 2 && hasRewards) {
      setCurrentStep(3); // Move to completion
    }
  }, [tutorialStep, hasTasks, hasRewards]);

  const handleAction = () => {
    if (isAnimating) return;
    
    playSound('button_press');
    const step = TUTORIAL_STEPS[currentStep];
    
    switch (step.action) {
      case 'continue':
        setCurrentStep(1);
        break;
      case 'navigate_tasks':
        onNavigateToTaskCreation();
        break;
      case 'navigate_rewards':
        onNavigateToRewardCreation();
        break;
      case 'complete':
        onComplete();
        break;
    }
  };

  const handleSkip = () => {
    Alert.alert(
      'Skip Tutorial?',
      'You can always create tasks and rewards later from the main dashboard.',
      [
        { text: 'Continue Tutorial', style: 'cancel' },
        { text: 'Skip', onPress: onComplete },
      ]
    );
  };

  if (!visible) return null;

  const currentStepData = TUTORIAL_STEPS[currentStep];

  return (
    <Modal
      visible={visible}
      animationType="none"
      transparent={true}
      statusBarTranslucent={true}
    >
      <View style={styles.overlay}>
        <LinearGradient
          colors={['rgba(102, 126, 234, 0.95)', 'rgba(118, 75, 162, 0.95)']}
          style={StyleSheet.absoluteFill}
        />
        
        <Animated.View
          style={[
            styles.container,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim },
              ],
            },
          ]}
        >
          <NintendoCard variant="elevated" withShadow style={styles.card}>
            {/* Progress Indicator */}
            <View style={styles.progressContainer}>
              <View style={styles.progressBar}>
                <View
                  style={[
                    styles.progressFill,
                    { width: `${((currentStep + 1) / TUTORIAL_STEPS.length) * 100}%` },
                  ]}
                />
              </View>
              <Text style={styles.progressText}>
                {currentStep + 1} of {TUTORIAL_STEPS.length}
              </Text>
            </View>

            {/* Content */}
            <View style={styles.content}>
              <Text style={styles.title}>{currentStepData.title}</Text>
              <Text style={styles.description}>{currentStepData.description}</Text>
              
              {/* Step-specific content */}
              {currentStep === 1 && (
                <View style={styles.stepInfo}>
                  <Text style={styles.stepInfoText}>
                    ✅ You'll use the real task creation interface
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Learn where everything is located
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Create a task your child can actually complete
                  </Text>
                </View>
              )}
              
              {currentStep === 2 && (
                <View style={styles.stepInfo}>
                  <Text style={styles.stepInfoText}>
                    ✅ Task created successfully!
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Now let's create a reward using the real interface
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Your child will be able to spend coins on this
                  </Text>
                </View>
              )}
              
              {currentStep === 3 && (
                <View style={styles.stepInfo}>
                  <Text style={styles.stepInfoText}>
                    ✅ Task created successfully!
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Reward created successfully!
                  </Text>
                  <Text style={styles.stepInfoText}>
                    ✅ Your child can now start earning and spending coins!
                  </Text>
                </View>
              )}
            </View>

            {/* Actions */}
            <View style={styles.actions}>
              <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
                <Text style={styles.skipButtonText}>Skip Tutorial</Text>
              </TouchableOpacity>
              
              <NintendoButton
                title={currentStepData.buttonText}
                onPress={handleAction}
                variant="success"
                style={styles.actionButton}
              />
            </View>
          </NintendoCard>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  container: {
    width: '100%',
    maxWidth: 400,
  },
  card: {
    padding: 30,
  },
  progressContainer: {
    marginBottom: 30,
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    marginTop: 8,
  },
  content: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  stepInfo: {
    alignSelf: 'stretch',
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 12,
    padding: 15,
  },
  stepInfoText: {
    fontSize: 14,
    color: '#4CAF50',
    marginBottom: 5,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  skipButton: {
    padding: 10,
  },
  skipButtonText: {
    fontSize: 14,
    color: '#999',
    textDecorationLine: 'underline',
  },
  actionButton: {
    flex: 1,
    marginLeft: 20,
  },
});

export default PostOnboardingTutorial; 