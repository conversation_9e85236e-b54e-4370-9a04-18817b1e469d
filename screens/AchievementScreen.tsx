import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar,
  ChildProfileSkeleton
} from '../components/ui';
import { supabase } from '../utils/supabase';
import { checkAchievements } from '../utils/rewardValidation';
import { useChildren, useTasks, useRewards } from '../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface AchievementScreenProps {
  onBack: () => void;
  selectedChildId?: string;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  criteria_type: string;
  criteria_value: number;
  coin_reward: number;
  created_at: string;
  is_unlocked: boolean;
  unlocked_at?: string;
}

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
}

const AchievementScreen: React.FC<AchievementScreenProps> = ({ onBack, selectedChildId }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // Simplified hooks
  const { children, loading: loadingChildren } = useChildren();
  const { tasks, loading: loadingTasks } = useTasks();
  const { rewards, loading: loadingRewards } = useRewards();
  const [selectedChild, setSelectedChild] = useState<string>(selectedChildId || '');
  
  // State  
  const loading = loadingChildren || loadingTasks || loadingRewards;
  const [refreshing, setRefreshing] = useState(false);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;

  // Auto-select first child if none selected
  useEffect(() => {
    if (!selectedChild && children.length > 0) {
      setSelectedChild(children[0].id);
    }
  }, [selectedChild, children]);

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Get selected child data
  const selectedChildData = children.find(c => c.id === selectedChild);
  const completedTasks = tasks.filter(task => task.status === 'completed' && task.child_id === selectedChild);

  const handleRefresh = async () => {
    setRefreshing(true);
    playSound('button_press');
    
    try {
      // Check for new achievements
      if (selectedChild) {
        await checkAchievements(selectedChild);
      }
    } catch (error) {
      console.error('Error refreshing achievements:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleChildSelect = (childId: string) => {
    setSelectedChild(childId);
    playSound('button_press');
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ChildProfileSkeleton />
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
    >
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }],
          },
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Achievements</Text>
        </View>

        {/* Child Selector */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.childSelector}>
          {children.map((child) => (
            <TouchableOpacity
              key={child.id}
              style={[
                styles.childOption,
                selectedChild === child.id && styles.selectedChildOption,
              ]}
              onPress={() => handleChildSelect(child.id)}
            >
              <Text style={[
                styles.childOptionText,
                selectedChild === child.id && styles.selectedChildOptionText,
              ]}>
                {child.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>

        {/* Content */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {selectedChildData ? (
            <View style={styles.contentContainer}>
              <Text style={styles.sectionTitle}>Child Progress</Text>
              <NintendoCard style={styles.statsCard}>
                <Text style={styles.statsText}>
                  Level: {selectedChildData.level} | XP: {selectedChildData.xp}
                </Text>
                <Text style={styles.statsText}>
                  Coins: {selectedChildData.coin_balance} | Streak: {selectedChildData.daily_streak} days
                </Text>
                <Text style={styles.statsText}>
                  Completed Tasks: {completedTasks.length}
                </Text>
              </NintendoCard>
              
              <Text style={styles.sectionTitle}>Available Rewards</Text>
              {rewards.map((reward) => (
                <NintendoCard key={reward.id} style={styles.rewardCard}>
                  <Text style={styles.rewardTitle}>{reward.title}</Text>
                  <Text style={styles.rewardDescription}>{reward.description}</Text>
                  <Text style={styles.rewardCost}>{reward.cost} coins</Text>
                </NintendoCard>
              ))}
            </View>
          ) : (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>No child selected</Text>
            </View>
          )}
        </ScrollView>
      </Animated.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
  },
  title: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
  },
  childSelector: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  childOption: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
  },
  selectedChildOption: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
  },
  childOptionText: {
    color: 'white',
    fontSize: 16,
  },
  selectedChildOptionText: {
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
  },
  statsCard: {
    marginBottom: 30,
  },
  statsText: {
    fontSize: 16,
    marginBottom: 5,
  },
  rewardCard: {
    marginBottom: 15,
  },
  rewardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  rewardCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B9D',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
  },
});

export default AchievementScreen;
