import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  TextInput,
  Alert,
  Animated,
  Dimensions
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { OnboardingData } from './OnboardingNavigator';
import { useAuth } from '../../contexts/AuthContext';
import { useAudio } from '../../contexts/AudioContext';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChildCreationScreenProps {
  onNext: (data?: Partial<OnboardingData>) => void;
  onPrev: () => void;
  data: OnboardingData;
  isActive: boolean;
  triggerHaptic: (type: 'light' | 'medium' | 'heavy') => void;
}

const AVATAR_OPTIONS = ['😊', '😄', '🤗', '😎', '🥳', '🤓', '😋', '🙂', '😃', '😆', '🌟', '🦄', '🐻', '🐱', '🦊', '🐸'];

const ChildCreationScreen: React.FC<ChildCreationScreenProps> = ({ 
  onNext, 
  onPrev, 
  data, 
  isActive, 
  triggerHaptic 
}) => {
  const { session } = useAuth();
  const { playSound } = useAudio();
  const [childName, setChildName] = useState('');
  const [childAge, setChildAge] = useState('');
  const [selectedAvatar, setSelectedAvatar] = useState('😊');
  const [loading, setLoading] = useState(false);
  const [existingChildren, setExistingChildren] = useState<any[]>([]);

  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const avatarScales = useRef(AVATAR_OPTIONS.map(() => new Animated.Value(1))).current;
  const previewScale = useRef(new Animated.Value(0.8)).current;
  const celebrationScale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isActive) {
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideInAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(previewScale, {
          toValue: 1,
          tension: 50,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();

      // Check for existing children and populate form if found
      fetchExistingChildren();
    }
  }, [isActive]);

  const fetchExistingChildren = async () => {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/children`, {
        headers: {
          'Authorization': `Bearer ${session?.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const children = await response.json();
        setExistingChildren(children);
        
        // If there's already a child created and onboarding data doesn't have children,
        // populate the form with the first child's data for editing
        if (children.length > 0 && data.children.length === 0) {
          const firstChild = children[0];
          setChildName(firstChild.name);
          setChildAge(firstChild.age ? firstChild.age.toString() : '');
          setSelectedAvatar(firstChild.avatar);
        }
      }
    } catch (error) {
      console.error('Error fetching existing children:', error);
      // Continue with empty state
    }
  };

  const handleAvatarSelect = (avatar: string, index: number) => {
    playSound('selection_change'); // Play sound for avatar selection
    triggerHaptic('light');
    setSelectedAvatar(avatar);
    
    // Bounce animation for selected avatar
    Animated.sequence([
      Animated.timing(avatarScales[index], {
        toValue: 1.3,
        duration: 150,
        useNativeDriver: true,
      }),
      Animated.timing(avatarScales[index], {
        toValue: 1,
        duration: 150,
        useNativeDriver: true,
      }),
    ]).start();

    // Preview update animation
    Animated.sequence([
      Animated.timing(previewScale, {
        toValue: 1.1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(previewScale, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handleNext = async () => {
    if (!childName.trim()) {
      triggerHaptic('heavy');
      Alert.alert('Missing Information', 'Please enter your child\'s name to continue.');
      return;
    }

    const isEditingExisting = existingChildren.length > 0 && data.children.length === 0;

    if (!isEditingExisting) {
      // Only check limits if we're creating a new child
      try {
        const limitsResponse = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/monetization/check-child-limit`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session?.access_token}`,
            'Content-Type': 'application/json',
          },
        });

        if (limitsResponse.ok) {
          const limitsData = await limitsResponse.json();
          if (!limitsData.can_add_child) {
            Alert.alert(
              'Child Limit Reached',
              'You\'ve reached your current child limit. Please upgrade your plan to add more children.',
              [{ text: 'OK', style: 'default' }]
            );
            return;
          }
        }
      } catch (error) {
        console.error('Error checking child limit:', error);
        // Continue anyway - don't block the flow
      }
    }

    setLoading(true);
    triggerHaptic('medium');

    try {
      const childData = {
        name: childName.trim(),
        age: childAge ? parseInt(childAge) : null,
        avatar: selectedAvatar,
      };

      let resultChild;

      if (isEditingExisting) {
        // Update existing child
        const existingChild = existingChildren[0];
        const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/children/${existingChild.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${session?.access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(childData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to update child profile');
        }

        resultChild = await response.json();
      } else {
        // Create new child
        const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/children`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${session?.access_token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(childData),
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to create child profile');
        }

        resultChild = await response.json();
      }

      // Celebration animation
      Animated.sequence([
        Animated.spring(celebrationScale, {
          toValue: 1,
          tension: 50,
          friction: 4,
          useNativeDriver: true,
        }),
        Animated.timing(celebrationScale, {
          toValue: 0,
          duration: 300,
          delay: 800,
          useNativeDriver: true,
        }),
      ]).start();

      // Continue to next step
      setTimeout(() => {
        onNext({
          children: [
            {
              name: resultChild.name,
              age: resultChild.age,
              avatar: resultChild.avatar,
              startingCoins: resultChild.coin_balance || 0,
              level: resultChild.level || 1,
            },
          ],
        });
      }, 1200);

    } catch (error) {
      console.error('Error with child profile:', error);
      triggerHaptic('heavy');
      Alert.alert(
        'Error', 
        error instanceof Error ? error.message : 'Unable to save child profile. Please try again.'
      );
    } finally {
      setLoading(false);
    }
  };

  if (!isActive) return null;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Animated.View 
        style={[
          styles.animatedContainer,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <View style={styles.header}>
          <Text style={styles.title}>
            {existingChildren.length > 0 && data.children.length === 0 
              ? "Edit Child Profile" 
              : "Create Your First Child"
            }
          </Text>
          <Text style={styles.subtitle}>
            {existingChildren.length > 0 && data.children.length === 0
              ? "Update your child's information below 📝"
              : "Let's set up a profile for one of your amazing kids! 🌟"
            }
          </Text>
        </View>

        {/* Freemium Info */}
        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>🎯 Starting Smart</Text>
          <Text style={styles.infoText}>
            Begin with one child to explore all of KidsCoin's features. You can add more family members later with our family plans!
          </Text>
        </View>

        {/* Child Creation Form */}
        <View style={styles.formSection}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Child's Name *</Text>
            <TextInput
              style={styles.textInput}
              value={childName}
              onChangeText={setChildName}
              placeholder="Enter name..."
              placeholderTextColor="#A0A0A0"
              maxLength={20}
              editable={!loading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Age (Optional)</Text>
            <TextInput
              style={styles.textInput}
              value={childAge}
              onChangeText={setChildAge}
              placeholder="Enter age..."
              placeholderTextColor="#A0A0A0"
              keyboardType="numeric"
              maxLength={2}
              editable={!loading}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Choose Avatar</Text>
            <View style={styles.avatarGrid}>
              {AVATAR_OPTIONS.map((avatar, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.avatarButton,
                    selectedAvatar === avatar && styles.avatarButtonSelected
                  ]}
                  onPress={() => handleAvatarSelect(avatar, index)}
                  disabled={loading}
                >
                  <Animated.View
                    style={[
                      styles.avatarContainer,
                      { transform: [{ scale: avatarScales[index] }] }
                    ]}
                  >
                    <Text style={styles.avatarEmoji}>{avatar}</Text>
                  </Animated.View>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        </View>

        {/* Preview Card */}
        <Animated.View 
          style={[
            styles.previewCard,
            { transform: [{ scale: previewScale }] }
          ]}
        >
          <Text style={styles.previewTitle}>Preview</Text>
          <View style={styles.previewContent}>
            <Text style={styles.previewAvatar}>{selectedAvatar}</Text>
            <View style={styles.previewDetails}>
              <Text style={styles.previewName}>
                {childName.trim() || 'Child Name'}
              </Text>
              <Text style={styles.previewAge}>
                {childAge ? `Age ${childAge}` : 'Age not set'}
              </Text>
              <Text style={styles.previewCoins}>Starting their journey! 🪙</Text>
            </View>
          </View>
        </Animated.View>

        <TouchableOpacity 
          style={[
            styles.nextButton,
            (!childName.trim() || loading) && styles.nextButtonDisabled
          ]} 
          onPress={handleNext}
          disabled={!childName.trim() || loading}
        >
          <LinearGradient
            colors={(!childName.trim() || loading) ? ['#A0A0A0', '#808080'] : ['#32CD32', '#228B22']}
            style={styles.nextButtonGradient}
          >
            <Text style={[
              styles.nextButtonText,
              (!childName.trim() || loading) && styles.nextButtonTextDisabled
            ]}>
              {loading 
                ? (existingChildren.length > 0 && data.children.length === 0 ? 'Updating Profile...' : 'Creating Profile...')
                : (existingChildren.length > 0 && data.children.length === 0 ? 'Update Profile →' : 'Create Profile →')
              }
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity style={styles.backButton} onPress={onPrev} disabled={loading}>
          <Text style={[styles.backButtonText, loading && styles.backButtonTextDisabled]}>← Back</Text>
        </TouchableOpacity>

        {/* Celebration Overlay */}
        {loading && (
          <Animated.View 
            style={[
              styles.celebrationOverlay,
              { transform: [{ scale: celebrationScale }] }
            ]}
          >
            <Text style={styles.celebrationEmoji}>🎉</Text>
            <Text style={styles.celebrationText}>
              {existingChildren.length > 0 && data.children.length === 0 
                ? 'Updating magical profile!' 
                : 'Creating magical profile!'
              }
            </Text>
          </Animated.View>
        )}
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 24,
    paddingTop: 60,
  },
  animatedContainer: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#E8E8E8',
    textAlign: 'center',
    lineHeight: 24,
  },
  infoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#E0E0E0',
    lineHeight: 20,
  },
  formSection: {
    marginBottom: 32,
  },
  inputContainer: {
    marginBottom: 24,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  avatarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  avatarButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  avatarButtonSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: '#4ECDC4',
  },
  avatarContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarEmoji: {
    fontSize: 24,
  },
  previewCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  previewTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 16,
  },
  previewContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  previewAvatar: {
    fontSize: 40,
    marginRight: 16,
  },
  previewDetails: {
    flex: 1,
  },
  previewName: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  previewAge: {
    fontSize: 14,
    color: '#C8C8C8',
    marginBottom: 4,
  },
  previewCoins: {
    fontSize: 14,
    color: '#4ECDC4',
    fontWeight: '600',
  },
  nextButton: {
    backgroundColor: '#4ECDC4',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    marginBottom: 16,
  },
  nextButtonDisabled: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  nextButtonGradient: {
    flex: 1,
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  nextButtonTextDisabled: {
    color: '#C8C8C8',
  },
  backButton: {
    alignItems: 'center',
    paddingVertical: 12,
  },
  backButtonText: {
    fontSize: 16,
    color: '#FFFFFF',
    opacity: 0.8,
  },
  backButtonTextDisabled: {
    opacity: 0.5,
  },
  celebrationOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -SCREEN_WIDTH * 0.2 }, { translateY: -SCREEN_WIDTH * 0.2 }],
    backgroundColor: 'rgba(0,0,0,0.7)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    zIndex: 10,
  },
  celebrationEmoji: {
    fontSize: 60,
    marginBottom: 10,
  },
  celebrationText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
});

export default ChildCreationScreen; 