import React, { useRef, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, Dimensions, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { OnboardingData } from './OnboardingNavigator';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface SummaryScreenProps {
  onNext: (data?: Partial<OnboardingData>) => void;
  onPrev: () => void;
  data: OnboardingData;
  isActive: boolean;
  triggerHaptic: (type: 'light' | 'medium' | 'heavy') => void;
}

const SummaryScreen: React.FC<SummaryScreenProps> = ({ 
  onNext, 
  onPrev, 
  data, 
  isActive, 
  triggerHaptic 
}) => {
  const celebrationScale = useRef(new Animated.Value(0)).current;
  const sparkleOpacity = useRef(new Animated.Value(0)).current;
  const contentFadeIn = useRef(new Animated.Value(0)).current;
  const cardScale = useRef(new Animated.Value(0.8)).current;
  const floatingAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isActive) {
      // Celebration animation sequence
      Animated.sequence([
        // Initial celebration appears
        Animated.spring(celebrationScale, {
          toValue: 1,
          tension: 50,
          friction: 4,
          useNativeDriver: true,
        }),
        // Content fades in
        Animated.parallel([
          Animated.timing(contentFadeIn, {
            toValue: 1,
            duration: 600,
            useNativeDriver: true,
          }),
          Animated.spring(cardScale, {
            toValue: 1,
            tension: 60,
            friction: 8,
            useNativeDriver: true,
          }),
        ]),
        // Sparkles appear
        Animated.timing(sparkleOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
      ]).start();
      
      // Continuous floating animation
      const floatingLoop = Animated.loop(
        Animated.sequence([
          Animated.timing(floatingAnim, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(floatingAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      );
      floatingLoop.start();
      
      // Haptic celebration
      setTimeout(() => triggerHaptic('heavy'), 300);
      setTimeout(() => triggerHaptic('medium'), 800);

      return () => floatingLoop.stop();
    }
  }, [isActive]);

  const handleFinish = () => {
    triggerHaptic('heavy');
    onNext();
  };

  const childName = data.children[0]?.name || 'Your child';
  const familyName = data.familySettings?.familyName || 'Your family';

  if (!isActive) return null;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />
      
      <ScrollView 
        style={styles.scrollView}
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        {/* Main Celebration */}
        <Animated.View 
          style={[
            styles.celebrationContainer,
            { 
              transform: [
                { scale: celebrationScale },
                { 
                  translateY: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -20],
                  })
                }
              ]
            }
          ]}
        >
          <Text style={styles.celebration}>🎉</Text>
        </Animated.View>
        
        <Animated.View style={{ opacity: contentFadeIn }}>
          <Text style={styles.title}>You're All Set!</Text>
          <Text style={styles.subtitle}>
            Welcome to the KidsCoin family! 🎊
          </Text>
          
          {/* Achievement Cards */}
          <Animated.View 
            style={[
              styles.achievementSection,
              { transform: [{ scale: cardScale }] }
            ]}
          >
            <Text style={styles.achievementTitle}>🏆 What You've Accomplished:</Text>
            
            <View style={styles.achievementCard}>
              <View style={styles.achievementItem}>
                <Text style={styles.achievementEmoji}>👨‍👩‍👧‍👦</Text>
                <View style={styles.achievementDetails}>
                  <Text style={styles.achievementText}>Family Setup Complete</Text>
                  <Text style={styles.achievementSubtext}>{familyName}</Text>
                </View>
              </View>
              
              <View style={styles.achievementItem}>
                <Text style={styles.achievementEmoji}>👶</Text>
                <View style={styles.achievementDetails}>
                  <Text style={styles.achievementText}>Child Profile Created</Text>
                  <Text style={styles.achievementSubtext}>{childName} is ready to start!</Text>
                </View>
              </View>
              
              <View style={styles.achievementItem}>
                <Text style={styles.achievementEmoji}>🔧</Text>
                <View style={styles.achievementDetails}>
                  <Text style={styles.achievementText}>System Configured</Text>
                  <Text style={styles.achievementSubtext}>
                    {data.familySettings?.taskVerificationMode === 'strict' ? 'Strict Mode' : 'Trusting Mode'} enabled
                  </Text>
                </View>
              </View>
              
              <View style={styles.achievementItem}>
                <Text style={styles.achievementEmoji}>🎯</Text>
                <View style={styles.achievementDetails}>
                  <Text style={styles.achievementText}>Setup Complete</Text>
                  <Text style={styles.achievementSubtext}>Ready to start your journey!</Text>
                </View>
              </View>
            </View>
          </Animated.View>

          {/* Next Steps Section */}
          <Animated.View style={[styles.nextStepsSection, { opacity: sparkleOpacity }]}>
            <Text style={styles.nextStepsTitle}>✨ What's Next:</Text>
            <View style={styles.nextStepsList}>
              <Text style={styles.nextStepItem}>🎯 Follow the guided tutorial to create your first task and reward</Text>
              <Text style={styles.nextStepItem}>📱 Switch to Kid Mode to test the experience</Text>
              <Text style={styles.nextStepItem}>🚀 Start the financial literacy journey!</Text>
            </View>
          </Animated.View>
        </Animated.View>
        
        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={handleFinish}>
            <LinearGradient
              colors={['#32CD32', '#228B22']}
              style={styles.buttonGradient}
            >
              <Text style={styles.buttonText}>Let's Go! 🚀</Text>
              <Text style={styles.buttonSubtext}>Enter the KidsCoin app</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
        
        <TouchableOpacity style={styles.backButton} onPress={onPrev}>
          <Text style={styles.backButtonText}>← Back to Tutorial</Text>
        </TouchableOpacity>

        {/* Floating Sparkles */}
        <Animated.View 
          style={[
            styles.sparkle,
            styles.sparkle1,
            { 
              opacity: sparkleOpacity,
              transform: [
                { 
                  translateY: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -15],
                  })
                }
              ]
            }
          ]}
        >
          <Text style={styles.sparkleText}>✨</Text>
        </Animated.View>
        
        <Animated.View 
          style={[
            styles.sparkle,
            styles.sparkle2,
            { 
              opacity: sparkleOpacity,
              transform: [
                { 
                  translateY: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, 10],
                  })
                }
              ]
            }
          ]}
        >
          <Text style={styles.sparkleText}>⭐</Text>
        </Animated.View>
        
        <Animated.View 
          style={[
            styles.sparkle,
            styles.sparkle3,
            { 
              opacity: sparkleOpacity,
              transform: [
                { 
                  translateY: floatingAnim.interpolate({
                    inputRange: [0, 1],
                    outputRange: [0, -8],
                  })
                }
              ]
            }
          ]}
        >
          <Text style={styles.sparkleText}>🌟</Text>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    flexGrow: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
    minHeight: Dimensions.get('window').height - 120,
  },
  celebrationContainer: {
    marginBottom: 30,
  },
  celebration: {
    fontSize: 80,
    textAlign: 'center',
  },
  title: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 30,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 50,
    lineHeight: 28,
    opacity: 0.95,
  },
  button: {
    marginBottom: 20,
    shadowColor: '#228B22',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
  },
  buttonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 50,
    borderRadius: 30,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  buttonSubtext: {
    fontSize: 14,
    color: '#fff',
    marginTop: 5,
    opacity: 0.8,
  },
  backButton: {
    padding: 15,
    marginBottom: 20,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.8,
  },
  achievementSection: {
    marginTop: 40,
    width: SCREEN_WIDTH * 0.9,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  achievementTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  achievementCard: {
    width: '100%',
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  achievementEmoji: {
    fontSize: 30,
    marginRight: 15,
  },
  achievementDetails: {
    flex: 1,
  },
  achievementText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 5,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  achievementSubtext: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.8,
  },
  nextStepsSection: {
    marginTop: 40,
    width: SCREEN_WIDTH * 0.9,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  nextStepsTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  nextStepsList: {
    width: '100%',
  },
  nextStepItem: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 10,
    textAlign: 'left',
    paddingLeft: 10,
    opacity: 0.9,
  },
  sparkle: {
    position: 'absolute',
    opacity: 0.8,
    pointerEvents: 'none', // Don't block touches
  },
  sparkle1: {
    top: SCREEN_WIDTH * 0.2,
    left: SCREEN_WIDTH * 0.1,
  },
  sparkle2: {
    bottom: SCREEN_WIDTH * 0.1,
    right: SCREEN_WIDTH * 0.1,
  },
  sparkle3: {
    top: SCREEN_WIDTH * 0.4,
    right: SCREEN_WIDTH * 0.2,
  },
  sparkleText: {
    fontSize: 30,
  },
  buttonContainer: {
    marginTop: 40,
    width: '100%',
    alignItems: 'center',
  },
});

export default SummaryScreen; 