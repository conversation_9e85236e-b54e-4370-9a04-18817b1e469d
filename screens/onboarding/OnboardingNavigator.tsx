import React, { useState, useRef, useEffect, Suspense } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  PanResponder,
  Animated,
  Platform,
  ActivityIndicator,
} from 'react-native';
// Removing LinearGradient to fix useInsertionEffect error
// import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';

// Audio hook component to defer loading
const AudioHookComponent: React.FC<{ onAudioReady: (audio: any) => void }> = ({ onAudioReady }) => {
  const { useAudio } = require('../../contexts/AudioContext');
  const audio = useAudio();

  useEffect(() => {
    onAudioReady(audio);
  }, [audio, onAudioReady]);

  return null;
};

// Onboarding Screens - lazy load to prevent LinearGradient issues
import SplashScreen from './SplashScreen';
const FamilySetupScreen = React.lazy(() => import('./FamilySetupScreen'));
const ChildCreationScreen = React.lazy(() => import('./ChildCreationScreen'));
const SummaryScreen = React.lazy(() => import('./SummaryScreen'));

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export interface OnboardingData {
  familySettings: {
    parentName: string;
    familyName: string;
    currency: string;
    timezone: string;
    taskVerificationMode: 'strict' | 'trusting'; // Add verification mode
  };
  children: Array<{
    name: string;
    age?: number;
    avatar: string;
    startingCoins: number;
    level: number;
  }>;
  preferences: {
    notifications: boolean;
    haptics: boolean;
    sounds: boolean;
  };
  // Add tutorial tracking
  tutorial: {
    completed: boolean;
    currentStep: number;
    needsTaskCreation: boolean;
    needsRewardCreation: boolean;
  };
  // Add monetization context
  subscription: {
    hasUnlimitedChildren: boolean;
    childLimit: number; // Free tier limit (default: 1)
    canAddMoreChildren: boolean;
  };
}

const ONBOARDING_STEPS = [
  { component: SplashScreen, name: 'splash' },
  { component: FamilySetupScreen, name: 'family' },
  { component: ChildCreationScreen, name: 'children' },
  { component: SummaryScreen, name: 'summary' },
];

interface OnboardingNavigatorProps {
  onComplete: (data: OnboardingData) => void;
}

const OnboardingNavigator: React.FC<OnboardingNavigatorProps> = ({ onComplete }) => {
  // Defer audio hook to prevent useInsertionEffect warnings
  const [audioReady, setAudioReady] = useState(false);
  const audioRef = useRef<any>(null);

  // Initialize audio after component mounts
  useEffect(() => {
    setTimeout(() => {
      setAudioReady(true);
    }, 0);
  }, []);

  const handleAudioReady = (audio: any) => {
    audioRef.current = audio;
    // Start background music when audio is ready
    if (audio.playBackgroundMusic) {
      audio.playBackgroundMusic('onboarding');
    }
  };
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    familySettings: {
      parentName: '',
      familyName: '',
      currency: 'coins',
      timezone: 'America/Los_Angeles',
      taskVerificationMode: 'strict',
    },
    children: [],
    preferences: {
      notifications: true,
      haptics: true,
      sounds: true,
    },
    tutorial: {
      completed: false,
      currentStep: 0,
      needsTaskCreation: false,
      needsRewardCreation: false,
    },
    subscription: {
      hasUnlimitedChildren: false,
      childLimit: 1,
      canAddMoreChildren: true,
    },
  });

  const slideAnim = useRef(new Animated.Value(0)).current;

  // Start background music when onboarding begins
  useEffect(() => {
    playBackgroundMusic('onboarding');
  }, []);

  // Nintendo-style haptic feedback (legacy - components now use audio system)
  const triggerHaptic = (type: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!onboardingData.preferences.haptics) return;
    
    if (Platform.OS === 'ios') {
      switch (type) {
        case 'light':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
      }
    }
  };

  const nextStep = (stepData?: Partial<OnboardingData>) => {
    if (stepData) {
      setOnboardingData(prev => ({ ...prev, ...stepData }));
    }

    if (currentStep < ONBOARDING_STEPS.length - 1) {
      triggerHaptic('medium');
      
      // Nintendo-style slide transition
      Animated.timing(slideAnim, {
        toValue: -(currentStep + 1) * SCREEN_WIDTH,
        duration: 400,
        useNativeDriver: true,
      }).start(() => {
        setCurrentStep(currentStep + 1);
      });
    } else {
      // Complete onboarding
      triggerHaptic('heavy');
      onComplete(onboardingData);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      triggerHaptic('light');
      
      Animated.timing(slideAnim, {
        toValue: -(currentStep - 1) * SCREEN_WIDTH,
        duration: 400,
        useNativeDriver: true,
      }).start(() => {
        setCurrentStep(currentStep - 1);
      });
    }
  };



  return (
    <View style={[styles.container, { backgroundColor: '#667eea' }]}>
      {/* Deferred Audio Hook */}
      {audioReady && <AudioHookComponent onAudioReady={handleAudioReady} />}

      <Animated.View
        style={[
          styles.slidingContainer,
          {
            transform: [{ translateX: slideAnim }],
          },
        ]}
      >
        {ONBOARDING_STEPS.map((step, index) => {
          const StepComponent = step.component;
          return (
            <View key={step.name} style={styles.stepContainer}>
              {step.name === 'splash' ? (
                // SplashScreen is not lazy-loaded
                <StepComponent
                  onNext={nextStep}
                  onPrev={prevStep}
                  data={onboardingData}
                  isActive={index === currentStep}
                  triggerHaptic={triggerHaptic}
                />
              ) : (
                // Other screens are lazy-loaded
                <Suspense fallback={<View style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}><ActivityIndicator size="large" color="#fff" /></View>}>
                  <StepComponent
                    onNext={nextStep}
                    onPrev={prevStep}
                    data={onboardingData}
                    isActive={index === currentStep}
                    triggerHaptic={triggerHaptic}
                  />
                </Suspense>
              )}
            </View>
          );
        })}
      </Animated.View>

      {/* Progress Indicator */}
      <View style={styles.progressContainer}>
        {ONBOARDING_STEPS.map((_, index) => (
          <View
            key={index}
            style={[
              styles.progressDot,
              index <= currentStep && styles.progressDotActive,
            ]}
          />
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  slidingContainer: {
    flex: 1,
    flexDirection: 'row',
    width: SCREEN_WIDTH * ONBOARDING_STEPS.length,
  },
  stepContainer: {
    width: SCREEN_WIDTH,
    flex: 1,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingBottom: 40,
    paddingTop: 20,
  },
  progressDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    marginHorizontal: 6,
  },
  progressDotActive: {
    backgroundColor: '#fff',
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 8,
  },
});

export default OnboardingNavigator; 