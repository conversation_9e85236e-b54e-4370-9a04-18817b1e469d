import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  ScrollView, 
  TextInput,
  Alert,
  Animated
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { OnboardingData } from './OnboardingNavigator';
import { useAuth } from '../../contexts/AuthContext';
import { useAudio } from '../../contexts/AudioContext';

interface FamilySetupScreenProps {
  onNext: (data?: Partial<OnboardingData>) => void;
  onPrev: () => void;
  data: OnboardingData;
  isActive: boolean;
  triggerHaptic: (type: 'light' | 'medium' | 'heavy') => void;
}

const FamilySetupScreen: React.FC<FamilySetupScreenProps> = ({ 
  onNext, 
  onPrev, 
  data, 
  isActive, 
  triggerHaptic 
}) => {
  const { session, user, userProfile } = useAuth();
  const { playSound } = useAudio();
  const [verificationMode, setVerificationMode] = useState<'strict' | 'trusting'>('strict');
  const [parentName, setParentName] = useState(userProfile?.full_name || '');
  const [familyName, setFamilyName] = useState('');
  const [parentPin, setParentPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [monetizationInfo, setMonetizationInfo] = useState({
    childLimit: 1,
    canAddChild: true,
    monetizationEnabled: true
  });
  const [loading, setLoading] = useState(false);

  // Animation refs
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const fadeInAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isActive) {
      // Animate in when screen becomes active
      Animated.parallel([
        Animated.timing(slideInAnim, {
          toValue: 0,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start();

      // Fetch monetization limits from our API
      fetchMonetizationLimits();
    }
  }, [isActive]);

  const fetchMonetizationLimits = async () => {
    try {
      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/monetization/limits`, {
        headers: {
          'Authorization': `Bearer ${session?.access_token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const limits = await response.json();
        setMonetizationInfo({
          childLimit: limits.child_limit || 1,
          canAddChild: limits.can_add_child || true,
          monetizationEnabled: limits.monetization_enabled || false
        });
      }
    } catch (error) {
      console.error('Error fetching monetization limits:', error);
      // Continue with defaults
    }
  };

  const handleVerificationToggle = (mode: 'strict' | 'trusting') => {
    playSound('selection_change'); // Play sound for radio button selection
    triggerHaptic('light');
    setVerificationMode(mode);
  };

  const handleNext = async () => {
    if (!parentName.trim()) {
      triggerHaptic('heavy');
      Alert.alert('Missing Information', 'Please enter your name to continue.');
      return;
    }

    if (verificationMode === 'strict') {
      if (!parentPin.trim()) {
        triggerHaptic('heavy');
        Alert.alert('Missing Information', 'Please set a parent PIN for strict mode.');
        return;
      }
      
      if (parentPin.length < 4) {
        triggerHaptic('heavy');
        Alert.alert('Invalid PIN', 'PIN must be at least 4 characters long.');
        return;
      }
      
      if (parentPin !== confirmPin) {
        triggerHaptic('heavy');
        Alert.alert('PIN Mismatch', 'PIN and confirmation do not match.');
        return;
      }
    }

    setLoading(true);
    triggerHaptic('medium');

    try {
      // Save family settings to database
      const familySettingsData: any = {
        task_verification_mode: verificationMode,
      };
      
      // Add parent PIN if strict mode
      if (verificationMode === 'strict') {
        familySettingsData.parent_pin = parentPin;
      }

      const response = await fetch(`${process.env.EXPO_PUBLIC_SUPABASE_URL}/functions/v1/family-settings`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${session?.access_token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(familySettingsData),
      });

      if (!response.ok) {
        throw new Error('Failed to save family settings');
      }

      onNext({
        familySettings: {
          parentName: parentName.trim(),
          familyName: familyName.trim() || `${parentName.trim()}'s Family`,
          currency: 'coins',
          timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
          taskVerificationMode: verificationMode,
        },
        subscription: {
          hasUnlimitedChildren: !monetizationInfo.monetizationEnabled || monetizationInfo.childLimit > 10,
          childLimit: monetizationInfo.childLimit,
          canAddMoreChildren: monetizationInfo.canAddChild,
        },
      });
    } catch (error) {
      console.error('Error saving family settings:', error);
      triggerHaptic('heavy');
      Alert.alert('Error', 'Unable to save settings. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (!isActive) return null;

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Animated.View 
        style={[
          styles.animatedContainer,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Family Settings</Text>
          <Text style={styles.subtitle}>
            Let's customize KidsCoin for your family! 👨‍👩‍👧‍👦
          </Text>
        </View>

        {/* Parent Information */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>About You</Text>
          
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Your Name</Text>
            <TextInput
              style={styles.textInput}
              value={parentName}
              onChangeText={setParentName}
              placeholder="Enter your name..."
              placeholderTextColor="#A0A0A0"
              maxLength={30}
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Family Name (Optional)</Text>
            <TextInput
              style={styles.textInput}
              value={familyName}
              onChangeText={setFamilyName}
              placeholder="The Smith Family"
              placeholderTextColor="#A0A0A0"
              maxLength={30}
            />
          </View>
        </View>

        {/* Task Verification Mode Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>How should tasks be completed?</Text>
          <Text style={styles.sectionSubtitle}>
            You can change this later in settings
          </Text>

          <View style={styles.optionContainer}>
            <TouchableOpacity
              style={[
                styles.optionButton,
                verificationMode === 'strict' && styles.optionButtonSelected
              ]}
              onPress={() => handleVerificationToggle('strict')}
            >
              <View style={styles.optionHeader}>
                <Text style={styles.optionTitle}>🔒 Strict Mode</Text>
                <Text style={styles.optionBadge}>RECOMMENDED</Text>
              </View>
              <Text style={styles.optionDescription}>
                Parents approve each task completion. Great for younger children or building habits.
              </Text>
              <View style={styles.optionFeatures}>
                <Text style={styles.featureText}>✓ PIN-based approval</Text>
                <Text style={styles.featureText}>✓ Real-time notifications</Text>
                <Text style={styles.featureText}>✓ Build trust gradually</Text>
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.optionButton,
                verificationMode === 'trusting' && styles.optionButtonSelected
              ]}
              onPress={() => handleVerificationToggle('trusting')}
            >
              <View style={styles.optionHeader}>
                <Text style={styles.optionTitle}>✅ Trusting Mode</Text>
              </View>
              <Text style={styles.optionDescription}>
                Children can mark tasks complete themselves. Perfect for responsible kids.
              </Text>
              <View style={styles.optionFeatures}>
                <Text style={styles.featureText}>✓ Instant completion</Text>
                <Text style={styles.featureText}>✓ Builds responsibility</Text>
                <Text style={styles.featureText}>✓ Less parent involvement</Text>
              </View>
            </TouchableOpacity>
          </View>
          
          {/* PIN Setup for Strict Mode */}
          {verificationMode === 'strict' && (
            <View style={styles.pinSection}>
              <Text style={styles.pinTitle}>🔐 Set Parent PIN</Text>
              <Text style={styles.pinDescription}>
                This PIN will be required to approve task completions and unlock kiosk mode.
              </Text>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Parent PIN (4+ characters)</Text>
                <TextInput
                  style={styles.textInput}
                  value={parentPin}
                  onChangeText={setParentPin}
                  placeholder="Enter PIN..."
                  placeholderTextColor="#A0A0A0"
                  secureTextEntry
                  maxLength={20}
                />
              </View>
              
              <View style={styles.inputContainer}>
                <Text style={styles.inputLabel}>Confirm PIN</Text>
                <TextInput
                  style={styles.textInput}
                  value={confirmPin}
                  onChangeText={setConfirmPin}
                  placeholder="Confirm PIN..."
                  placeholderTextColor="#A0A0A0"
                  secureTextEntry
                  maxLength={20}
                />
              </View>
              
              <View style={styles.pinTips}>
                <Text style={styles.tipText}>💡 Tips:</Text>
                <Text style={styles.tipText}>• Use something memorable but not obvious</Text>
                <Text style={styles.tipText}>• Avoid birthdays or simple sequences</Text>
                <Text style={styles.tipText}>• You can change this later in settings</Text>
              </View>
            </View>
          )}
        </View>

        {/* Monetization Information */}
        {monetizationInfo.monetizationEnabled && (
          <View style={styles.infoSection}>
            <Text style={styles.infoTitle}>🎯 Getting Started</Text>
            <Text style={styles.infoText}>
              • Start with <Text style={styles.highlight}>{monetizationInfo.childLimit} child profile{monetizationInfo.childLimit > 1 ? 's' : ''}</Text> to explore KidsCoin
            </Text>
            <Text style={styles.infoText}>
              • All core features included from day one!
            </Text>
            <Text style={styles.infoText}>
              • Upgrade anytime for larger families
            </Text>
          </View>
        )}

        <TouchableOpacity 
          style={[styles.nextButton, loading && styles.nextButtonDisabled]} 
          onPress={handleNext}
          disabled={loading}
        >
          <LinearGradient
            colors={loading ? ['#A0A0A0', '#808080'] : ['#32CD32', '#228B22']}
            style={styles.nextButtonGradient}
          >
            <Text style={styles.nextButtonText}>
              {loading ? 'Saving...' : 'Continue Setup →'}
            </Text>
          </LinearGradient>
        </TouchableOpacity>

        <TouchableOpacity style={styles.backButton} onPress={onPrev}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
      </Animated.View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 24,
    paddingTop: 60,
  },
  animatedContainer: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    color: '#E8E8E8',
    textAlign: 'center',
    lineHeight: 24,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: '#C8C8C8',
    marginBottom: 20,
  },
  optionContainer: {
    gap: 16,
  },
  optionButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  optionButtonSelected: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: '#4ECDC4',
  },
  optionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  optionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  optionBadge: {
    backgroundColor: '#FF6B9D',
    color: '#FFFFFF',
    fontSize: 10,
    fontWeight: 'bold',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  optionDescription: {
    fontSize: 14,
    color: '#E0E0E0',
    lineHeight: 20,
  },
  optionFeatures: {
    marginTop: 12,
    paddingLeft: 10,
  },
  featureText: {
    fontSize: 12,
    color: '#C8C8C8',
    marginBottom: 4,
  },
  infoSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: '#E0E0E0',
    lineHeight: 20,
    marginBottom: 4,
  },
  highlight: {
    fontWeight: '600',
    color: '#4ECDC4',
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    color: '#FFFFFF',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: '#FFFFFF',
    borderWidth: 1,
    borderColor: 'transparent',
  },
  nextButton: {
    backgroundColor: '#4ECDC4',
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  nextButtonDisabled: {
    backgroundColor: '#A0A0A0',
    opacity: 0.7,
  },
  nextButtonGradient: {
    borderRadius: 16,
    paddingVertical: 16,
    paddingHorizontal: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextButtonText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: '600',
  },
  backButton: {
    marginTop: 20,
    alignSelf: 'center',
  },
  backButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  pinSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginTop: 20,
  },
  pinTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  pinDescription: {
    fontSize: 14,
    color: '#E0E0E0',
    marginBottom: 20,
  },
  pinTips: {
    marginTop: 10,
    paddingLeft: 10,
  },
  tipText: {
    fontSize: 12,
    color: '#C8C8C8',
    marginBottom: 4,
  },
});

export default FamilySetupScreen; 