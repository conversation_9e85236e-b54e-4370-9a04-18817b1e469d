import React, { useState, useRef, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity,
  ScrollView,
  Animated,
  Dimensions,
  TextInput,
  Alert
} from 'react-native';
// Removing LinearGradient to fix useInsertionEffect error
// import { LinearGradient } from 'expo-linear-gradient';
import { OnboardingData } from './OnboardingNavigator';
import { useAuth } from '../../contexts/AuthContext';
import { useAudio } from '../../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoInput, 
  NintendoCard, 
  NintendoProgressBar, 
  SuggestionCard 
} from '../../components/ui';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TutorialScreenProps {
  onNext: (data?: Partial<OnboardingData>) => void;
  onPrev: () => void;
  data: OnboardingData;
  isActive: boolean;
  triggerHaptic: (type: 'light' | 'medium' | 'heavy') => void;
}

const TUTORIAL_STEPS = [
  { 
    title: 'Welcome to KidsCoin!', 
    content: 'Let\'s create your first task and reward to see how everything works together! 🎯',
    type: 'intro'
  },
  { 
    title: 'Create Your First Task', 
    content: 'Tasks are the heart of KidsCoin. Let\'s make one for your child!',
    type: 'task'
  },
  { 
    title: 'Set Up a Reward', 
    content: 'Now let\'s create something exciting for your child to earn!',
    type: 'reward'
  },
  { 
    title: 'You\'re Ready!', 
    content: 'Perfect! Your child can now complete tasks to earn coins and unlock rewards!',
    type: 'completion'
  }
];

const TASK_SUGGESTIONS = [
  { title: 'Make Bed', coins: 5, category: 'Bedroom' },
  { title: 'Brush Teeth', coins: 3, category: 'Hygiene' },
  { title: 'Put Away Toys', coins: 4, category: 'Cleaning' },
  { title: 'Help with Dishes', coins: 6, category: 'Kitchen' },
  { title: 'Feed Pet', coins: 4, category: 'Pet Care' },
];

const REWARD_SUGGESTIONS = [
  { title: 'Extra Screen Time', coins: 20, category: 'Entertainment' },
  { title: 'Choose Dinner', coins: 15, category: 'Food' },
  { title: 'Stay Up 15 Min Late', coins: 25, category: 'Special' },
  { title: 'Pick Family Movie', coins: 18, category: 'Entertainment' },
  { title: 'Small Toy/Treat', coins: 30, category: 'Tangible' },
];

const TutorialScreen: React.FC<TutorialScreenProps> = ({ 
  onNext, 
  onPrev, 
  data, 
  isActive, 
  triggerHaptic 
}) => {
  const { session } = useAuth();
  const { playSound } = useAudio();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  
  // Task creation state
  const [taskTitle, setTaskTitle] = useState('');
  const [taskCoins, setTaskCoins] = useState('5');
  const [taskCategory, setTaskCategory] = useState('');
  
  // Reward creation state
  const [rewardTitle, setRewardTitle] = useState('');
  const [rewardCoins, setRewardCoins] = useState('20');
  const [rewardCategory, setRewardCategory] = useState('');

  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const stepProgress = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (isActive) {
      // Entrance animation
      Animated.parallel([
        Animated.timing(fadeInAnim, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        Animated.timing(slideInAnim, {
          toValue: 0,
          duration: 800,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isActive]);

  useEffect(() => {
    // Animate progress bar
    Animated.timing(stepProgress, {
      toValue: (currentStep + 1) / TUTORIAL_STEPS.length,
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [currentStep]);

  const handleTaskSuggestion = (suggestion: typeof TASK_SUGGESTIONS[0]) => {
    playSound('card_flip'); // Play sound for suggestion selection
    triggerHaptic('light');
    setTaskTitle(suggestion.title);
    setTaskCoins(suggestion.coins.toString());
    setTaskCategory(suggestion.category);
  };

  const handleRewardSuggestion = (suggestion: typeof REWARD_SUGGESTIONS[0]) => {
    playSound('card_flip'); // Play sound for suggestion selection
    triggerHaptic('light');
    setRewardTitle(suggestion.title);
    setRewardCoins(suggestion.coins.toString());
    setRewardCategory(suggestion.category);
  };

  const createTaskTemplate = async () => {
    if (!taskTitle.trim()) {
      Alert.alert('Missing Information', 'Please enter a task title.');
      return false;
    }

    try {
      // Since we don't have a task template endpoint yet, we'll simulate success
      // In the real implementation, this would create a task template
      triggerHaptic('medium');
      return true;
    } catch (error) {
      console.error('Error creating task template:', error);
      Alert.alert('Error', 'Unable to create task. Please try again.');
      return false;
    }
  };

  const createReward = async () => {
    if (!rewardTitle.trim()) {
      Alert.alert('Missing Information', 'Please enter a reward title.');
      return false;
    }

    try {
      // Since we don't have a rewards endpoint yet, we'll simulate success
      // In the real implementation, this would create a reward
      triggerHaptic('medium');
      return true;
    } catch (error) {
      console.error('Error creating reward:', error);
      Alert.alert('Error', 'Unable to create reward. Please try again.');
      return false;
    }
  };

  const handleNext = async () => {
    if (currentStep === 1) {
      // Task creation step
      const success = await createTaskTemplate();
      if (!success) return;
    } else if (currentStep === 2) {
      // Reward creation step
      const success = await createReward();
      if (!success) return;
    }

    if (currentStep < TUTORIAL_STEPS.length - 1) {
      triggerHaptic('light');
      setCurrentStep(currentStep + 1);
    } else {
      // Complete tutorial
      triggerHaptic('heavy');
      onNext({
        // Store the tutorial data for later use - will be handled in App.tsx
      });
    }
  };

  const handlePrev = () => {
    if (currentStep > 0) {
      triggerHaptic('light');
      setCurrentStep(currentStep - 1);
    } else {
      onPrev();
    }
  };

  const renderStepContent = () => {
    const step = TUTORIAL_STEPS[currentStep];

    switch (step.type) {
      case 'intro':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepEmoji}>🎮</Text>
            <Text style={styles.stepDescription}>
              KidsCoin works in 3 simple steps:
            </Text>
            <View style={styles.flowSteps}>
              <View style={styles.flowStep}>
                <Text style={styles.flowEmoji}>📋</Text>
                <Text style={styles.flowText}>Create Tasks</Text>
              </View>
              <Text style={styles.flowArrow}>↓</Text>
              <View style={styles.flowStep}>
                <Text style={styles.flowEmoji}>🪙</Text>
                <Text style={styles.flowText}>Earn Coins</Text>
              </View>
              <Text style={styles.flowArrow}>↓</Text>
              <View style={styles.flowStep}>
                <Text style={styles.flowEmoji}>🎁</Text>
                <Text style={styles.flowText}>Get Rewards</Text>
              </View>
            </View>
          </View>
        );

      case 'task':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepEmoji}>📋</Text>
            <Text style={styles.stepDescription}>
              Choose from our suggestions or create your own:
            </Text>
            
            <View style={styles.suggestionsGrid}>
              {TASK_SUGGESTIONS.map((suggestion, index) => (
                <SuggestionCard
                  key={index}
                  title={suggestion.title}
                  coins={suggestion.coins}
                  category={suggestion.category}
                  selected={taskTitle === suggestion.title}
                  onPress={() => handleTaskSuggestion(suggestion)}
                />
              ))}
            </View>

            <View style={styles.customForm}>
              <NintendoInput
                label="Task Title"
                variant="transparent"
                value={taskTitle}
                onChangeText={setTaskTitle}
                placeholder="Enter task name..."
              />
              
              <View style={styles.formRow}>
                <View style={styles.formHalf}>
                  <NintendoInput
                    label="Coin Value"
                    variant="transparent"
                    value={taskCoins}
                    onChangeText={setTaskCoins}
                    placeholder="5"
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.formHalf}>
                  <NintendoInput
                    label="Category"
                    variant="transparent"
                    value={taskCategory}
                    onChangeText={setTaskCategory}
                    placeholder="Chores"
                  />
                </View>
              </View>
            </View>
          </View>
        );

      case 'reward':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepEmoji}>🎁</Text>
            <Text style={styles.stepDescription}>
              Pick a reward that will motivate your child:
            </Text>
            
            <View style={styles.suggestionsGrid}>
              {REWARD_SUGGESTIONS.map((suggestion, index) => (
                <SuggestionCard
                  key={index}
                  title={suggestion.title}
                  coins={suggestion.coins}
                  category={suggestion.category}
                  selected={rewardTitle === suggestion.title}
                  onPress={() => handleRewardSuggestion(suggestion)}
                />
              ))}
            </View>

            <View style={styles.customForm}>
              <NintendoInput
                label="Reward Title"
                variant="transparent"
                value={rewardTitle}
                onChangeText={setRewardTitle}
                placeholder="Enter reward name..."
              />
              
              <View style={styles.formRow}>
                <View style={styles.formHalf}>
                  <NintendoInput
                    label="Coin Cost"
                    variant="transparent"
                    value={rewardCoins}
                    onChangeText={setRewardCoins}
                    placeholder="20"
                    keyboardType="numeric"
                  />
                </View>
                <View style={styles.formHalf}>
                  <NintendoInput
                    label="Category"
                    variant="transparent"
                    value={rewardCategory}
                    onChangeText={setRewardCategory}
                    placeholder="Fun"
                  />
                </View>
              </View>
            </View>
          </View>
        );

      case 'completion':
        return (
          <View style={styles.stepContent}>
            <Text style={styles.stepEmoji}>🎉</Text>
            <Text style={styles.stepDescription}>
              Perfect! You've set up everything {data.children[0]?.name} needs to start earning and learning!
            </Text>
            
            <NintendoCard variant="elevated" withShadow>
              <Text style={styles.summaryTitle}>Your Setup:</Text>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryEmoji}>📋</Text>
                <Text style={styles.summaryText}>Task: {taskTitle} ({taskCoins} coins)</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryEmoji}>🎁</Text>
                <Text style={styles.summaryText}>Reward: {rewardTitle} ({rewardCoins} coins)</Text>
              </View>
            </NintendoCard>
          </View>
        );

      default:
        return null;
    }
  };

  if (!isActive) return null;

  const currentStepData = TUTORIAL_STEPS[currentStep];

  return (
    <View style={styles.container}>
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#667eea' }]} />
      
      <Animated.View 
        style={[
          styles.content,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        {/* Progress Bar */}
        <NintendoProgressBar
          progress={(currentStep + 1) / TUTORIAL_STEPS.length}
          totalSteps={TUTORIAL_STEPS.length}
          currentStep={currentStep + 1}
          style={styles.progressContainer}
        />

        <ScrollView style={styles.scrollContent} contentContainerStyle={styles.scrollContainer}>
          <Text style={styles.title}>{currentStepData.title}</Text>
          <Text style={styles.subtitle}>{currentStepData.content}</Text>
          
          {renderStepContent()}
        </ScrollView>

        <View style={styles.buttonContainer}>
          <NintendoButton
            title="← Back"
            variant="back"
            onPress={handlePrev}
            size="small"
            fullWidth={false}
          />
          
          <NintendoButton
            title={currentStep === TUTORIAL_STEPS.length - 1 ? 'Complete Setup!' : 'Next →'}
            variant="success"
            onPress={handleNext}
            style={styles.nextButton}
          />
        </View>
      </Animated.View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
  subtitle: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 50,
    lineHeight: 26,
    opacity: 0.9,
  },
  button: {
    marginBottom: 20,
  },
  buttonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 40,
    borderRadius: 25,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
  },
  progressContainer: {
    paddingHorizontal: 20,
  },
  stepContent: {
    alignItems: 'center',
    marginBottom: 40,
  },
  stepEmoji: {
    fontSize: 60,
    marginBottom: 15,
  },
  stepDescription: {
    fontSize: 20,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
  },
  flowSteps: {
    flexDirection: 'column',
    alignItems: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  flowStep: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 20,
    marginVertical: 5,
    minWidth: '80%',
    justifyContent: 'center',
  },
  flowEmoji: {
    fontSize: 30,
    marginRight: 5,
  },
  flowText: {
    fontSize: 18,
    color: '#fff',
  },
  flowArrow: {
    fontSize: 20,
    color: '#fff',
    marginVertical: 8,
    opacity: 0.8,
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    marginBottom: 20,
  },

  customForm: {
    width: '100%',
    marginTop: 20,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  formHalf: {
    flex: 1,
    marginHorizontal: 5,
  },

  summaryTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
    textAlign: 'center',
  },
  summaryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  summaryEmoji: {
    fontSize: 25,
    marginRight: 10,
  },
  summaryText: {
    fontSize: 16,
    color: '#fff',
    flex: 1,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    paddingHorizontal: 20,
    marginTop: 20,
  },
  nextButton: {
    flex: 1,
    marginLeft: 10,
  },
  scrollContent: {
    flex: 1,
    marginBottom: 10,
  },
  scrollContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
  },
});

export default TutorialScreen; 