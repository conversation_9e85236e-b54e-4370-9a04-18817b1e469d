import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Animated,
  TouchableOpacity,
  Dimensions,
  Switch,
} from 'react-native';
// Removing LinearGradient to fix useInsertionEffect error
// import { LinearGradient } from 'expo-linear-gradient';
import { useAudio } from '../../contexts/AudioContext';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface SplashScreenProps {
  onNext: () => void;
  isActive: boolean;
  triggerHaptic: (type: 'light' | 'medium' | 'heavy') => void;
}

const SplashScreen: React.FC<SplashScreenProps> = ({ onNext, isActive, triggerHaptic }) => {
  const { 
    backgroundMusicEnabled, 
    setBackgroundMusicEnabled, 
    playSound,
    playBackgroundMusic
  } = useAudio();
  
  const logoScale = useRef(new Animated.Value(0)).current;
  const logoRotation = useRef(new Animated.Value(0)).current;
  const titleOpacity = useRef(new Animated.Value(0)).current;
  const subtitleOpacity = useRef(new Animated.Value(0)).current;
  const buttonOpacity = useRef(new Animated.Value(0)).current;
  const sparkleRotation = useRef(new Animated.Value(0)).current;
  const audioControlsOpacity = useRef(new Animated.Value(0)).current;

  const [animationComplete, setAnimationComplete] = useState(false);

  useEffect(() => {
    if (isActive) {
      // Nintendo-style entrance animation sequence
      const sequence = Animated.sequence([
        // Logo appears with bounce
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 4,
          useNativeDriver: true,
        }),
        // Title fades in
        Animated.timing(titleOpacity, {
          toValue: 1,
          duration: 600,
          useNativeDriver: true,
        }),
        // Subtitle follows
        Animated.timing(subtitleOpacity, {
          toValue: 1,
          duration: 500,
          useNativeDriver: true,
        }),
        // Button appears
        Animated.spring(buttonOpacity, {
          toValue: 1,
          tension: 80,
          friction: 6,
          useNativeDriver: true,
        }),
        // Audio controls fade in last
        Animated.timing(audioControlsOpacity, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ]);

      // Continuous sparkle rotation
      const sparkleAnimation = Animated.loop(
        Animated.timing(sparkleRotation, {
          toValue: 1,
          duration: 3000,
          useNativeDriver: true,
        })
      );

      sequence.start(() => {
        setAnimationComplete(true);
        triggerHaptic('medium');
      });

      sparkleAnimation.start();

      return () => {
        sparkleAnimation.stop();
      };
    }
  }, [isActive]);

  const handleStart = () => {
    // playSound('button_success'); // Play success sound for adventure start
    triggerHaptic('heavy');
    onNext();
  };

  const handleMusicToggle = () => {
    // Defer the state update using setTimeout
    setTimeout(() => {
      setBackgroundMusicEnabled(!backgroundMusicEnabled);
    }, 0);

    // No button sound for music toggle - just haptic feedback
    triggerHaptic('light');
  };

  const sparkleRotationValue = sparkleRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '360deg'],
  });

  return (
    <View style={styles.container}>
      {/* Magical Background */}
      <View style={[StyleSheet.absoluteFill, { backgroundColor: '#667eea' }]} />

      {/* Floating Sparkles - Orbiting around K circle */}
      <Animated.View
        style={[
          styles.sparkleContainer,
          {
            transform: [{ rotate: sparkleRotationValue }],
          },
        ]}
      >
        {[...Array(8)].map((_, index) => {
          const angle = (index * 45) * (Math.PI / 180); // Convert to radians
          const radius = 120; // Distance from center
          const x = Math.cos(angle) * radius;
          const y = Math.sin(angle) * radius;
          
          return (
            <View
              key={index}
              style={[
                styles.sparkle,
                {
                  transform: [
                    { translateX: x },
                    { translateY: y },
                  ],
                },
              ]}
            >
              <Text style={styles.sparkleText}>✨</Text>
            </View>
          );
        })}
      </Animated.View>

      {/* Music Toggle - Top Right Corner */}
      <Animated.View style={[styles.audioControls, { opacity: audioControlsOpacity }]}>
        <TouchableOpacity style={styles.musicButton} onPress={handleMusicToggle}>
          <Text style={styles.musicIcon}>
                            {'🎵'}
          </Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <View style={styles.content}>
        {/* Animated Logo */}
        <Animated.View
          style={[
            styles.logoContainer,
            {
              transform: [{ scale: logoScale }],
            },
          ]}
        >
                        <View style={[styles.coinLogo, { backgroundColor: '#FFD700' }]}>
                <Text style={styles.coinText}>K</Text>
              </View>
        </Animated.View>

        {/* Title */}
        <Animated.View style={{ opacity: titleOpacity }}>
          <Text style={styles.title}>KidsCoin</Text>
        </Animated.View>

        {/* Subtitle */}
        <Animated.View style={{ opacity: subtitleOpacity }}>
          <Text style={styles.subtitle}>
            Where chores become adventures and{'\n'}
            kids earn their way to awesome rewards! 🎮
          </Text>
        </Animated.View>

        {/* Start Button */}
        <Animated.View style={[styles.buttonContainer, { opacity: buttonOpacity }]}>
          <TouchableOpacity
            style={styles.startButton}
            onPress={handleStart}
            activeOpacity={0.8}
            disabled={!animationComplete}
          >
                            <View style={[styles.buttonGradient, { backgroundColor: '#FF6B9D' }]}>
                  <Text style={styles.buttonText}>Start Your Adventure! ✨</Text>
                </View>
          </TouchableOpacity>
        </Animated.View>
      </View>

      {/* Bottom Decoration */}
      <View style={styles.bottomDecoration}>
        <Text style={styles.decorationText}>🏆 💰 🎯 🎮 ⭐</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sparkleContainer: {
    position: 'absolute',
    width: 240,
    height: 240,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sparkle: {
    position: 'absolute',
  },
  sparkleText: {
    fontSize: 20,
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 10,
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    marginBottom: 30,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 20,
  },
  coinLogo: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: '#fff',
  },
  coinText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    marginBottom: 20,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 8,
    letterSpacing: 2,
  },
  subtitle: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 50,
    lineHeight: 26,
    opacity: 0.9,
    paddingHorizontal: 20,
  },
  buttonContainer: {
    width: '100%',
    maxWidth: 300,
  },
  startButton: {
    shadowColor: '#C44569',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
  },
  buttonGradient: {
    paddingVertical: 18,
    paddingHorizontal: 40,
    borderRadius: 30,
    alignItems: 'center',
  },
  buttonText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  bottomDecoration: {
    position: 'absolute',
    bottom: 100,
    width: '100%',
    alignItems: 'center',
  },
  decorationText: {
    fontSize: 32,
    letterSpacing: 15,
    opacity: 0.7,
  },
  audioControls: {
    position: 'absolute',
    top: 60,
    right: 20,
  },
  musicButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 25,
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  musicIcon: {
    fontSize: 24,
    textShadowColor: 'rgba(255, 255, 255, 0.8)',
    textShadowOffset: { width: 0, height: 0 },
    textShadowRadius: 5,
  },
});

export default SplashScreen; 