import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  NintendoProgressBar 
} from '../components/ui';
import { supabase } from '../utils/supabase';
// Removed old API imports - using direct Supabase calls instead
import { useChildren } from '../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChildSettingsScreenProps {
  onBack: () => void;
  childId: string;
  onSettingsUpdated?: (childId: string) => void;
}

interface ChildProfile {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  last_activity_date: string;
  created_at: string;
}

interface ChildSettings {
  max_daily_coins: number;
  max_reward_cost: number;
  task_verification_required: boolean;
  reward_approval_required: boolean;
  daily_task_limit: number;
  streak_bonus_enabled: boolean;
  achievement_notifications: boolean;
  parent_notifications: boolean;
  hide_expensive_rewards: boolean;
  auto_assign_recurring_tasks: boolean;
  custom_coin_name: string;
  custom_reward_message: string;
}

const ChildSettingsScreen: React.FC<ChildSettingsScreenProps> = ({ 
  onBack, 
  childId, 
  onSettingsUpdated 
}) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // REACT QUERY: Fast direct Supabase calls (3-5x faster!)
  const { children } = useChildren();
  const childProfile = children.find(c => c.id === childId) || null;
  
  // State
  const [settings, setSettings] = useState<ChildSettings>({
    max_daily_coins: 100,
    max_reward_cost: 500,
    task_verification_required: false,
    reward_approval_required: false,
    daily_task_limit: 10,
    streak_bonus_enabled: true,
    achievement_notifications: true,
    parent_notifications: true,
    hide_expensive_rewards: false,
    auto_assign_recurring_tasks: false,
    custom_coin_name: 'Coins',
    custom_reward_message: 'Great job! You earned a reward!',
  });
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;

  // Child profile is now loaded automatically via useChildren hook

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Load child settings (profile now comes from React Query)
  useEffect(() => {
    const loadSettings = async () => {
      try {
        if (!childId) return;
        
        const { data: settingsData, error } = await supabase
          .from('child_settings')
          .select('*')
          .eq('child_id', childId)
          .single();
        
        if (error) throw error;
        setSettings(settingsData);
      } catch (error) {
        console.error('Error loading child settings:', error);
        Alert.alert('Error', 'Failed to load child settings.');
      } finally {
        setLoading(false);
      }
    };

    loadSettings();
  }, [childId]);

  const handleSaveSettings = async () => {
    if (!childProfile || !user) return;

    setSaving(true);
    playSound('button_press');

    try {
      // Validate settings
      if (settings.max_daily_coins < 1 || settings.max_daily_coins > 1000) {
        Alert.alert('Validation Error', 'Max daily coins must be between 1 and 1000.');
        setSaving(false);
        return;
      }

      if (settings.max_reward_cost < 1 || settings.max_reward_cost > 10000) {
        Alert.alert('Validation Error', 'Max reward cost must be between 1 and 10000.');
        setSaving(false);
        return;
      }

      if (settings.daily_task_limit < 1 || settings.daily_task_limit > 50) {
        Alert.alert('Validation Error', 'Daily task limit must be between 1 and 50.');
        setSaving(false);
        return;
      }

      // Save settings using direct Supabase call
      const { error } = await supabase
        .from('child_settings')
        .upsert({
          child_id: childId,
          ...settings
        });
      
      if (error) throw error;

      playSound('achievement_unlock');
      Alert.alert(
        'Settings Saved! 🎉',
        `Settings for ${childProfile.name} have been successfully updated.`,
        [
          {
            text: 'Done',
            onPress: () => {
              onSettingsUpdated?.(childId);
              onBack();
            },
          },
        ]
      );

    } catch (error) {
      console.error('Error saving child settings:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSettingChange = (key: keyof ChildSettings, value: any) => {
    playSound('button_press');
    setSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const handleResetSettings = () => {
    Alert.alert(
      'Reset Settings',
      'Are you sure you want to reset all settings to default values?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            playSound('button_press');
            setSettings({
              max_daily_coins: 100,
              max_reward_cost: 500,
              task_verification_required: false,
              reward_approval_required: false,
              daily_task_limit: 10,
              streak_bonus_enabled: true,
              achievement_notifications: true,
              parent_notifications: true,
              hide_expensive_rewards: false,
              auto_assign_recurring_tasks: false,
              custom_coin_name: 'Coins',
              custom_reward_message: 'Great job! You earned a reward!',
            });
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.loadingContainer}>
          <NintendoProgressBar progress={0.7} />
          <Text style={styles.loadingText}>Loading settings...</Text>
        </View>
      </View>
    );
  }

  if (!childProfile) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Child profile not found</Text>
          <NintendoButton
            title="Go Back"
            onPress={onBack}
            variant="secondary"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Child Settings</Text>
        <TouchableOpacity onPress={handleResetSettings} style={styles.resetButton}>
          <Text style={styles.resetButtonText}>🔄</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Child Info */}
        <Animated.View style={[styles.section, { 
          opacity: fadeInAnim,
          transform: [{ translateY: slideInAnim }]
        }]}>
          <NintendoCard variant="elevated" withShadow style={styles.childInfoCard}>
            <View style={styles.childInfoContent}>
              <Text style={styles.childAvatar}>{childProfile.avatar}</Text>
              <View style={styles.childInfo}>
                <Text style={styles.childName}>{childProfile.name}'s Settings</Text>
                <Text style={styles.childLevel}>Level {childProfile.level} • {childProfile.coin_balance} 🪙</Text>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Limits & Controls */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.settingsCard}>
            <Text style={styles.sectionTitle}>🎯 Limits & Controls</Text>
            
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Max Daily Coins</Text>
              <Text style={styles.settingDescription}>
                Maximum coins this child can earn per day
              </Text>
              <NintendoInput
                value={settings.max_daily_coins.toString()}
                onChangeText={(text) => {
                  const value = parseInt(text) || 0;
                  if (value >= 0 && value <= 1000) {
                    handleSettingChange('max_daily_coins', value);
                  }
                }}
                placeholder="100"
                keyboardType="numeric"
                style={styles.settingInput}
              />
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Max Reward Cost</Text>
              <Text style={styles.settingDescription}>
                Maximum cost of rewards this child can redeem
              </Text>
              <NintendoInput
                value={settings.max_reward_cost.toString()}
                onChangeText={(text) => {
                  const value = parseInt(text) || 0;
                  if (value >= 0 && value <= 10000) {
                    handleSettingChange('max_reward_cost', value);
                  }
                }}
                placeholder="500"
                keyboardType="numeric"
                style={styles.settingInput}
              />
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Daily Task Limit</Text>
              <Text style={styles.settingDescription}>
                Maximum tasks this child can complete per day
              </Text>
              <NintendoInput
                value={settings.daily_task_limit.toString()}
                onChangeText={(text) => {
                  const value = parseInt(text) || 0;
                  if (value >= 0 && value <= 50) {
                    handleSettingChange('daily_task_limit', value);
                  }
                }}
                placeholder="10"
                keyboardType="numeric"
                style={styles.settingInput}
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Verification & Approval */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.settingsCard}>
            <Text style={styles.sectionTitle}>🔐 Verification & Approval</Text>
            
            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Task Verification Required</Text>
                <Text style={styles.switchDescription}>
                  Require parent verification for this child's task completion
                </Text>
              </View>
              <Switch
                value={settings.task_verification_required}
                onValueChange={(value) => handleSettingChange('task_verification_required', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.task_verification_required ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Reward Approval Required</Text>
                <Text style={styles.switchDescription}>
                  Require parent approval before this child can redeem rewards
                </Text>
              </View>
              <Switch
                value={settings.reward_approval_required}
                onValueChange={(value) => handleSettingChange('reward_approval_required', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.reward_approval_required ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Gamification */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.settingsCard}>
            <Text style={styles.sectionTitle}>🎮 Gamification</Text>
            
            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Streak Bonus Enabled</Text>
                <Text style={styles.switchDescription}>
                  Give bonus coins for maintaining daily streaks
                </Text>
              </View>
              <Switch
                value={settings.streak_bonus_enabled}
                onValueChange={(value) => handleSettingChange('streak_bonus_enabled', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.streak_bonus_enabled ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Achievement Notifications</Text>
                <Text style={styles.switchDescription}>
                  Show notifications when this child unlocks achievements
                </Text>
              </View>
              <Switch
                value={settings.achievement_notifications}
                onValueChange={(value) => handleSettingChange('achievement_notifications', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.achievement_notifications ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Hide Expensive Rewards</Text>
                <Text style={styles.switchDescription}>
                  Hide rewards that cost more than this child can afford
                </Text>
              </View>
              <Switch
                value={settings.hide_expensive_rewards}
                onValueChange={(value) => handleSettingChange('hide_expensive_rewards', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.hide_expensive_rewards ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Automation */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.settingsCard}>
            <Text style={styles.sectionTitle}>🤖 Automation</Text>
            
            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Auto-Assign Recurring Tasks</Text>
                <Text style={styles.switchDescription}>
                  Automatically assign daily/weekly tasks to this child
                </Text>
              </View>
              <Switch
                value={settings.auto_assign_recurring_tasks}
                onValueChange={(value) => handleSettingChange('auto_assign_recurring_tasks', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.auto_assign_recurring_tasks ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>

            <View style={styles.switchItem}>
              <View style={styles.switchInfo}>
                <Text style={styles.switchLabel}>Parent Notifications</Text>
                <Text style={styles.switchDescription}>
                  Send notifications to parents about this child's activity
                </Text>
              </View>
              <Switch
                value={settings.parent_notifications}
                onValueChange={(value) => handleSettingChange('parent_notifications', value)}
                trackColor={{ false: '#767577', true: '#667eea' }}
                thumbColor={settings.parent_notifications ? '#f4f3f4' : '#f4f3f4'}
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Customization */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.settingsCard}>
            <Text style={styles.sectionTitle}>🎨 Customization</Text>
            
            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Custom Coin Name</Text>
              <Text style={styles.settingDescription}>
                Custom name for coins (e.g., "Stars", "Points", "Tokens")
              </Text>
              <NintendoInput
                value={settings.custom_coin_name}
                onChangeText={(text) => handleSettingChange('custom_coin_name', text)}
                placeholder="Coins"
                style={styles.settingInput}
                maxLength={20}
              />
            </View>

            <View style={styles.settingItem}>
              <Text style={styles.settingLabel}>Custom Reward Message</Text>
              <Text style={styles.settingDescription}>
                Message shown when this child redeems a reward
              </Text>
              <NintendoInput
                value={settings.custom_reward_message}
                onChangeText={(text) => handleSettingChange('custom_reward_message', text)}
                placeholder="Great job! You earned a reward!"
                style={styles.settingInput}
                maxLength={100}
                multiline
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <View style={styles.actionButtons}>
            <NintendoButton
              title="Cancel"
              onPress={onBack}
              variant="secondary"
              style={styles.actionButton}
              disabled={saving}
            />
            <NintendoButton
              title={saving ? 'Saving...' : 'Save Settings'}
              onPress={handleSaveSettings}
              variant="success"
              style={styles.actionButton}
              disabled={saving}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  resetButton: {
    padding: 10,
  },
  resetButtonText: {
    fontSize: 20,
    color: '#fff',
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  childInfoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  childInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  childAvatar: {
    fontSize: 40,
    marginRight: 15,
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  childLevel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  settingsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  settingItem: {
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20,
  },
  settingInput: {
    marginBottom: 0,
  },
  switchItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  switchInfo: {
    flex: 1,
    marginRight: 15,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  switchDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default ChildSettingsScreen; 