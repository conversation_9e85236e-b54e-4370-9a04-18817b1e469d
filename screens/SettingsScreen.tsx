import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAudio } from '../contexts/AudioContext';
import SplashScreen from './onboarding/SplashScreen';

interface SettingsScreenProps {
  onBack: () => void;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ onBack }) => {
  const { 
    backgroundMusicEnabled, 
    setBackgroundMusicEnabled, 
    soundEffectsEnabled, 
    setSoundEffectsEnabled,
    masterVolume,
    setMasterVolume,
    playSound
  } = useAudio();
  
  const [showSplashScreen, setShowSplashScreen] = useState(false);
  const [isClosingSplash, setIsClosingSplash] = useState(false);

  const handleMusicToggle = (value: boolean) => {
    setBackgroundMusicEnabled(value);
    playSound('button_press');
  };

  const handleSoundToggle = (value: boolean) => {
    setSoundEffectsEnabled(value);
    if (value) {
      playSound('button_press');
    }
  };

  const handleVolumeUp = () => {
    const newVolume = Math.min(1, masterVolume + 0.1);
    setMasterVolume(newVolume);
    playSound('button_press');
  };

  const handleVolumeDown = () => {
    const newVolume = Math.max(0, masterVolume - 0.1);
    setMasterVolume(newVolume);
    playSound('button_press');
  };

  const handleShowSplash = () => {
    setShowSplashScreen(true);
    playSound('button_success');
  };

  const handleSplashClose = () => {
    setIsClosingSplash(true);
    // Give the component time to clean up before unmounting
    setTimeout(() => {
      setShowSplashScreen(false);
      setIsClosingSplash(false);
    }, 100);
  };

  if (showSplashScreen) {
    return (
      <View style={styles.fullScreen}>
        <SplashScreen 
          onNext={handleSplashClose}
          isActive={!isClosingSplash}
          triggerHaptic={() => {}} // Use audio system instead
        />
        
        {/* Close Button for Splash */}
        <TouchableOpacity style={styles.closeButton} onPress={handleSplashClose}>
          <LinearGradient
            colors={['rgba(0,0,0,0.7)', 'rgba(0,0,0,0.5)']}
            style={styles.closeButtonGradient}
          >
            <Text style={styles.closeButtonText}>× Close</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2', '#f093fb']}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.container}
    >
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={onBack}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Settings</Text>
        </View>

        {/* Audio Settings Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🎵 Audio Controls</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>Background Music</Text>
              <Text style={styles.labelSubtext}>Ambient music during app use</Text>
            </View>
            <Switch
              value={backgroundMusicEnabled}
              onValueChange={handleMusicToggle}
              trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#4CAF50' }}
              thumbColor={backgroundMusicEnabled ? '#FFFFFF' : '#E0E0E0'}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>Sound Effects</Text>
              <Text style={styles.labelSubtext}>Button clicks and notifications</Text>
            </View>
            <Switch
              value={soundEffectsEnabled}
              onValueChange={handleSoundToggle}
              trackColor={{ false: 'rgba(255, 255, 255, 0.3)', true: '#4CAF50' }}
              thumbColor={soundEffectsEnabled ? '#FFFFFF' : '#E0E0E0'}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.labelContainer}>
              <Text style={styles.label}>Master Volume</Text>
              <Text style={styles.labelSubtext}>{Math.round(masterVolume * 100)}%</Text>
            </View>
            <View style={styles.volumeControls}>
              <TouchableOpacity style={styles.volumeButton} onPress={handleVolumeDown}>
                <Text style={styles.volumeButtonText}>−</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.volumeButton} onPress={handleVolumeUp}>
                <Text style={styles.volumeButtonText}>+</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>

        {/* Access to Splash Screen */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🌟 Experience</Text>
          
          <TouchableOpacity style={styles.featureButton} onPress={handleShowSplash}>
            <LinearGradient
              colors={['#FF6B9D', '#C44569']}
              style={styles.featureButtonGradient}
            >
              <Text style={styles.featureButtonTitle}>Start Your Adventure</Text>
              <Text style={styles.featureButtonSubtext}>Revisit the magical intro screen</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>

        {/* Audio Testing */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>🔧 Test Audio</Text>
          
          <View style={styles.testButtonRow}>
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => playSound('button_press')}
            >
              <Text style={styles.testButtonText}>🔘 Button</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => playSound('coin_collect')}
            >
              <Text style={styles.testButtonText}>🪙 Coin</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => playSound('achievement_unlock')}
            >
              <Text style={styles.testButtonText}>🏆 Achievement</Text>
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.testButton} 
              onPress={() => playSound('celebration')}
            >
              <Text style={styles.testButtonText}>🎉 Celebration</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Info Section */}
        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>ℹ️ Audio Notes</Text>
          <Text style={styles.infoText}>• Audio plays even in silent mode (like Spotify)</Text>
          <Text style={styles.infoText}>• Background music can be muted while keeping button sounds</Text>
          <Text style={styles.infoText}>• Use the splash screen to easily adjust audio settings</Text>
        </View>
      </ScrollView>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  fullScreen: {
    flex: 1,
  },
  content: {
    padding: 20,
    paddingTop: 60,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 30,
  },
  backButton: {
    padding: 10,
    marginRight: 15,
  },
  backButtonText: {
    fontSize: 18,
    color: '#fff',
    fontWeight: '600',
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 8,
  },
  section: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  settingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.2)',
  },
  labelContainer: {
    flex: 1,
  },
  label: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '500',
  },
  labelSubtext: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    marginTop: 2,
  },
  volumeControls: {
    flexDirection: 'row',
  },
  volumeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 5,
  },
  volumeButtonText: {
    fontSize: 20,
    color: '#fff',
    fontWeight: 'bold',
  },
  featureButton: {
    shadowColor: '#C44569',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  featureButtonGradient: {
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 15,
    alignItems: 'center',
  },
  featureButtonTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#fff',
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 3,
  },
  featureButtonSubtext: {
    fontSize: 14,
    color: '#fff',
    marginTop: 5,
    opacity: 0.9,
  },
  testButtonRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  testButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginBottom: 8,
    minWidth: '48%',
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: '500',
  },
  infoSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 15,
    padding: 15,
    marginTop: 10,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 4,
  },
  closeButton: {
    position: 'absolute',
    top: 60,
    left: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  closeButtonGradient: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: '600',
  },
});

export default SettingsScreen; 