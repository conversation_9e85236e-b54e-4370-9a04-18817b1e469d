import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  NintendoProgressBar 
} from '../components/ui';
// Removed old API imports - using direct Supabase calls instead
import { supabase } from '../utils/supabase';
import { 
  validateTaskTemplate, 
  TaskTemplateData,
  TASK_CATEGORIES,
  TASK_FREQUENCIES 
} from '../utils/taskValidation';
import { useTaskTemplates } from '../hooks/useTaskTemplates';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TaskTemplateScreenProps {
  onBack: () => void;
  onTemplateSelected?: (template: any) => void;
  selectionMode?: boolean; // If true, allows selecting a template to use
}

interface TaskTemplate {
  id: string;
  title: string;
  description?: string;
  value: number;
  category?: string;
  frequency?: string;
  parent_id: string;
  created_at: string;
  updated_at?: string;
}

const TaskTemplateScreen: React.FC<TaskTemplateScreenProps> = ({ 
  onBack, 
  onTemplateSelected,
  selectionMode = false 
}) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // REACT QUERY: Direct Supabase calls for optimal performance
  const { taskTemplates, loading, loadTaskTemplates } = useTaskTemplates();
  
  // State
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<TaskTemplate | null>(null);
  
  // Edit form state
  const [editTitle, setEditTitle] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editCoinValue, setEditCoinValue] = useState('');
  const [editCategory, setEditCategory] = useState('');
  const [editFrequency, setEditFrequency] = useState('Daily');
  const [editLoading, setEditLoading] = useState(false);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const modalScale = useRef(new Animated.Value(0)).current;

  // Load data on mount
  useEffect(() => {
    loadTaskTemplates();
  }, []);

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Task templates are now loaded automatically via useTaskTemplates hook

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadTaskTemplates();
    setRefreshing(false);
    playSound('button_press');
  };

  const handleTemplatePress = (template: TaskTemplate) => {
    playSound('card_flip');
    
    if (selectionMode) {
      // In selection mode, select the template and return
      onTemplateSelected?.(template);
      onBack();
    } else {
      // In management mode, show options
      setSelectedTemplate(template);
    }
  };

  const handleEditTemplate = (template: TaskTemplate) => {
    playSound('button_press');
    setEditingTemplate(template);
    setEditTitle(template.title);
    setEditDescription(template.description || '');
    setEditCoinValue(template.value.toString());
    setEditCategory(template.category || '');
    setEditFrequency(template.frequency || 'Daily');
    setShowEditModal(true);
    
    // Modal animation
    Animated.spring(modalScale, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handleDuplicateTemplate = async (template: TaskTemplate) => {
    playSound('button_press');
    
    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    try {
      const duplicateData: TaskTemplateData = {
        parent_id: user.id,
        title: `${template.title} (Copy)`,
        description: template.description || '',
        value: template.value,
        category: template.category || 'General',
        frequency: template.frequency || 'Daily',
      };

      const validation = validateTaskTemplate(duplicateData);
      if (!validation.isValid) {
        Alert.alert('Validation Error', validation.errors.join('\n'));
        return;
      }

      const { data, error } = await supabase
        .from('task_templates')
        .insert({
          parent_id: user?.id,
          title: duplicateData.title,
          description: duplicateData.description,
          value: duplicateData.value,
          category: duplicateData.category,
          frequency: duplicateData.frequency
        })
        .select()
        .single();
      
      if (error) throw error;
      playSound('achievement_unlock');
      Alert.alert('Template Duplicated! 🎉', `"${duplicateData.title}" has been created.`);
      loadTaskTemplates(); // Refresh the list
    } catch (error) {
      console.error('Error duplicating template:', error);
      Alert.alert('Error', 'Failed to duplicate template.');
    }
  };

  const handleDeleteTemplate = (template: TaskTemplate) => {
    playSound('button_press');
    setSelectedTemplate(template);
    setShowDeleteModal(true);
  };

  const confirmDeleteTemplate = async () => {
    if (!selectedTemplate) return;

    try {
      const { error } = await supabase
        .from('task_templates')
        .delete()
        .eq('id', selectedTemplate.id);
      
      if (error) throw error;
      playSound('button_success');
      Alert.alert('Template Deleted! ✅', `"${selectedTemplate.title}" has been removed.`);
      loadTaskTemplates(); // Refresh the list
    } catch (error) {
      console.error('Error deleting template:', error);
      Alert.alert('Error', 'Failed to delete template.');
    } finally {
      setShowDeleteModal(false);
      setSelectedTemplate(null);
    }
  };

  const handleSaveEdit = async () => {
    if (!editingTemplate || !user) return;

    // Validate edit data
    const editData: TaskTemplateData = {
      parent_id: user.id,
      title: editTitle.trim(),
      description: editDescription.trim(),
      value: parseInt(editCoinValue) || 5,
      category: editCategory || 'General',
      frequency: editFrequency,
    };

    const validation = validateTaskTemplate(editData);
    if (!validation.isValid) {
      Alert.alert('Validation Error', validation.errors.join('\n'));
      return;
    }

    setEditLoading(true);
    playSound('button_press');

    try {
      const { data, error } = await supabase
        .from('task_templates')
        .update({
          title: editData.title,
          description: editData.description,
          value: editData.value,
          category: editData.category,
          frequency: editData.frequency,
          updated_at: new Date().toISOString(),
        })
        .eq('id', editingTemplate.id)
        .select()
        .single();
      
      if (error) throw error;

      playSound('achievement_unlock');
      Alert.alert('Template Updated! 🎉', `"${editData.title}" has been saved.`);
      loadTaskTemplates(); // Refresh the list
      setShowEditModal(false);
    } catch (error) {
      console.error('Error updating template:', error);
      Alert.alert('Error', 'Failed to update template.');
    } finally {
      setEditLoading(false);
    }
  };

  const closeEditModal = () => {
    Animated.timing(modalScale, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      setShowEditModal(false);
      setEditingTemplate(null);
    });
  };

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'HOUSEHOLD': '🏠',
      'RESPONSIBILITY': '📋',
      'LEARNING': '📚',
      'HEALTH': '💪',
      'CREATIVITY': '🎨',
      'SOCIAL': '👥',
      'TECHNOLOGY': '💻',
      'NATURE': '🌱',
      'MUSIC': '🎵',
      'SPORTS': '⚽',
      'COOKING': '👨‍🍳',
      'GENERAL': '📝',
    };
    return icons[category] || '📝';
  };

  const getFrequencyColor = (frequency: string) => {
    const colors: { [key: string]: string } = {
      'Daily': '#FF6B9D',
      'Weekly': '#4ECDC4',
      'Monthly': '#45B7D1',
      'One-time': '#96CEB4',
    };
    return colors[frequency] || '#FF6B9D';
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.loadingContainer}>
          <NintendoProgressBar progress={0.7} />
          <Text style={styles.loadingText}>Loading task templates...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <Animated.View style={[styles.header, { opacity: fadeInAnim, transform: [{ translateY: slideInAnim }] }]}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>
          {selectionMode ? 'Select Task Template' : 'Task Templates'}
        </Text>
        <TouchableOpacity onPress={handleRefresh} style={styles.refreshButton}>
          <Text style={styles.refreshButtonText}>🔄</Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Content */}
      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#fff"
            colors={['#fff']}
          />
        }
      >
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          {taskTemplates.length === 0 ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <Text style={styles.emptyIcon}>📋</Text>
              <Text style={styles.emptyTitle}>No Task Templates Yet</Text>
              <Text style={styles.emptyText}>
                Create your first task template to get started! Templates help you quickly assign common tasks to your children.
              </Text>
              <NintendoButton
                title="Create Template"
                onPress={() => {
                  // Navigate to task creation screen
                  onBack();
                }}
                variant="primary"
                style={styles.createButton}
              />
            </NintendoCard>
          ) : (
            <>
              <Text style={styles.sectionTitle}>
                {selectionMode ? 'Choose a Template' : 'Your Templates'} ({taskTemplates.length})
              </Text>
              
              {taskTemplates.map((template, index) => (
                <Animated.View
                  key={template.id}
                  style={[
                    styles.templateCard,
                    { 
                      opacity: fadeInAnim,
                      transform: [{ translateY: slideInAnim }],
                    }
                  ]}
                >
                  <TouchableOpacity
                    onPress={() => handleTemplatePress(template)}
                    style={styles.templateTouchable}
                  >
                    <NintendoCard variant="default" style={styles.templateCardInner}>
                      <View style={styles.templateHeader}>
                        <View style={styles.templateInfo}>
                          <Text style={styles.templateTitle}>{template.title}</Text>
                          <Text style={styles.templateDescription}>
                            {template.description || 'No description'}
                          </Text>
                        </View>
                        <View style={styles.templateCoins}>
                          <Text style={styles.coinValue}>{template.value}</Text>
                          <Text style={styles.coinLabel}>🪙</Text>
                        </View>
                      </View>
                      
                      <View style={styles.templateMeta}>
                        <View style={styles.templateTags}>
                          <View style={[styles.categoryTag, { backgroundColor: getFrequencyColor(template.frequency) }]}>
                            <Text style={styles.categoryText}>{template.frequency}</Text>
                          </View>
                          <View style={styles.categoryIcon}>
                            <Text style={styles.iconText}>
                              {getCategoryIcon(template.category)}
                            </Text>
                          </View>
                        </View>
                        
                        {!selectionMode && (
                          <View style={styles.templateActions}>
                            <TouchableOpacity
                              onPress={() => handleEditTemplate(template)}
                              style={styles.actionButton}
                            >
                              <Text style={styles.actionButtonText}>✏️</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => handleDuplicateTemplate(template)}
                              style={styles.actionButton}
                            >
                              <Text style={styles.actionButtonText}>📋</Text>
                            </TouchableOpacity>
                            <TouchableOpacity
                              onPress={() => handleDeleteTemplate(template)}
                              style={styles.actionButton}
                            >
                              <Text style={styles.actionButtonText}>🗑️</Text>
                            </TouchableOpacity>
                          </View>
                        )}
                      </View>
                    </NintendoCard>
                  </TouchableOpacity>
                </Animated.View>
              ))}
            </>
          )}
        </Animated.View>
      </ScrollView>

      {/* Edit Template Modal */}
      <Modal
        visible={showEditModal}
        transparent
        animationType="none"
        onRequestClose={closeEditModal}
      >
        <View style={styles.modalOverlay}>
          <Animated.View 
            style={[
              styles.modalContent,
              { transform: [{ scale: modalScale }] }
            ]}
          >
            <NintendoCard variant="elevated" style={styles.modalCard}>
              <Text style={styles.modalTitle}>Edit Task Template</Text>
              
              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Title</Text>
                <NintendoInput
                  value={editTitle}
                  onChangeText={setEditTitle}
                  placeholder="Task title"
                  style={styles.input}
                />
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Description (Optional)</Text>
                <NintendoInput
                  value={editDescription}
                  onChangeText={setEditDescription}
                  placeholder="Describe the task"
                  multiline
                  numberOfLines={3}
                  style={styles.textArea}
                />
              </View>

              <View style={styles.formRow}>
                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Coin Value</Text>
                  <NintendoInput
                    value={editCoinValue}
                    onChangeText={setEditCoinValue}
                    placeholder="5"
                    keyboardType="numeric"
                    style={styles.input}
                  />
                </View>

                <View style={styles.formGroup}>
                  <Text style={styles.formLabel}>Category</Text>
                  <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                    <View style={styles.categoryButtons}>
                      {TASK_CATEGORIES.map((cat) => (
                        <TouchableOpacity
                          key={cat}
                          onPress={() => setEditCategory(cat)}
                          style={[
                            styles.categoryButton,
                            editCategory === cat && styles.categoryButtonSelected
                          ]}
                        >
                          <Text style={[
                            styles.categoryButtonText,
                            editCategory === cat && styles.categoryButtonTextSelected
                          ]}>
                            {cat}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </ScrollView>
                </View>
              </View>

              <View style={styles.formGroup}>
                <Text style={styles.formLabel}>Frequency</Text>
                <View style={styles.frequencyButtons}>
                  {TASK_FREQUENCIES.map((freq) => (
                    <TouchableOpacity
                      key={freq}
                      onPress={() => setEditFrequency(freq)}
                      style={[
                        styles.frequencyButton,
                        editFrequency === freq && styles.frequencyButtonSelected
                      ]}
                    >
                      <Text style={[
                        styles.frequencyButtonText,
                        editFrequency === freq && styles.frequencyButtonTextSelected
                      ]}>
                        {freq}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <View style={styles.modalActions}>
                <NintendoButton
                  title="Cancel"
                  onPress={closeEditModal}
                  variant="secondary"
                  style={styles.modalButton}
                />
                <NintendoButton
                  title="Save Changes"
                  onPress={handleSaveEdit}
                  variant="primary"
                  style={styles.modalButton}
                  loading={editLoading}
                />
              </View>
            </NintendoCard>
          </Animated.View>
        </View>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        transparent
        animationType="fade"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <NintendoCard variant="elevated" style={styles.deleteModalCard}>
            <Text style={styles.deleteModalTitle}>Delete Template?</Text>
            <Text style={styles.deleteModalText}>
              Are you sure you want to delete "{selectedTemplate?.title}"? This action cannot be undone.
            </Text>
            
            <View style={styles.modalActions}>
              <NintendoButton
                title="Cancel"
                onPress={() => setShowDeleteModal(false)}
                variant="secondary"
                style={styles.modalButton}
              />
              <NintendoButton
                title="Delete"
                onPress={confirmDeleteTemplate}
                variant="danger"
                style={styles.modalButton}
              />
            </View>
          </NintendoCard>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#fff',
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  backButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  backButtonText: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
    flex: 1,
  },
  refreshButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  refreshButtonText: {
    fontSize: 20,
    color: '#fff',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  section: {
    marginTop: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  emptyCard: {
    alignItems: 'center',
    padding: 40,
    marginTop: 40,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  createButton: {
    marginTop: 16,
  },
  templateCard: {
    marginBottom: 16,
  },
  templateTouchable: {
    borderRadius: 16,
  },
  templateCardInner: {
    padding: 20,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  templateInfo: {
    flex: 1,
    marginRight: 16,
  },
  templateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  templateDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  templateCoins: {
    alignItems: 'center',
  },
  coinValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFD700',
  },
  coinLabel: {
    fontSize: 16,
    color: '#FFD700',
  },
  templateMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  templateTags: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryTag: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  categoryText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#fff',
  },
  categoryIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    fontSize: 12,
  },
  templateActions: {
    flexDirection: 'row',
  },
  actionButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(0, 0, 0, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  actionButtonText: {
    fontSize: 14,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  modalContent: {
    width: '100%',
    maxWidth: 400,
  },
  modalCard: {
    padding: 24,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 24,
  },
  formGroup: {
    marginBottom: 20,
  },
  formLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  input: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  textArea: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  categoryButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f8f9fa',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  categoryButtonSelected: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  categoryButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  categoryButtonTextSelected: {
    color: '#fff',
  },
  frequencyButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  frequencyButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f8f9fa',
    borderWidth: 2,
    borderColor: 'transparent',
  },
  frequencyButtonSelected: {
    backgroundColor: '#FF6B9D',
    borderColor: '#FF6B9D',
  },
  frequencyButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  frequencyButtonTextSelected: {
    color: '#fff',
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
    gap: 12,
  },
  modalButton: {
    flex: 1,
  },
  deleteModalCard: {
    padding: 24,
    maxWidth: 320,
  },
  deleteModalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 16,
  },
  deleteModalText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
});

export default TaskTemplateScreen; 