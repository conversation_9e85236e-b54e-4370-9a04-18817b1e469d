import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { signInWithGoogle, signInWithApple } from '../utils/supabase';

const AuthScreen: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const handleGoogleSignIn = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      console.log('Button pressed - starting Google sign in...');
      const { data, error } = await signInWithGoogle();
      
      if (error) {
        Alert.alert('Error', `Failed to sign in with Google: ${error.message || 'Please try again.'}`);
        console.error('Google sign-in error:', error);
      } else {
        console.log('Success! User:', data?.user?.email);
        Alert.alert('Success!', `Welcome ${data?.user?.email || 'User'}!`);
      }
    } catch (error) {
      Alert.alert('Error', `An unexpected error occurred: ${error.message || 'Please try again.'}`);
      console.error('Unexpected error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAppleSignIn = async () => {
    if (loading) return;
    
    setLoading(true);
    try {
      const { data, error } = await signInWithApple();
      
      if (error) {
        Alert.alert('Error', 'Failed to sign in with Apple. Please try again.');
        console.error('Apple sign-in error:', error);
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
      console.error('Unexpected error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#FF6B9D', '#4ECDC4']}
        style={styles.gradient}
      >
        <View style={styles.content}>
          {/* Hero Section */}
          <View style={styles.heroSection}>
            <Text style={styles.logo}>🪙</Text>
            <Text style={styles.title}>KidsCoin</Text>
            <Text style={styles.subtitle}>
              Teaching financial responsibility through fun
            </Text>
          </View>

          {/* Value Proposition */}
          <View style={styles.valueProps}>
            <View style={styles.valueProp}>
              <Text style={styles.valueIcon}>✨</Text>
              <Text style={styles.valueText}>Turn chores into coins</Text>
            </View>
            <View style={styles.valueProp}>
              <Text style={styles.valueIcon}>🎯</Text>
              <Text style={styles.valueText}>Set goals and earn rewards</Text>
            </View>
            <View style={styles.valueProp}>
              <Text style={styles.valueIcon}>📈</Text>
              <Text style={styles.valueText}>Track progress together</Text>
            </View>
          </View>

          {/* Login Buttons */}
          <View style={styles.loginSection}>
            <Text style={styles.loginTitle}>Get Started</Text>
            
            {/* Google Sign In */}
            <TouchableOpacity
              style={[styles.loginButton, styles.googleButton]}
              onPress={handleGoogleSignIn}
              disabled={loading}
            >
              <Ionicons name="logo-google" size={24} color="#4285F4" />
              <Text style={styles.googleButtonText}>Continue with Google</Text>
              {loading && <ActivityIndicator size="small" color="#4285F4" />}
            </TouchableOpacity>

            {/* Apple Sign In (iOS only) */}
            {Platform.OS === 'ios' && (
              <TouchableOpacity
                style={[styles.loginButton, styles.appleButton]}
                onPress={handleAppleSignIn}
                disabled={loading}
              >
                <Ionicons name="logo-apple" size={24} color="#FFFFFF" />
                <Text style={styles.appleButtonText}>Continue with Apple</Text>
                {loading && <ActivityIndicator size="small" color="#FFFFFF" />}
              </TouchableOpacity>
            )}

            <Text style={styles.disclaimer}>
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  content: {
    flex: 1,
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 40,
  },
  heroSection: {
    alignItems: 'center',
    marginTop: 60,
  },
  logo: {
    fontSize: 80,
    marginBottom: 16,
  },
  title: {
    fontSize: 42,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.9,
    lineHeight: 24,
  },
  valueProps: {
    alignItems: 'center',
    gap: 24,
  },
  valueProp: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderRadius: 16,
    minWidth: 280,
  },
  valueIcon: {
    fontSize: 24,
    marginRight: 16,
  },
  valueText: {
    fontSize: 16,
    color: '#FFFFFF',
    fontWeight: '600',
  },
  loginSection: {
    alignItems: 'center',
    gap: 16,
  },
  loginTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  loginButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    minWidth: 280,
    gap: 12,
  },
  googleButton: {
    backgroundColor: '#FFFFFF',
  },
  googleButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333333',
  },
  appleButton: {
    backgroundColor: '#000000',
  },
  appleButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  disclaimer: {
    fontSize: 12,
    color: '#FFFFFF',
    textAlign: 'center',
    opacity: 0.7,
    lineHeight: 16,
    marginTop: 8,
    paddingHorizontal: 20,
  },
});

export default AuthScreen; 