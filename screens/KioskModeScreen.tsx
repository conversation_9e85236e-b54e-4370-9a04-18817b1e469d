import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar,
  TaskListSkeleton
} from '../components/ui';
import KioskChildSelector from '../components/KioskChildSelector';
import ParentPinEntry from '../components/tasks/ParentPinEntry';
import { 
  useSmartTaskCompletion, 
  useSmartChildSelection, 
  useSmartModalManagement 
} from '../hooks';
import { 
  useModal, 
  useAppStore,
  useUIStore 
} from '../stores';
// Removed old API imports - using direct Supabase calls instead

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  child_id: string;
  parent_id: string;
  created_at: string;
  completed_at?: string;
  child_name?: string;
  child_avatar?: string;
  is_recurring?: boolean;
}

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
}

interface KioskModeScreenProps {
  onBack: () => void;
}

const KioskModeScreen: React.FC<KioskModeScreenProps> = ({ onBack }) => {
  const { playSound } = useAudio();
  
  // Smart hooks (combine TanStack Query + Zustand)
  const {
    tasks,
    familySettings,
    loadingTasks,
    handleTaskComplete,
    handlePinSuccess,
    handlePinCancel,
    refreshTasks,
    isCompleting,
    currentTaskForVerification,
  } = useSmartTaskCompletion();
  
  const {
    children,
    selectedChild,
    loadingChildren,
    hasChildren,
    hasMultipleChildren,
    selectChild,
    autoSelectFirstChild,
    openChildSelector,
  } = useSmartChildSelection();
  
  const {
    openModal,
    closeModal,
    showError,
  } = useSmartModalManagement();
  
  // Modal states from Zustand
  const childSelectorModal = useModal('childSelector');
  const taskPinModal = useModal('parentPin');
  const parentModeModal = useModal('parentModeAccess');
  
  // UI state from Zustand
  const refreshing = useUIStore((state) => state.refreshing);
  const setRefreshing = useUIStore((state) => state.setRefreshing);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  // Auto-select first child when children are loaded (only when children change)
  useEffect(() => {
    if (!selectedChild && hasChildren && children.length > 0) {
      selectChild(children[0].id);
    }
  }, [children, selectedChild, hasChildren, selectChild]);

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Sparkle animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(sparkleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(sparkleAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshTasks();
    setRefreshing(false);
  };

  const handleChildSelect = (childId: string) => {
    selectChild(childId);
    closeModal('childSelector');
  };



  const handleSwitchChild = () => {
    openChildSelector();
  };



  const handleParentModeAccess = () => {
    if (!familySettings?.parent_pin) {
      showError('Parent PIN not found. Please check your family settings.');
      return;
    }
    
    openModal('parentModeAccess');
  };

  const handleParentPinSuccess = () => {
    closeModal('parentModeAccess');
    onBack(); // Return to parent mode
  };

  if (loadingChildren) {
    return <TaskListSkeleton />;
  }

  if (!hasChildren || !selectedChild) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.noChildContainer}>
          <Text style={styles.noChildTitle}>👶 No Children Found</Text>
          <Text style={styles.noChildText}>
            Please add children to your family to use kid mode.
          </Text>
          <NintendoButton
            title="Go Back"
            onPress={onBack}
            variant="primary"
            style={styles.noChildButton}
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <Text style={styles.headerTitle}>🎮 Kid Mode</Text>
        <TouchableOpacity onPress={handleParentModeAccess} style={styles.parentModeButton}>
          <Text style={styles.parentModeButtonText}>🔒 Parent Mode</Text>
        </TouchableOpacity>
      </Animated.View>

      {/* Main Content */}
      <ScrollView 
        style={styles.scrollView} 
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor="#ffffff"
            colors={['#ffffff']}
          />
        }
      >
        {/* Current Child Info */}
        <Animated.View style={[styles.childSection, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.childCard}>
            <View style={styles.childHeader}>
              <View style={styles.childInfo}>
                <Text style={styles.childAvatar}>{selectedChild.avatar}</Text>
                <View style={styles.childDetails}>
                  <Text style={styles.childName}>{selectedChild.name}</Text>
                  <Text style={styles.childStats}>
                    Level {selectedChild.level} • {selectedChild.coins} 🪙 • 🔥 {selectedChild.streak} day streak
                  </Text>
                </View>
              </View>
              {hasMultipleChildren && (
                <TouchableOpacity onPress={handleSwitchChild} style={styles.switchButton}>
                  <Text style={styles.switchButtonText}>🔄</Text>
                </TouchableOpacity>
              )}
            </View>
          </NintendoCard>
        </Animated.View>



        {/* Tasks Section */}
        <Animated.View style={[styles.tasksSection, { opacity: fadeInAnim }]}>
          <Text style={styles.sectionTitle}>
            📋 Tasks ({tasks.length})
          </Text>
          
          {loadingTasks ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <NintendoProgressBar progress={0.7} />
              <Text style={styles.loadingText}>Loading tasks...</Text>
            </NintendoCard>
          ) : tasks.length === 0 ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <Text style={styles.emptyText}>
                🎉 No pending tasks! Great job!
              </Text>
            </NintendoCard>
          ) : (
            <View style={styles.tasksList}>
              {tasks.map((task) => (
                <NintendoCard 
                  key={task.id} 
                  variant="elevated" 
                  withShadow 
                  style={styles.taskCard}
                >
                  <View style={styles.taskContent}>
                    <View style={styles.taskHeader}>
                      <Text style={styles.taskTitle}>{task.title}</Text>
                      <Text style={styles.taskValue}>{task.value} 🪙</Text>
                    </View>
                    
                    {task.description && (
                      <Text style={styles.taskDescription}>{task.description}</Text>
                    )}
                    
                    <View style={styles.taskFooter}>
                      <Text style={styles.taskStatus}>
                        {task.status === 'pending' ? '📝 To Do' : '⏸️ Awaiting Approval'}
                      </Text>
                      <NintendoButton
                        title={isCompleting(task.id) ? 'Completing...' : '✅ Complete'}
                        onPress={() => handleTaskComplete(task)}
                        variant="success"
                        size="small"
                        disabled={isCompleting(task.id)}
                      />
                    </View>
                  </View>
                </NintendoCard>
              ))}
            </View>
          )}
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Child Selector Modal */}
      <KioskChildSelector
        visible={childSelectorModal.isOpen}
        onClose={() => closeModal('childSelector')}
        onChildSelect={handleChildSelect}
        currentChildId={selectedChild?.id}
        children={children}
      />

      {/* PIN Entry Modal for Task Verification */}
      <ParentPinEntry
        visible={taskPinModal.isOpen}
        onClose={handlePinCancel}
        onSuccess={handlePinSuccess}
        taskTitle={currentTaskForVerification?.title || "Task Completion"}
        childName={selectedChild?.name || ''}
        coinValue={currentTaskForVerification?.value || 0}
        correctPin={familySettings?.parent_pin || '1234'}
      />

      {/* PIN Entry Modal for Parent Mode Access */}
      <ParentPinEntry
        visible={parentModeModal.isOpen}
        onClose={() => closeModal('parentModeAccess')}
        onSuccess={handleParentPinSuccess}
        taskTitle="Parent Mode Access"
        childName=""
        coinValue={0}
        correctPin={familySettings?.parent_pin || '1234'}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    color: '#ffffff',
    fontSize: 18,
    marginTop: 20,
    textAlign: 'center',
  },
  noChildContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  noChildTitle: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 15,
  },
  noChildText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
    marginBottom: 30,
  },
  noChildButton: {
    width: 200,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    zIndex: 1001, // Higher than DevMenu cornerTapArea
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: '600',
  },
  headerTitle: {
    flex: 1,
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginHorizontal: 20,
  },
  parentModeButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  parentModeButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  childSection: {
    marginBottom: 20,
  },
  childCard: {
    padding: 20,
  },
  childHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  childInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  childAvatar: {
    fontSize: 48,
    marginRight: 15,
  },
  childDetails: {
    flex: 1,
  },
  childName: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  childStats: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.8,
  },
  switchButton: {
    padding: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
  },
  switchButtonText: {
    fontSize: 20,
  },
  statusSection: {
    marginBottom: 20,
  },
  statusCard: {
    padding: 20,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  statusTitle: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
  },
  statusIndicator: {
    backgroundColor: '#FFD700',
    borderRadius: 15,
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  statusIndicatorText: {
    fontSize: 16,
  },
  statusText: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.8,
    marginBottom: 15,
  },
  statusActions: {
    alignItems: 'center',
  },
  tasksSection: {
    gap: 15,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  emptyCard: {
    padding: 40,
    alignItems: 'center',
  },
  emptyText: {
    color: '#ffffff',
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.8,
  },
  tasksList: {
    gap: 15,
  },
  taskCard: {
    padding: 20,
  },
  taskContent: {
    gap: 10,
  },
  taskHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    flex: 1,
  },
  taskValue: {
    color: '#FFD700',
    fontSize: 16,
    fontWeight: 'bold',
  },
  taskDescription: {
    color: '#ffffff',
    fontSize: 14,
    opacity: 0.8,
  },
  taskFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskStatus: {
    color: '#ffffff',
    fontSize: 12,
    opacity: 0.7,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default KioskModeScreen; 