import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  NintendoProgressBar 
} from '../components/ui';
import { 
  validateTaskCompletion,
  updateChildProgress,
  TaskCompletionData 
} from '../utils/taskValidation';
import { useFamilySettings, useChildren, useTasks } from '../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TaskCompletionScreenProps {
  onBack: () => void;
  selectedChildId?: string; // Optional: if viewing tasks for a specific child
}

interface AssignedTask {
  id: string;
  title: string;
  description: string;
  value: number;
  status: 'pending' | 'pending_approval' | 'completed';
  child_id: string;
  child_name: string;
  child_avatar: string;
  created_at: string;
  is_recurring: boolean;
}

const TaskCompletionScreen: React.FC<TaskCompletionScreenProps> = ({ onBack, selectedChildId }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // State
  const [selectedChild, setSelectedChild] = useState<string>(selectedChildId || '');
  const [completing, setCompleting] = useState<string | null>(null);
  
  // React Query hooks
  const { familySettings } = useFamilySettings();
  const { children } = useChildren();
  const { tasks: pendingTasks, loading, error, loadTasks, completeTask } = useTasks();
  
  
  
  // Verification Code Modal
  const [showCodeModal, setShowCodeModal] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const celebrationScale = useRef(new Animated.Value(0)).current;

  // Set first child as selected if none specified
  useEffect(() => {
    if (!selectedChild && children && children.length > 0) {
      setSelectedChild(children[0].id);
    }
  }, [children, selectedChild]);

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleChildSelect = (childId: string) => {
    playSound('selection_change');
    setSelectedChild(childId);
  };

  const handleTaskComplete = async (task: AssignedTask) => {
    if (verificationMode === 'strict') {
      // Show verification code modal
      setCurrentTaskId(task.id);
      setShowCodeModal(true);
      playSound('button_press');
    } else {
      // Direct completion in trusting mode
      await completeTask(task.id);
    }
  };

  const handleVerificationSubmit = async () => {
    if (!currentTaskId) return;
    
    // Validate using parent PIN from family settings instead of temporary codes
    if (!familySettings?.parent_pin) {
      Alert.alert('Error', 'Parent PIN not found. Please check family settings.');
      return;
    }
    
    if (verificationCode !== familySettings.parent_pin) {
      Alert.alert('Verification Error', 'Incorrect PIN. Please try again.');
      return;
    }

    const task = pendingTasks?.find(t => t.id === currentTaskId);
    if (task) {
      setShowCodeModal(false);
      setVerificationCode('');
      setCurrentTaskId(null);
      await completeTask(task.id);
    }
  };

  const handleCompleteTask = async (taskId: string, coinValue: number, childId: string) => {
    setCompleting(taskId);
    playSound('button_press');

    try {
      // Validate task completion
      const completionData: TaskCompletionData = {
        task_id: taskId,
        child_id: childId,
        coin_value: coinValue,
      };

      const validation = await validateTaskCompletion(completionData);
      if (!validation.isValid) {
        Alert.alert('Validation Error', validation.errors.join('\n'));
        setCompleting(null);
        return;
      }

      // Complete task using simplified hook
      await completeTask(taskId);

      // Update child XP and level progression
      try {
        await updateChildProgress(childId, coinValue); // XP = coin value
      } catch (error) {
        console.error('Error updating child progress:', error);
        // Don't fail the task completion for XP update errors
      }

      // Celebration animation
      Animated.spring(celebrationScale, {
        toValue: 1,
        tension: 50,
        friction: 4,
        useNativeDriver: true,
      }).start(() => {
        setTimeout(() => {
          Animated.spring(celebrationScale, {
            toValue: 0,
            tension: 80,
            friction: 6,
            useNativeDriver: true,
          }).start();
        }, 2000);
      });

      playSound('coin_collect');
      
      const completedTask = pendingTasks?.find(t => t.id === taskId);
      Alert.alert(
        'Task Completed! 🎉',
        `Great job! You earned ${coinValue} coins for "${completedTask?.title}"!`,
        [{ text: 'Awesome!', onPress: () => {} }]
      );

      // React Query will automatically refetch related data due to cache invalidation
    } catch (error) {
      console.error('Error completing task:', error);
      Alert.alert('Error', 'Failed to complete task. Please try again.');
    } finally {
      setCompleting(null);
    }
  };



  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFill}
        />
        <Text style={styles.loadingText}>Loading Tasks...</Text>
      </View>
    );
  }

  // Show error state if data failed to load
  if (error) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFill}
        />
        <Text style={styles.loadingText}>Error loading data. Please try again.</Text>
        {/* The original code had a refetchTasks() call here, but refetchTasks is not defined in the provided context.
            Assuming it was meant to be loadTasks or a placeholder for a future implementation.
            For now, removing it to avoid errors. */}
        {/* <NintendoButton
          title="Retry"
          onPress={() => refetchTasks()}
          variant="primary"
          style={{ marginTop: 20 }}
        /> */}
      </View>
    );
  }

  const selectedChildData = children?.find(c => c.id === selectedChild);
  const verificationMode = familySettings?.task_verification_mode || 'strict';

  return (
    <View style={styles.container}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Celebration Overlay */}
      <Animated.View
        style={[
          styles.celebrationOverlay,
          {
            transform: [{ scale: celebrationScale }],
            opacity: celebrationScale,
          },
        ]}
      >
        <Text style={styles.celebrationText}>🎉</Text>
        <Text style={styles.celebrationSubtext}>Task Completed!</Text>
      </Animated.View>

      {/* Header */}
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Complete Tasks</Text>
        <View style={styles.headerSpacer} />
      </Animated.View>

      {/* Main Content */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Child Selection */}
        {children && children.length > 1 && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>👶 Select Child</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.childrenScroll}>
              {children.map((child) => (
                <TouchableOpacity
                  key={child.id}
                  onPress={() => handleChildSelect(child.id)}
                  style={styles.childSelectorCard}
                >
                  <NintendoCard 
                    variant={selectedChild === child.id ? "elevated" : "default"}
                    withShadow={selectedChild === child.id}
                    style={selectedChild === child.id
                      ? { ...styles.childSelectorInner, ...styles.childSelectorSelected }
                      : styles.childSelectorInner
                    }
                  >
                    <Text style={styles.childSelectorAvatar}>{child.avatar}</Text>
                    <Text style={styles.childSelectorName}>{child.name}</Text>
                    <Text style={styles.childSelectorCoins}>{child.coin_balance} 🪙</Text>
                  </NintendoCard>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Animated.View>
        )}

        {/* Current Child Info */}
        {selectedChildData && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <NintendoCard variant="elevated" withShadow style={styles.childInfoCard}>
              <View style={styles.childInfoContent}>
                <Text style={styles.childInfoAvatar}>{selectedChildData.avatar}</Text>
                <View style={styles.childInfoText}>
                  <Text style={styles.childInfoName}>{selectedChildData.name}'s Tasks</Text>
                  <Text style={styles.childInfoCoins}>Current Balance: {selectedChildData.coin_balance} 🪙</Text>
                </View>
              </View>
            </NintendoCard>
          </Animated.View>
        )}

        {/* Verification Mode Info */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.infoCard}>
            <Text style={styles.infoTitle}>
              {verificationMode === 'strict' ? '🔒 Strict Mode' : '🤝 Trusting Mode'}
            </Text>
            <Text style={styles.infoText}>
              {verificationMode === 'strict' 
                ? 'Tasks require a verification code from parent before completion.'
                : 'Tasks can be marked as complete independently.'}
            </Text>
          </NintendoCard>
        </Animated.View>

        {/* Assigned Tasks */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <Text style={styles.sectionTitle}>📋 Assigned Tasks</Text>
          
          {!pendingTasks ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <NintendoProgressBar progress={0.7} />
              <Text style={styles.loadingText}>Loading tasks...</Text>
            </NintendoCard>
          ) : pendingTasks.length === 0 ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <Text style={styles.emptyText}>
                🎉 No pending tasks! All caught up!
              </Text>
            </NintendoCard>
          ) : (
            pendingTasks.map((task) => (
              <NintendoCard key={task.id} variant="elevated" withShadow style={styles.taskCard}>
                <View style={styles.taskContent}>
                  <View style={styles.taskInfo}>
                    <Text style={styles.taskTitle}>{task.title}</Text>
                    {task.description && (
                      <Text style={styles.taskDescription}>{task.description}</Text>
                    )}
                    <View style={styles.taskMeta}>
                      <Text style={styles.taskCoins}>{task.value} 🪙</Text>
                      <Text style={styles.taskStatus}>
                        {task.status === 'pending_approval' ? '⏳ Awaiting Approval' : '📝 Ready to Complete'}
                      </Text>
                    </View>
                  </View>
                  
                  <View style={styles.taskActions}>
                    {task.status === 'pending' && (
                      <NintendoButton
                        title={completing === task.id ? 'Completing...' : '✅ Complete'}
                        onPress={() => handleTaskComplete(task as AssignedTask)}
                        variant="success"
                        size="small"
                        disabled={completing === task.id}
                      />
                    )}
                    
                    {verificationMode === 'strict' && task.status === 'pending' && (
                      <Text style={styles.helpText}>
                        💡 Ask your parent for their PIN
                      </Text>
                    )}
                  </View>
                </View>
              </NintendoCard>
            ))
          )}
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Verification Code Modal */}
      <Modal
        visible={showCodeModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowCodeModal(false)}
      >
        <View style={styles.modalOverlay}>
          <NintendoCard variant="elevated" withShadow style={styles.modalCard}>
            <Text style={styles.modalTitle}>🔑 Enter Parent PIN</Text>
            <Text style={styles.modalSubtitle}>
              Ask your parent to enter their PIN to complete this task.
            </Text>
            
            <NintendoInput
              value={verificationCode}
              onChangeText={setVerificationCode}
              placeholder="Enter parent PIN"
              secureTextEntry
              maxLength={20}
              style={styles.codeInput}
            />
            
            <View style={styles.modalActions}>
              <NintendoButton
                title="Cancel"
                onPress={() => {
                  setShowCodeModal(false);
                  setVerificationCode('');
                  setCurrentTaskId(null);
                }}
                variant="secondary"
                style={styles.modalButton}
              />
              <NintendoButton
                title="Verify"
                onPress={handleVerificationSubmit}
                variant="success"
                disabled={verificationCode.length === 0}
                style={styles.modalButton}
              />
            </View>
          </NintendoCard>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  celebrationOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
  },
  celebrationText: {
    fontSize: 80,
    marginBottom: 20,
  },
  celebrationSubtext: {
    fontSize: 24,
    color: '#fff',
    fontWeight: 'bold',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
    zIndex: 1,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  childrenScroll: {
    flexDirection: 'row',
  },
  childSelectorCard: {
    marginRight: 15,
    width: 100,
  },
  childSelectorInner: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 15,
    alignItems: 'center',
    minHeight: 100,
  },
  childSelectorSelected: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  childSelectorAvatar: {
    fontSize: 24,
    marginBottom: 5,
  },
  childSelectorName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  childSelectorCoins: {
    fontSize: 10,
    color: '#666',
  },
  childInfoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
  },
  childInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  childInfoAvatar: {
    fontSize: 40,
    marginRight: 15,
  },
  childInfoText: {
    flex: 1,
  },
  childInfoName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  childInfoCoins: {
    fontSize: 14,
    color: '#666',
  },
  infoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  emptyCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 30,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
  },
  taskCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    marginBottom: 15,
  },
  taskContent: {
    flexDirection: 'row',
    padding: 15,
    alignItems: 'center',
  },
  taskInfo: {
    flex: 1,
    marginRight: 15,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20,
  },
  taskMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  taskCoins: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  taskStatus: {
    fontSize: 12,
    color: '#888',
  },
  taskActions: {
    alignItems: 'center',
  },
  helpText: {
    fontSize: 12,
    color: '#888',
    marginTop: 10,
    textAlign: 'center',
    fontStyle: 'italic',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  modalCard: {
    backgroundColor: '#fff',
    padding: 20,
    width: '100%',
    maxWidth: 300,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  codeInput: {
    marginBottom: 20,
    textAlign: 'center',
    fontSize: 24,
    letterSpacing: 8,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default TaskCompletionScreen; 