import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { useTutorial } from '../contexts/TutorialContext';
import { GuidedTutorialOverlay } from '../components/GuidedTutorialOverlay';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  SuggestionCard 
} from '../components/ui';
// Removed old API imports - using direct Supabase calls instead
import { 
  validateTaskTemplate, 
  TaskTemplateData,
  TASK_CATEGORIES,
  TASK_FREQUENCIES 
} from '../utils/taskValidation';
import { 
  generateAITasks,
  generatePersonalizedTasks,
  isOpenAIConfigured,
  withAIErrorHandling,
  TASK_CATEGORIES as AI_TASK_CATEGORIES,
  AGE_GROUPS
} from '../utils/openaiConfig';
import { 
  getTasksByAgeGroup,
  getTasksByAge,
  getRandomTasks,
  getTaskStats
} from '../utils/taskLibrary';
import { checkFeatureAccess, recordFeatureUsage } from '../utils/unifiedMonetization';
import { useChildren } from '../hooks/useChildren';
import { supabase } from '../utils/supabase';
import { 
  useFormData, 
  useSetFormData,
  useResetForm,
  useLoadingState, 
  useErrorState,
  useModal,
  useOpenModal,
  useCloseModal
} from '../stores';


const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TaskCreationScreenProps {
  onBack: () => void;
  onTaskCreated?: (task: any) => void;
}

interface TaskSuggestion {
  title: string;
  description: string;
  coins: number;
  category: string;
  frequency: string;
  ageRange: string;
}

// Pre-populated task suggestions (future: generated by AI)
const TASK_SUGGESTIONS: TaskSuggestion[] = [
  // Early Childhood (3-6)
  { title: 'Put Away Toys', description: 'Clean up toys after playing', coins: 3, category: 'Cleaning', frequency: 'Daily', ageRange: '3-6' },
  { title: 'Brush Teeth', description: 'Morning and evening teeth brushing', coins: 2, category: 'Hygiene', frequency: 'Daily', ageRange: '3-6' },
  { title: 'Make Bed', description: 'Make your bed in the morning', coins: 4, category: 'Bedroom', frequency: 'Daily', ageRange: '3-6' },
  { title: 'Feed Pet', description: 'Give food and water to pet', coins: 5, category: 'Pet Care', frequency: 'Daily', ageRange: '3-6' },
  
  // Elementary (7-12)
  { title: 'Homework Time', description: 'Complete daily homework assignments', coins: 8, category: 'Education', frequency: 'Daily', ageRange: '7-12' },
  { title: 'Set Dinner Table', description: 'Put out plates, utensils, and napkins', coins: 6, category: 'Kitchen', frequency: 'Daily', ageRange: '7-12' },
  { title: 'Water Plants', description: 'Water indoor and outdoor plants', coins: 4, category: 'Garden', frequency: 'Daily', ageRange: '7-12' },
  { title: 'Empty Dishwasher', description: 'Put clean dishes away', coins: 7, category: 'Kitchen', frequency: 'Daily', ageRange: '7-12' },
  
  // Middle School (13-15)
  { title: 'Do Laundry', description: 'Wash, dry, and fold clothes', coins: 12, category: 'Cleaning', frequency: 'Weekly', ageRange: '13-15' },
  { title: 'Study Session', description: '45-minute focused study time', coins: 10, category: 'Education', frequency: 'Daily', ageRange: '13-15' },
  { title: 'Vacuum Room', description: 'Vacuum bedroom and common areas', coins: 8, category: 'Cleaning', frequency: 'Weekly', ageRange: '13-15' },
  { title: 'Organize Backpack', description: 'Clean and organize school backpack', coins: 5, category: 'Organization', frequency: 'Weekly', ageRange: '13-15' },
  
  // High School (16-18)
  { title: 'Grocery Shopping', description: 'Shop for family groceries with list', coins: 15, category: 'Life Skills', frequency: 'Weekly', ageRange: '16-18' },
  { title: 'Car Maintenance', description: 'Check oil, tire pressure, wash car', coins: 20, category: 'Vehicle', frequency: 'Monthly', ageRange: '16-18' },
  { title: 'Meal Planning', description: 'Plan and prep meals for the week', coins: 18, category: 'Kitchen', frequency: 'Weekly', ageRange: '16-18' },
  { title: 'Budget Review', description: 'Review and update personal budget', coins: 12, category: 'Finance', frequency: 'Weekly', ageRange: '16-18' },
  
  // Young Adult (19-25)
  { title: 'Job Application', description: 'Apply to one job or internship', coins: 25, category: 'Career', frequency: 'Daily', ageRange: '19-25' },
  { title: 'Exercise Routine', description: '30-minute workout or gym session', coins: 15, category: 'Health', frequency: 'Daily', ageRange: '19-25' },
  { title: 'Networking Event', description: 'Attend professional networking event', coins: 30, category: 'Career', frequency: 'Monthly', ageRange: '19-25' },
  { title: 'Skill Development', description: 'Complete online course or tutorial', coins: 20, category: 'Education', frequency: 'Weekly', ageRange: '19-25' },
];

// Constants now imported from taskValidation utility

const TaskCreationScreen: React.FC<TaskCreationScreenProps> = ({ onBack, onTaskCreated }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  // Tutorial management - single call to prevent infinite loops
  const tutorial = useTutorial();
  const { 
    tutorialState, 
    currentStep, 
    nextStep, 
    completeTutorial, 
    markTaskCreated, 
    setCurrentScreen,
    setPersisted
  } = tutorial;
  
  // ZUSTAND: Client state (form data, UI state)
  const formData = useFormData();
  const setFormData = useSetFormData();
  const resetForm = useResetForm();
  const loading = useLoadingState('taskCreation');
  const error = useErrorState('taskCreation');
  const aiGenerating = useLoadingState('aiGeneration');
  const aiModal = useModal('aiGeneration');
  const openModal = useOpenModal();
  const closeModal = useCloseModal();
  
  // TANSTACK QUERY: Server state
  const { children } = useChildren();
  
  // Simple task creation function that also handles assignment
  const createTask = async (taskData: TaskTemplateData) => {
    try {
      // First create the task template
      const { data: template, error: templateError } = await supabase
        .from('task_templates')
        .insert({
          parent_id: user?.id,
          title: taskData.title,
          description: taskData.description,
          value: taskData.value,
          category: taskData.category,
          frequency: taskData.frequency
        })
        .select()
        .single();
      
      if (templateError) throw templateError;
      
      // Then assign to selected children if any
      const selectedChildIds = formData.selectedChildren || [];
      let assignedTasks = [];
      
      if (selectedChildIds.length > 0) {
        const tasksToInsert = selectedChildIds.map(childId => ({
          parent_id: user?.id,
          child_id: childId,
          title: template.title,
          description: template.description,
          value: template.value,
          template_id: template.id,
          status: 'pending'
        }));
        
        const { data: tasks, error: assignError } = await supabase
          .from('tasks')
          .insert(tasksToInsert)
          .select();
          
        if (assignError) throw assignError;
        assignedTasks = tasks;
      }
      
      playSound('achievement_unlock');
      
      const assignmentText = assignedTasks.length > 0 
        ? ` and assigned to ${assignedTasks.length} child${assignedTasks.length > 1 ? 'ren' : ''}`
        : '';
        
      Alert.alert(
        '✅ Task Created!',
        `${template.title} has been created successfully${assignmentText}!`,
        [{ text: 'OK', onPress: () => {
          onTaskCreated?.(template);
          onBack();
        }}]
      );
      
      return template;
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('Error', 'Failed to create task. Please try again.');
      throw error;
    }
  };
  
  // UI state (Local state for component-specific UI)
  const [showSuggestions, setShowSuggestions] = React.useState(true);
  const [showAIGeneration, setShowAIGeneration] = React.useState(false);
  const [selectedSuggestion, setSelectedSuggestion] = React.useState<TaskSuggestion | null>(null);
  
  // AI Generation state (Local state)
  const [aiSuggestions, setAISuggestions] = React.useState<any[]>([]);
  const [selectedChild, setSelectedChild] = React.useState<any>(null);
  // interests and difficulty now come from formData
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;
  
  // Tutorial refs
  const titleInputRef = useRef<any>(null);
  const descriptionInputRef = useRef<any>(null);
  const coinValueInputRef = useRef<any>(null);
  const categoryInputRef = useRef<any>(null);
  const frequencyInputRef = useRef<any>(null);
  const createButtonRef = useRef<any>(null);
  const suggestionCardRef = useRef<any>(null);
  const aiGenerationRef = useRef<any>(null);
  const personalizedGenerationRef = useRef<any>(null);
  const libraryTasksRef = useRef<any>(null);
  
  // Load children on mount
  useEffect(() => {
    if (children.length > 0 && !selectedChild) {
      setSelectedChild(children[0]);
    }
  }, [children, selectedChild]);
  
  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  // Tutorial effect
  useEffect(() => {
    if (tutorialState.currentStepId === 'task_creation_start' && tutorialState.isActive) {
      setCurrentScreen('task_creation');
      setPersisted(true);
    }
  }, [tutorialState.currentStepId, tutorialState.isActive]);
  
  const handleSuggestionPress = (suggestion: TaskSuggestion) => {
    playSound('button_press');
    
    // Track selected suggestion for UI feedback
    setSelectedSuggestion(suggestion);
    
    // ZUSTAND: Update form data
    setFormData('taskTitle', suggestion.title);
    setFormData('taskDescription', suggestion.description);
    setFormData('taskCoinValue', suggestion.coins.toString());
    setFormData('taskCategory', suggestion.category);
    setFormData('taskFrequency', suggestion.frequency);
    
    // Tutorial navigation
    if (tutorialState.currentStepId === 'task_creation_form') {
      nextStep();
    }
  };
  
  const handleCreateTask = async () => {
    if (!user) return;
    
    playSound('button_press');
    
    // Validate form data
    const taskData: TaskTemplateData = {
      title: formData.taskTitle,
      description: formData.taskDescription,
      value: parseInt(formData.taskCoinValue) || 5,
      category: formData.taskCategory,
      frequency: formData.taskFrequency,
      parent_id: user.id,
    };
    
    const validation = validateTaskTemplate(taskData);
    if (!validation.isValid) {
      Alert.alert('Validation Error', validation.errors.join('\n'));
      return;
    }
    
    // Basic task creation is always free - no monetization check needed
    // Only AI features require premium access
    
    try {
      // TANSTACK QUERY: Create task
      await createTask(taskData);
      
      // Basic task creation doesn't need usage tracking
      
      // Mark tutorial progress
      markTaskCreated();
      
      // Reset form
      resetForm();
      
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('Error', 'Failed to create task. Please try again.');
    }
  };

  // AI Generation functions
  const handleAIGeneration = async () => {
    if (!user) return;

    // Check premium access
    const accessCheck = await checkFeatureAccess(user.id, 'ai_task_generation');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        '🤖 AI Task Generation',
        'This premium feature uses AI to create personalized tasks for your children!\n\n' +
        '✨ What you get:\n' +
        '• Age-appropriate task suggestions\n' +
        '• Personalized based on interests\n' +
        '• Educational and fun activities\n' +
        '• Unlimited AI generation\n\n' +
        'Would you like to upgrade to Premium to unlock this feature?',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { 
            text: 'Upgrade Now', 
            onPress: () => {
              // Navigate to subscription screen or show upgrade modal
              Alert.alert('Upgrade', 'Please go to Settings > Subscription to upgrade your plan.');
            }
          }
        ]
      );
      return;
    }

    if (!isOpenAIConfigured()) {
      Alert.alert(
        '🔧 Setup Required',
        'To use AI task generation, you need to add your OpenAI API key.\n\n' +
        '1. Get a free API key from openai.com\n' +
        '2. Go to Settings > AI Configuration\n' +
        '3. Enter your API key\n\n' +
        'Would you like to go to settings now?',
        [
          { text: 'Cancel', style: 'cancel' },
          { 
            text: 'Go to Settings', 
            onPress: () => {
              // Navigate to settings
              Alert.alert('Settings', 'Please go to Settings > AI Configuration to add your API key.');
            }
          }
        ]
      );
      return;
    }

    // setAIGenerating(true); // This state is managed by ZUSTAND
    // setShowAIGeneration(true); // This state is managed by ZUSTAND
    
    try {
      const childAge = selectedChild ? calculateAge(selectedChild.created_at) : 10;
      const interestsList = formData.interests ? formData.interests.split(',').map(i => i.trim()) : [];
      const categories = formData.selectedCategory !== 'all' ? [formData.selectedCategory] : [];
      
      const aiTasks = await withAIErrorHandling(
        () => generateAITasks({
          age: childAge,
          child_name: selectedChild?.name,
          interests: interestsList,
          categories,
          difficulty: formData.difficulty,
          count: 5,
          family_context: `Family with ${children.length} children`,
        }),
        []
      );

      if (aiTasks && aiTasks.length > 0) {
        setAISuggestions(aiTasks);
        playSound('achievement_unlock');
        // Record feature usage
        await recordFeatureUsage(user.id, 'ai_task_generation');
      } else {
        Alert.alert('No Suggestions', 'Unable to generate AI suggestions. Try adjusting your criteria.');
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      Alert.alert('Error', 'Failed to generate AI suggestions. Please try again.');
    } finally {
      // setAIGenerating(false); // This state is managed by ZUSTAND
    }
  };

  const handlePersonalizedGeneration = async () => {
    if (!user) return;

    // Check premium access
    const accessCheck = await checkFeatureAccess(user.id, 'ai_personalized_suggestions');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        '✨ Personalized AI Suggestions',
        'This premium feature learns from your family\'s patterns to suggest the perfect tasks!\n\n' +
        '🎯 What you get:\n' +
        '• Tasks based on your child\'s history\n' +
        '• Learning from completion patterns\n' +
        '• Adaptive difficulty levels\n' +
        '• Family-specific recommendations\n\n' +
        'Would you like to upgrade to Premium to unlock this feature?',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { 
            text: 'Upgrade Now', 
            onPress: () => {
              Alert.alert('Upgrade', 'Please go to Settings > Subscription to upgrade your plan.');
            }
          }
        ]
      );
      return;
    }

    if (!selectedChild) {
      Alert.alert('Select Child', 'Please select a child for personalized suggestions.');
      return;
    }

    // setAIGenerating(true); // This state is managed by ZUSTAND
    // setShowAIGeneration(true); // This state is managed by ZUSTAND
    
    try {
      // Load child's task history (using query client fetch)
      const taskHistory = await supabase
        .from('tasks')
        .select('*')
        .eq('child_id', selectedChild.id)
        .eq('status', 'completed');
      const taskHistoryData = taskHistory.data || [];

      const personalizedTasks = await withAIErrorHandling(
        () => generatePersonalizedTasks(
          {
            name: selectedChild.name,
            age: calculateAge(selectedChild.created_at),
            coin_balance: selectedChild.coin_balance,
          },
          taskHistoryData,
          {
            interests: formData.interests ? formData.interests.split(',').map(i => i.trim()) : [],
            family_context: `Family with ${children.length} children`,
          }
        ),
        []
      );

      if (personalizedTasks && personalizedTasks.length > 0) {
        setAISuggestions(personalizedTasks);
        playSound('achievement_unlock');
        // Record feature usage
        await recordFeatureUsage(user.id, 'ai_personalized_suggestions');
      } else {
        Alert.alert('No Suggestions', 'Unable to generate personalized suggestions.');
      }
    } catch (error) {
      console.error('Personalized Generation Error:', error);
      Alert.alert('Error', 'Failed to generate personalized suggestions.');
    } finally {
      // setAIGenerating(false); // This state is managed by ZUSTAND
    }
  };

  const handleLoadLibraryTasks = async () => {
    if (!user) return;

    // Check premium access for library access
    const accessCheck = await checkFeatureAccess(user.id, 'task_library');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        '📚 Task Library',
        'Access our extensive library of age-appropriate tasks!\n\n' +
        '📖 What you get:\n' +
        '• 500+ pre-made tasks\n' +
        '• Age-specific categories\n' +
        '• Educational content\n' +
        '• Time-tested activities\n\n' +
        'Would you like to upgrade to Premium to unlock this feature?',
        [
          { text: 'Maybe Later', style: 'cancel' },
          { 
            text: 'Upgrade Now', 
            onPress: () => {
              Alert.alert('Upgrade', 'Please go to Settings > Subscription to upgrade your plan.');
            }
          }
        ]
      );
      return;
    }

    const childAge = selectedChild ? calculateAge(selectedChild.created_at) : 10;
    const libraryTasks = getTasksByAge(childAge);
    
    // Convert library tasks to suggestion format
    const suggestions = libraryTasks.slice(0, 8).map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      coins: task.coin_value,
      category: task.category,
      frequency: 'Daily',
      ageRange: task.age_group,
      instructions: task.instructions,
      tips: task.tips,
      isLibraryTask: true,
    }));

    setAISuggestions(suggestions);
    // setShowAIGeneration(true); // This state is managed by ZUSTAND
    playSound('button_press');
    // Record feature usage
    await recordFeatureUsage(user.id, 'task_library');
  };

  const calculateAge = (createdAt: string): number => {
    const now = new Date();
    const created = new Date(createdAt);
    const ageInMs = now.getTime() - created.getTime();
    const ageInYears = Math.floor(ageInMs / (1000 * 60 * 60 * 24 * 365));
    return Math.max(3, ageInYears + 7); // Assume child was created when they were 7
  };

  const handleUseSuggestion = (suggestion: any) => {
    setFormData('taskTitle', suggestion.title);
    setFormData('taskDescription', suggestion.description);
    setFormData('taskCoinValue', suggestion.coins?.toString() || suggestion.coin_value?.toString() || '5');
    setFormData('taskCategory', suggestion.category);
    setFormData('taskFrequency', suggestion.frequency || 'Daily');
    setShowSuggestions(false);
    setShowAIGeneration(false);
    playSound('button_press');
  };

  // Tutorial navigation handler
  const handleTutorialNavigation = (targetScreen: string) => {
    // For task creation screen, we don't need to navigate anywhere
    // Just mark the task as created when the user creates a task
    if (targetScreen === 'TaskCreation') {
      // The tutorial will be handled by the task creation success
    }
  };

  const filteredSuggestions = TASK_SUGGESTIONS.filter(suggestion => {
    const matchesAge = formData.selectedAgeRange === 'all' || suggestion.ageRange === formData.selectedAgeRange;
    const matchesCategory = formData.selectedCategory === 'all' || suggestion.category === formData.selectedCategory;
    return matchesAge && matchesCategory;
  });

  const uniqueAgeRanges = ['all', ...Array.from(new Set(TASK_SUGGESTIONS.map(s => s.ageRange)))];
  const uniqueCategories = ['all', ...Array.from(new Set(TASK_SUGGESTIONS.map(s => s.category)))];

  return (
    <View style={styles.container}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Header */}
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Task</Text>
        <View style={styles.headerSpacer} />
      </Animated.View>

      {/* Main Content */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Task Form */}
        <Animated.View style={[styles.formSection, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.formCard}>
            <Text style={styles.sectionTitle}>📝 Task Details</Text>
            
            <NintendoInput
              value={formData.taskTitle}
              onChangeText={(text) => {
                setFormData('taskTitle', text);
                // Clear selection when manually editing
                if (selectedSuggestion && text !== selectedSuggestion.title) {
                  setSelectedSuggestion(null);
                }
              }}
              placeholder="Task title (e.g., 'Make Bed')"
              style={styles.input}
            />
            
            <NintendoInput
              value={formData.taskDescription}
              onChangeText={(text) => setFormData('taskDescription', text)}
              placeholder="Description (optional)"
              multiline
              numberOfLines={2}
              style={styles.input}
            />
            
            <View style={styles.formRow}>
              <View style={styles.formHalf}>
                <Text style={styles.inputLabel}>Coin Value (1-100)</Text>
                <NintendoInput
                  value={formData.taskCoinValue}
                  onChangeText={(text) => {
                    // Only allow numbers and limit to 3 digits
                    const numericValue = text.replace(/[^0-9]/g, '');
                    if (numericValue.length <= 3) {
                      setFormData('taskCoinValue', numericValue);
                    }
                  }}
                  placeholder="5"
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
              
              <View style={styles.formHalf}>
                <Text style={styles.inputLabel}>Frequency</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionScroll}>
                  {TASK_FREQUENCIES.map((freq) => (
                    <TouchableOpacity
                      key={freq}
                      style={[
                        styles.optionButton,
                        formData.taskFrequency === freq && styles.optionButtonActive
                      ]}
                      onPress={() => {
                        setFormData('taskFrequency', freq);
                        playSound('selection_change');
                      }}
                    >
                      <Text style={[
                        styles.optionButtonText,
                        formData.taskFrequency === freq && styles.optionButtonTextActive
                      ]}>
                        {freq}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>
            
            <View style={styles.formRow}>
              <Text style={styles.inputLabel}>Category</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionScroll}>
                {TASK_CATEGORIES.map((cat) => (
                  <TouchableOpacity
                    key={cat}
                    style={[
                      styles.optionButton,
                      formData.taskCategory === cat && styles.optionButtonActive
                    ]}
                    onPress={() => {
                      setFormData('taskCategory', cat);
                      playSound('selection_change');
                    }}
                  >
                    <Text style={[
                      styles.optionButtonText,
                                              formData.taskCategory === cat && styles.optionButtonTextActive
                    ]}>
                      {cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            {/* Child Assignment Section */}
            {children && children.length > 0 && (
              <View style={styles.formRow}>
                <Text style={styles.inputLabel}>
                  Assign to ({(formData.selectedChildren || []).length} selected)
                </Text>
                <Text style={styles.inputHint}>
                  Optional: Select children to assign this task to immediately
                </Text>
                <View style={styles.childrenGrid}>
                  {children.map((child) => {
                    const isSelected = (formData.selectedChildren || []).includes(child.id);
                    return (
                      <TouchableOpacity
                        key={child.id}
                        style={[
                          styles.childButton,
                          isSelected && styles.childButtonActive
                        ]}
                        onPress={() => {
                          const currentSelected = formData.selectedChildren || [];
                          const newSelected = isSelected
                            ? currentSelected.filter(id => id !== child.id)
                            : [...currentSelected, child.id];
                          setFormData('selectedChildren', newSelected);
                          playSound('selection_change');
                        }}
                      >
                        <Text style={styles.childButtonEmoji}>
                          {child.avatar || '👶'}
                        </Text>
                        <Text style={[
                          styles.childButtonText,
                          isSelected && styles.childButtonTextActive
                        ]}>
                          {child.name}
                        </Text>
                        <Text style={styles.childButtonCoins}>
                          {child.coin_balance || 0} 🪙
                        </Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              </View>
            )}
          </NintendoCard>
        </Animated.View>

        {/* AI Generation Section */}
        <View style={styles.aiSection}>
          <View style={styles.aiSectionHeader}>
            <Text style={styles.aiSectionTitle}>🤖 AI-Powered Task Generation</Text>
            <View style={styles.premiumBadge}>
              <Text style={styles.premiumBadgeText}>PREMIUM</Text>
            </View>
          </View>
          
          <Text style={styles.aiSectionDescription}>
            Let AI create personalized tasks for your children based on their age, interests, and your family's patterns.
          </Text>

          {/* AI Generation Buttons */}
          <View style={styles.aiButtonsRow}>
            <NintendoButton
              title={aiGenerating ? "Generating..." : "🤖 AI Generate"}
              onPress={handleAIGeneration}
              variant="primary"
              style={styles.aiButton}
              disabled={aiGenerating}
            />
            <NintendoButton
              title={aiGenerating ? "Generating..." : "✨ Personalized"}
              onPress={handlePersonalizedGeneration}
              variant="secondary"
              style={styles.aiButton}
              disabled={aiGenerating || !selectedChild}
            />
          </View>

          {/* Library Tasks Button */}
          <NintendoButton
            title="📚 Load Library Tasks"
            onPress={handleLoadLibraryTasks}
            variant="success"
            style={styles.libraryButton}
          />

          {/* OpenAI Status */}
          {!isOpenAIConfigured() && (
            <View style={styles.aiStatusContainer}>
              <Text style={styles.aiStatusText}>
                💡 Add OpenAI API key in settings to enable AI generation
              </Text>
            </View>
          )}
        </View>

        {/* Toggle Suggestions */}
        <TouchableOpacity
          style={styles.toggleButton}
          onPress={() => {
            setShowSuggestions(!showSuggestions);
            playSound('button_press');
          }}
        >
          <Text style={styles.toggleButtonText}>
            {showSuggestions ? '🔽 Hide Manual Suggestions' : '🔼 Show Manual Suggestions'}
          </Text>
        </TouchableOpacity>

        {/* Task Suggestions */}
        {showSuggestions && (
          <Animated.View style={[styles.suggestionsSection, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>💡 Task Suggestions</Text>
            
            {/* Age Range Filter */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Age Range:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
                {uniqueAgeRanges.map((range) => (
                  <TouchableOpacity
                    key={range}
                    style={[
                      styles.filterButton,
                      formData.selectedAgeRange === range && styles.filterButtonActive
                    ]}
                    onPress={() => {
                      setFormData('selectedAgeRange', range);
                      playSound('selection_change');
                    }}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      formData.selectedAgeRange === range && styles.filterButtonTextActive
                    ]}>
                      {range === 'all' ? 'All Ages' : range}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Category Filter */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Category:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
                {uniqueCategories.map((cat) => (
                  <TouchableOpacity
                    key={cat}
                    style={[
                      styles.filterButton,
                      formData.selectedCategory === cat && styles.filterButtonActive
                    ]}
                    onPress={() => {
                      setFormData('selectedCategory', cat);
                      playSound('selection_change');
                    }}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      formData.selectedCategory === cat && styles.filterButtonTextActive
                    ]}>
                      {cat === 'all' ? 'All Categories' : cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            {/* Suggestions Grid */}
            <View style={styles.suggestionsGrid}>
                            {filteredSuggestions.map((suggestion, index) => (
                              <SuggestionCard
                key={index}
                title={suggestion.title}
                coins={suggestion.coins}
                category={suggestion.category}
                selected={selectedSuggestion?.title === suggestion.title}
                onPress={() => handleSuggestionPress(suggestion)}
                style={styles.suggestionCard}
              />
              ))}
            </View>
          </Animated.View>
        )}

        {/* AI Generated Suggestions */}
        {showAIGeneration && aiSuggestions.length > 0 && (
          <Animated.View style={[styles.aiSuggestionsSection, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>
              {aiSuggestions[0]?.isLibraryTask ? '📚 Library Tasks' : '🤖 AI Generated Tasks'}
            </Text>
            
            <View style={styles.aiSuggestionsGrid}>
              {aiSuggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.aiSuggestionCard}
                  onPress={() => handleUseSuggestion(suggestion)}
                >
                  <Text style={styles.aiSuggestionTitle}>{suggestion.title}</Text>
                  <Text style={styles.aiSuggestionDescription} numberOfLines={2}>
                    {suggestion.description}
                  </Text>
                  <View style={styles.aiSuggestionFooter}>
                    <Text style={styles.aiSuggestionCoins}>
                      {suggestion.coins || suggestion.coin_value} 🪙
                    </Text>
                    <Text style={styles.aiSuggestionCategory}>
                      {suggestion.category}
                    </Text>
                  </View>
                  {suggestion.instructions && (
                    <Text style={styles.aiSuggestionInstructions} numberOfLines={1}>
                      Steps: {suggestion.instructions.length}
                    </Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
            
            <TouchableOpacity
              style={styles.clearAIButton}
              onPress={() => {
                setShowAIGeneration(false);
                setAISuggestions([]);
                playSound('button_press');
              }}
            >
              <Text style={styles.clearAIButtonText}>Clear AI Suggestions</Text>
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Create Button */}
        <View style={styles.createButtonContainer}>
          <NintendoButton
            title={loading ? 'Creating...' : '🚀 Create Task'}
            onPress={handleCreateTask}
            variant="success"
                          disabled={loading || !formData.taskTitle.trim()}
            style={styles.createButton}
          />
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Tutorial Overlay */}
      <GuidedTutorialOverlay
        visible={tutorialState.isActive}
        currentStep={currentStep}
        onNext={nextStep}
        onComplete={() => {
          completeTutorial();
          setPersisted(false);
        }}
        onNavigate={handleTutorialNavigation}
        totalSteps={11}
        currentStepIndex={tutorialState.currentStepIndex}
        scrollViewRef={null}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  formSection: {
    marginBottom: 20,
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  input: {
    marginBottom: 15,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  formHalf: {
    flex: 1,
    marginHorizontal: 5,
  },
  optionScroll: {
    flexDirection: 'row',
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  optionButtonActive: {
    backgroundColor: '#4CAF50',
  },
  optionButtonText: {
    fontSize: 14,
    color: '#333',
  },
  optionButtonTextActive: {
    color: '#fff',
    fontWeight: 'bold',
  },
  toggleButton: {
    alignItems: 'center',
    paddingVertical: 15,
    marginBottom: 20,
  },
  toggleButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  suggestionsSection: {
    marginBottom: 20,
  },
  filterRow: {
    marginBottom: 15,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 8,
  },
  filterScroll: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    marginRight: 8,
  },
  filterButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#fff',
  },
  filterButtonTextActive: {
    color: '#333',
    fontWeight: 'bold',
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  suggestionCard: {
    width: '48%',
    marginBottom: 10,
  },
  createButtonContainer: {
    marginVertical: 20,
  },
  createButton: {
    minHeight: 50,
  },
  bottomSpacing: {
    height: 40,
  },
  
  // AI Generation Styles
  aiSection: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 20,
    padding: 20,
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#f39c12',
    borderStyle: 'dashed',
  },
  aiSectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  aiSectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  premiumBadge: {
    backgroundColor: '#f39c12',
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
  premiumBadgeText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: 'bold',
  },
  aiSectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  aiCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  childSelectionRow: {
    marginBottom: 15,
  },
  childScroll: {
    marginTop: 10,
  },
  childButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  childButtonActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  childButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  childButtonTextActive: {
    color: '#fff',
  },
  difficultyRow: {
    marginBottom: 15,
  },
  difficultyButtons: {
    flexDirection: 'row',
    marginTop: 10,
  },
  difficultyButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 15,
    marginHorizontal: 5,
    borderWidth: 1,
    borderColor: '#ddd',
    alignItems: 'center',
  },
  difficultyButtonActive: {
    backgroundColor: '#f39c12',
    borderColor: '#f39c12',
  },
  difficultyButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  difficultyButtonTextActive: {
    color: '#fff',
  },
  
  // Child Assignment Styles
  inputHint: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 10,
  },
  childrenGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  childButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 15,
    padding: 12,
    alignItems: 'center',
    minWidth: 80,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  childButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  childButtonEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  childButtonText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
  },
  childButtonTextActive: {
    color: '#fff',
  },
  childButtonCoins: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  aiButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  aiButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  libraryButton: {
    marginBottom: 15,
  },
  aiStatusContainer: {
    alignItems: 'center',
    marginTop: 10,
  },
  aiStatusText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  // AI Suggestions Display Styles
  aiSuggestionsSection: {
    marginBottom: 20,
  },
  aiSuggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  aiSuggestionCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#667eea',
    minHeight: 120,
  },
  aiSuggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  aiSuggestionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 10,
  },
  aiSuggestionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  aiSuggestionCoins: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#667eea',
  },
  aiSuggestionCategory: {
    fontSize: 12,
    color: '#999',
    fontWeight: '600',
  },
  aiSuggestionInstructions: {
    fontSize: 12,
    color: '#f39c12',
    marginTop: 5,
    fontStyle: 'italic',
  },
  clearAIButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  clearAIButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
});

export default TaskCreationScreen; 