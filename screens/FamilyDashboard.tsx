import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
} from 'react-native';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { useChildren } from '../hooks/useChildren';
import { useTasks } from '../hooks/useTasks';
import { useRewards } from '../hooks/useRewards';
import { useFamilySettings } from '../hooks/useFamilySettings';
import { useUIStore, useModal } from '../stores';

// Screen Components
import TaskCreationScreen from './TaskCreationScreen';
import TaskAssignmentScreen from './TaskAssignmentScreen';
import RewardCreationScreen from './RewardCreationScreen';
import ChildProfileScreen from './ChildProfileScreen';

// UI Components
import DevMenu from '../components/ui/DevMenu';

interface FamilyDashboardProps {
  onShowSettings: () => void;
}

const FamilyDashboard: React.FC<FamilyDashboardProps> = ({ onShowSettings }) => {
  const { user } = useAuth();
  const { playSound, playBackgroundMusic } = useAudio();
  const { children, loading } = useChildren();
  const { tasks, loading: tasksLoading } = useTasks();
  const { rewards, loading: rewardsLoading } = useRewards();
  const { familySettings, loading: settingsLoading } = useFamilySettings();
  
  // Test basic Zustand store
  const openModal = useUIStore((state) => state.openModal);
  const closeModal = useUIStore((state) => state.closeModal);
  const modals = useUIStore((state) => state.modals);
  
  // Test the useModal hook that might have caused the issue
  const taskCreationModal = useModal('taskCreation');

  // Start background music when dashboard loads
  useEffect(() => {
    playBackgroundMusic('main');
  }, [playBackgroundMusic]);

  // Show modal screens
  if (modals.taskCreation?.isOpen) {
    return (
      <TaskCreationScreen
        onBack={() => closeModal('taskCreation')}
        onTaskCreated={(task) => {
          closeModal('taskCreation');
        }}
      />
    );
  }

  if (modals.taskAssignment?.isOpen) {
    return (
      <TaskAssignmentScreen
        onBack={() => closeModal('taskAssignment')}
        onTaskAssigned={(assignedTasks) => {
          closeModal('taskAssignment');
        }}
      />
    );
  }

  if (modals.rewardCreation?.isOpen) {
    return (
      <RewardCreationScreen
        onBack={() => closeModal('rewardCreation')}
        onRewardCreated={(reward) => {
          closeModal('rewardCreation');
        }}
      />
    );
  }

  if (modals.childProfile?.isOpen) {
    return (
      <ChildProfileScreen
        onBack={() => closeModal('childProfile')}
        childId={modals.childProfile.data?.child?.id || 'unknown'}
        onEditProfile={(childId) => {
          closeModal('childProfile');
          openModal('childProfileEditor', { childId });
        }}
        onChildSettings={(childId) => {
          closeModal('childProfile');
          openModal('childSettings', { childId });
        }}

      />
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#f5f5f5' }}>
      <View style={{ 
        backgroundColor: '#FF6B9D', 
        paddingTop: 60, 
        paddingBottom: 20, 
        paddingHorizontal: 20 
      }}>
        <Text style={{ 
          fontSize: 24, 
          fontWeight: 'bold', 
          color: 'white', 
          textAlign: 'center' 
        }}>
          Family Dashboard
        </Text>
        <Text style={{ 
          fontSize: 14, 
          color: 'white', 
          textAlign: 'center', 
          marginTop: 5 
        }}>
          {children?.length || 0} children • {tasks?.length || 0} tasks • {rewards?.length || 0} rewards
        </Text>
      </View>

      <View style={{ flex: 1, padding: 20 }}>
        {/* Quick Actions */}
        <Text style={{ 
          fontSize: 18, 
          fontWeight: 'bold', 
          marginBottom: 15, 
          color: '#333' 
        }}>
          Quick Actions
        </Text>
        
        <View style={{ 
          flexDirection: 'row', 
          flexWrap: 'wrap', 
          gap: 10, 
          marginBottom: 30 
        }}>
          <TouchableOpacity
            style={{
              backgroundColor: '#4CAF50',
              padding: 15,
              borderRadius: 10,
              minWidth: 120,
              alignItems: 'center'
            }}
            onPress={() => {
              playSound('button_press');
              openModal('taskCreation');
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>
              Create Task
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: '#FF9800',
              padding: 15,
              borderRadius: 10,
              minWidth: 120,
              alignItems: 'center'
            }}
            onPress={() => {
              playSound('button_press');
              openModal('taskAssignment');
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>
              Assign Task
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={{
              backgroundColor: '#2196F3',
              padding: 15,
              borderRadius: 10,
              minWidth: 120,
              alignItems: 'center'
            }}
            onPress={() => {
              playSound('button_press');
              openModal('rewardCreation');
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>
              Create Reward
            </Text>
          </TouchableOpacity>

          {/* TODO: Add RewardAssignmentScreen for batch reward assignment
          <TouchableOpacity
            style={{
              backgroundColor: '#9C27B0',
              padding: 15,
              borderRadius: 10,
              minWidth: 120,
              alignItems: 'center'
            }}
            onPress={() => {
              playSound('button_press');
              openModal('rewardAssignment');
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>
              Assign Reward
            </Text>
          </TouchableOpacity>
          */}

          <TouchableOpacity
            style={{
              backgroundColor: '#FF9800',
              padding: 15,
              borderRadius: 10,
              minWidth: 120,
              alignItems: 'center'
            }}
            onPress={() => {
              playSound('button_press');
              onShowSettings();
            }}
          >
            <Text style={{ color: 'white', fontWeight: 'bold' }}>
              Settings
            </Text>
          </TouchableOpacity>
        </View>

        {/* Children List */}
        {children && children.length > 0 && (
          <View>
            <Text style={{ 
              fontSize: 18, 
              fontWeight: 'bold', 
              marginBottom: 15, 
              color: '#333' 
            }}>
              Children
            </Text>
            {children.map((child) => (
              <TouchableOpacity
                key={child.id}
                style={{
                  backgroundColor: 'white',
                  padding: 15,
                  borderRadius: 10,
                  marginBottom: 10,
                  flexDirection: 'row',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
                onPress={() => {
                  playSound('button_press');
                  openModal('childProfile', { child });
                }}
              >
                <View>
                  <Text style={{ fontSize: 16, fontWeight: 'bold', color: '#333' }}>
                    {child.name}
                  </Text>
                  <Text style={{ fontSize: 14, color: '#666' }}>
                    {child.coins || 0} coins
                  </Text>
                </View>
                <Text style={{ fontSize: 12, color: '#999' }}>
                  Tap to view
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        )}
      </View>
      
      {/* Developer Menu */}
      <DevMenu
        onShowSettings={onShowSettings}
        onResetOnboarding={() => {
          // Reset onboarding functionality
          console.log('Reset onboarding');
        }}
        onSignOut={() => {
          // Sign out functionality
          console.log('Sign out');
        }}
      />
    </View>
  );
};

export default FamilyDashboard;