import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar,
  ChildProfileSkeleton
} from '../components/ui';
import { supabase } from '../utils/supabase';
import { checkAchievements } from '../utils/rewardValidation';
import { useChildren, useTasks, useRewards } from '../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChildProfileScreenProps {
  onBack: () => void;
  childId: string;
  onEditProfile?: (childId: string) => void;
  onChildSettings?: (childId: string) => void;
}

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  parent_id: string;
  created_at: string;
}

interface Task {
  id: string;
  title: string;
  description?: string;
  value: number;
  status: 'pending' | 'completed';
  child_id: string;
  parent_id: string;
  created_at: string;
  completed_at?: string;
}

interface Reward {
  id: string;
  title: string;
  description?: string;
  cost: number;
  category?: string;
  parent_id: string;
  created_at: string;
}

const ChildProfileScreen: React.FC<ChildProfileScreenProps> = ({ 
  onBack, 
  childId, 
  onEditProfile, 
  onChildSettings 
}) => {
  const { children, loading: loadingChildren } = useChildren();
  const { tasks, loading: loadingTasks } = useTasks();
  const { rewards, loading: loadingRewards } = useRewards();
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // UI state only
  const [refreshing, setRefreshing] = useState(false);
  const [selectedTab, setSelectedTab] = useState<'overview' | 'tasks' | 'achievements' | 'rewards'>('overview');
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const sparkleAnim = useRef(new Animated.Value(0)).current;

  // Get selected child data
  const selectedChild = children.find(c => c.id === childId);
  const childTasks = tasks.filter(task => task.child_id === childId);
  const completedTasks = childTasks.filter(task => task.status === 'completed');
  const childRewards = rewards.filter(reward => reward.parent_id === user?.id);

  // Handle child not found
  useEffect(() => {
    if (!loadingChildren && children.length > 0 && !selectedChild) {
      Alert.alert(
        'Child Not Found',
        'This child profile could not be loaded. The child may have been deleted.',
        [
          { text: 'Go Back', onPress: onBack }
        ]
      );
    }
  }, [loadingChildren, children, selectedChild, onBack]);

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();

    // Sparkle animation
    Animated.loop(
      Animated.sequence([
        Animated.timing(sparkleAnim, {
          toValue: 1,
          duration: 2000,
          useNativeDriver: true,
        }),
        Animated.timing(sparkleAnim, {
          toValue: 0,
          duration: 2000,
          useNativeDriver: true,
        }),
      ])
    ).start();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    playSound('button_press');
    
    try {
      // Check for new achievements
      if (childId) {
        await checkAchievements(childId);
      }
    } catch (error) {
      console.error('Error refreshing child profile:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleTabPress = (tab: 'overview' | 'tasks' | 'achievements' | 'rewards') => {
    setSelectedTab(tab);
    playSound('button_press');
  };

  const handleEditProfile = () => {
    if (onEditProfile) {
      onEditProfile(childId);
    }
  };

  const handleChildSettings = () => {
    if (onChildSettings) {
      onChildSettings(childId);
    }
  };

  const getXPProgress = () => {
    if (!selectedChild) return 0;
    const currentLevel = selectedChild.level;
    const xpForCurrentLevel = currentLevel * 100; // 100 XP per level
    const xpForNextLevel = (currentLevel + 1) * 100;
    const progress = ((selectedChild.xp - xpForCurrentLevel) / (xpForNextLevel - xpForCurrentLevel)) * 100;
    return Math.min(Math.max(progress, 0), 100);
  };

  const getXPToNextLevel = () => {
    if (!selectedChild) return 0;
    const currentLevel = selectedChild.level;
    const xpForCurrentLevel = currentLevel * 100;
    const xpForNextLevel = (currentLevel + 1) * 100;
    return xpForNextLevel - selectedChild.xp;
  };

  const loading = loadingChildren || loadingTasks || loadingRewards;

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ChildProfileSkeleton />
      </View>
    );
  }

  if (!selectedChild) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>Child not found</Text>
        <NintendoButton title="Go Back" onPress={onBack} variant="back" />
      </View>
    );
  }

  return (
    <LinearGradient
      colors={['#667eea', '#764ba2']}
      style={styles.container}
    >
      <Animated.View
        style={[
          styles.content,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }],
          },
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>{selectedChild.name}</Text>
          <View style={styles.headerActions}>
            <TouchableOpacity onPress={handleEditProfile} style={styles.actionButton}>
              <Text style={styles.actionButtonText}>Edit</Text>
            </TouchableOpacity>
            <TouchableOpacity onPress={handleChildSettings} style={styles.actionButton}>
              <Text style={styles.actionButtonText}>Settings</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Child Stats */}
        <View style={styles.statsContainer}>
          <NintendoCard style={styles.statsCard}>
            <Text style={styles.childName}>{selectedChild.name}</Text>
            <Text style={styles.childLevel}>Level {selectedChild.level}</Text>
            <NintendoProgressBar 
              progress={getXPProgress()} 
              style={styles.xpBar}
            />
            <Text style={styles.xpText}>
              {selectedChild.xp} XP • {getXPToNextLevel()} XP to next level
            </Text>
            <Text style={styles.statsText}>
              Coins: {selectedChild.coin_balance} | Streak: {selectedChild.daily_streak} days
            </Text>
          </NintendoCard>
        </View>

        {/* Tab Navigation */}
        <View style={styles.tabContainer}>
          {(['overview', 'tasks', 'achievements', 'rewards'] as const).map((tab) => (
            <TouchableOpacity
              key={tab}
              style={[
                styles.tabButton,
                selectedTab === tab && styles.selectedTabButton,
              ]}
              onPress={() => handleTabPress(tab)}
            >
              <Text style={[
                styles.tabButtonText,
                selectedTab === tab && styles.selectedTabButtonText,
              ]}>
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Tab Content */}
        <ScrollView
          style={styles.scrollView}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
          }
        >
          {selectedTab === 'overview' && (
            <View style={styles.tabContent}>
              <Text style={styles.sectionTitle}>Overview</Text>
              <NintendoCard style={styles.overviewCard}>
                <Text style={styles.overviewText}>
                  Total Tasks: {childTasks.length}
                </Text>
                <Text style={styles.overviewText}>
                  Completed: {completedTasks.length}
                </Text>
                <Text style={styles.overviewText}>
                  Available Rewards: {childRewards.length}
                </Text>
              </NintendoCard>
            </View>
          )}

          {selectedTab === 'tasks' && (
            <View style={styles.tabContent}>
              <Text style={styles.sectionTitle}>Tasks</Text>
              {childTasks.map((task) => (
                <NintendoCard key={task.id} style={styles.taskCard}>
                  <Text style={styles.taskTitle}>{task.title}</Text>
                  <Text style={styles.taskDescription}>{task.description}</Text>
                  <Text style={styles.taskValue}>{task.value} coins</Text>
                  <Text style={styles.taskStatus}>
                    Status: {task.status}
                  </Text>
                </NintendoCard>
              ))}
            </View>
          )}

          {selectedTab === 'rewards' && (
            <View style={styles.tabContent}>
              <Text style={styles.sectionTitle}>Rewards</Text>
              {childRewards.map((reward) => (
                <NintendoCard key={reward.id} style={styles.rewardCard}>
                  <Text style={styles.rewardTitle}>{reward.title}</Text>
                  <Text style={styles.rewardDescription}>{reward.description}</Text>
                  <Text style={styles.rewardCost}>{reward.cost} coins</Text>
                </NintendoCard>
              ))}
            </View>
          )}

          {selectedTab === 'achievements' && (
            <View style={styles.tabContent}>
              <Text style={styles.sectionTitle}>Achievements</Text>
              <Text style={styles.comingSoon}>Coming Soon!</Text>
            </View>
          )}
        </ScrollView>
      </Animated.View>
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#666',
    marginBottom: 20,
  },
  content: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
  },
  backButton: {
    marginRight: 20,
  },
  backButtonText: {
    color: 'white',
    fontSize: 18,
  },
  title: {
    color: 'white',
    fontSize: 24,
    fontWeight: 'bold',
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    marginLeft: 15,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
  },
  statsContainer: {
    padding: 20,
  },
  statsCard: {
    marginBottom: 20,
  },
  childName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  childLevel: {
    fontSize: 18,
    color: '#666',
    marginBottom: 10,
  },
  xpBar: {
    marginBottom: 10,
  },
  xpText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
  },
  statsText: {
    fontSize: 16,
    marginBottom: 5,
  },
  tabContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  tabButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  selectedTabButton: {
    borderBottomColor: '#FF6B9D',
  },
  tabButtonText: {
    color: 'white',
    fontSize: 16,
  },
  selectedTabButtonText: {
    fontWeight: 'bold',
  },
  scrollView: {
    flex: 1,
  },
  tabContent: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 15,
  },
  overviewCard: {
    marginBottom: 20,
  },
  overviewText: {
    fontSize: 16,
    marginBottom: 5,
  },
  taskCard: {
    marginBottom: 15,
  },
  taskTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  taskValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B9D',
  },
  taskStatus: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  rewardCard: {
    marginBottom: 15,
  },
  rewardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  rewardCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B9D',
  },
  comingSoon: {
    fontSize: 18,
    color: '#666',
    textAlign: 'center',
    marginTop: 50,
  },
});

export default ChildProfileScreen;
