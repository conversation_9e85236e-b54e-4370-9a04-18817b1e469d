import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useChildren } from '../hooks/useChildren';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  NintendoProgressBar 
} from '../components/ui';
// Removed old API imports - using direct Supabase calls instead
import { supabase } from '../utils/supabase';
import { 
  useFormData, 
  useFormActions, 
  useLoadingState, 
  useErrorState,
  useModal,
  useModalActions
} from '../stores';


const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChildProfileEditorScreenProps {
  onBack: () => void;
  childId: string;
  onProfileUpdated?: (childId: string) => void;
}

interface ChildProfile {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  last_activity_date: string;
  created_at: string;
}

// Available avatars for selection
const AVATAR_OPTIONS = [
  '😊', '😄', '😁', '🤗', '😍', '🥰', '😘', '😜', '🤨', '😏',
  '🤓', '😎', '🤩', '🥳', '😇', '🙂', '😐', '😑', '🙃', '😉',
  '👦', '👧', '🧒', '👶', '👨', '👩', '🧑', '👴', '👵', '🧓',
  '🦸', '🦹', '🧙', '🧚', '🧜', '🧝', '🧞', '🧟', '🤖', '👽',
  '🐶', '🐱', '🐭', '🐹', '🐰', '🦊', '🐻', '🐼', '🐨', '🐯',
  '🦁', '🐮', '🐷', '🐸', '🐵', '🐔', '🐧', '🐦', '🐤', '🦄',
];

const ChildProfileEditorScreen: React.FC<ChildProfileEditorScreenProps> = ({ 
  onBack, 
  childId, 
  onProfileUpdated 
}) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // ZUSTAND: Client state
  const formData = useFormData();
  const { setFormData, resetForm } = useFormActions();
  const loading = useLoadingState('childProfileEditor');
  const error = useErrorState('childProfileEditor');
  const saving = useLoadingState('childProfileSaving');
  const avatarPickerModal = useModal('avatarPicker');
  const { openModal, closeModal } = useModalActions();
  
  // TANSTACK QUERY: Server state
  const { children } = useChildren();
  const childProfile = children.find(child => child.id === childId);
  
  // Computed state (derived from server data + form data)
  const name = formData.childName || childProfile?.name || '';
  const selectedAvatar = formData.childAvatar || childProfile?.avatar || '😊';
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const avatarPickerAnim = useRef(new Animated.Value(0)).current;

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Avatar picker animation
  useEffect(() => {
    if (avatarPickerModal.isOpen) {
      Animated.spring(avatarPickerAnim, {
        toValue: 1,
        tension: 50,
        friction: 4,
        useNativeDriver: true,
      }).start();
    } else {
      avatarPickerAnim.setValue(0);
    }
  }, [avatarPickerModal.isOpen]);

  // Initialize form data when child profile loads
  useEffect(() => {
    if (childProfile) {
      setFormData('childName', childProfile.name);
      setFormData('childAvatar', childProfile.avatar);
    }
  }, [childProfile, setFormData]);

  const handleSaveProfile = async () => {
    if (!childProfile) return;
    
    playSound('button_press');
    
    // Validate form data
    if (!name.trim()) {
      Alert.alert('Validation Error', 'Please enter a name for the child.');
      return;
    }
    
    try {
      // TANSTACK QUERY: Update child profile
      const { error } = await supabase
        .from('children')
        .update({
          name: name.trim(),
          avatar: selectedAvatar,
        })
        .eq('id', childId);
      
      if (error) throw error;
      
      // ZUSTAND: Show success feedback
      Alert.alert(
        '✅ Profile Updated!',
        `${name}'s profile has been updated successfully!`,
        [{ text: 'OK', onPress: () => {
          onProfileUpdated?.(childId);
          onBack();
        }}]
      );
      
      // Reset form
      resetForm();
      
    } catch (error) {
      console.error('Error updating child profile:', error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    }
  };
  
  const handleAvatarSelect = (avatar: string) => {
    playSound('button_press');
    
    // ZUSTAND: Update form data
    setFormData('childAvatar', avatar);
    closeModal('avatarPicker');
  };
  
  const handleShowAvatarPicker = () => {
    playSound('button_press');
    openModal('avatarPicker');
  };
  
  const handleHideAvatarPicker = () => {
    closeModal('avatarPicker');
  };
  
  const handleResetProgress = () => {
    playSound('button_press');
    
    Alert.alert(
      '⚠️ Reset Progress',
      'This will reset all progress for this child including coins, level, and achievements. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Reset', 
          style: 'destructive',
          onPress: performResetProgress 
        }
      ]
    );
  };
  
  const performResetProgress = async () => {
    if (!childProfile) return;
    
    try {
      // TANSTACK QUERY: Reset child progress
      const { error } = await supabase.rpc('reset_child_progress', { child_id: childId });
      
      if (error) throw error;
      
      Alert.alert(
        '✅ Progress Reset',
        `${childProfile.name}'s progress has been reset successfully.`,
        [{ text: 'OK' }]
      );
      
    } catch (error) {
      console.error('Error resetting progress:', error);
      Alert.alert('Error', 'Failed to reset progress. Please try again.');
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.loadingContainer}>
          <NintendoProgressBar progress={0.7} />
          <Text style={styles.loadingText}>Loading editor...</Text>
        </View>
      </View>
    );
  }

  if (!childProfile) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Child profile not found</Text>
          <NintendoButton
            title="Go Back"
            onPress={onBack}
            variant="secondary"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Profile</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Profile Editor */}
        <Animated.View style={[styles.section, { 
          opacity: fadeInAnim,
          transform: [{ translateY: slideInAnim }]
        }]}>
          <NintendoCard variant="elevated" withShadow style={styles.editorCard}>
            <Text style={styles.sectionTitle}>✏️ Profile Details</Text>
            
            {/* Avatar Selection */}
            <View style={styles.avatarSection}>
              <Text style={styles.inputLabel}>Avatar</Text>
              <TouchableOpacity 
                style={styles.avatarButton}
                onPress={handleShowAvatarPicker}
              >
                <Text style={styles.avatarButtonText}>{selectedAvatar}</Text>
                <Text style={styles.avatarButtonLabel}>Tap to change</Text>
              </TouchableOpacity>
            </View>

            {/* Name Input */}
            <View style={styles.nameSection}>
              <NintendoInput
                label="Name"
                value={name}
                onChangeText={(text) => setFormData('childName', text)}
                placeholder="Enter child's name"
                style={styles.nameInput}
                maxLength={50}
              />
            </View>

            {/* Current Stats Display */}
            <View style={styles.statsSection}>
              <Text style={styles.inputLabel}>Current Stats</Text>
              <View style={styles.statsGrid}>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{childProfile.level}</Text>
                  <Text style={styles.statLabel}>Level</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{childProfile.xp}</Text>
                  <Text style={styles.statLabel}>XP</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{childProfile.coin_balance}</Text>
                  <Text style={styles.statLabel}>Coins</Text>
                </View>
                <View style={styles.statItem}>
                  <Text style={styles.statNumber}>{childProfile.daily_streak}</Text>
                  <Text style={styles.statLabel}>Streak</Text>
                </View>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Advanced Options */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.advancedCard}>
            <Text style={styles.sectionTitle}>⚙️ Advanced Options</Text>
            
            <View style={styles.advancedOption}>
              <View style={styles.optionInfo}>
                <Text style={styles.optionTitle}>Reset Progress</Text>
                <Text style={styles.optionDescription}>
                  Reset all progress including level, XP, streak, tasks, achievements, and rewards.
                </Text>
              </View>
              <NintendoButton
                title="Reset"
                onPress={handleResetProgress}
                variant="danger"
                size="small"
                disabled={saving}
              />
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Action Buttons */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <View style={styles.actionButtons}>
            <NintendoButton
              title="Cancel"
              onPress={onBack}
              variant="secondary"
              style={styles.actionButton}
              disabled={saving}
            />
            <NintendoButton
              title={saving ? 'Saving...' : 'Save Changes'}
              onPress={handleSaveProfile}
              variant="success"
              style={styles.actionButton}
              disabled={saving}
            />
          </View>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Avatar Picker Modal */}
      {avatarPickerModal.isOpen && (
        <View style={styles.avatarPickerOverlay}>
          <TouchableOpacity 
            style={styles.avatarPickerBackdrop}
            onPress={handleHideAvatarPicker}
          />
          <Animated.View
            style={[
              styles.avatarPickerContainer,
              {
                transform: [
                  {
                    scale: avatarPickerAnim,
                  },
                ],
                opacity: avatarPickerAnim,
              },
            ]}
          >
            <NintendoCard variant="elevated" withShadow style={styles.avatarPickerCard}>
              <Text style={styles.avatarPickerTitle}>Choose Avatar</Text>
              <ScrollView style={styles.avatarPickerScroll}>
                <View style={styles.avatarGrid}>
                  {AVATAR_OPTIONS.map((avatar, index) => (
                    <TouchableOpacity
                      key={index}
                      style={[
                        styles.avatarOption,
                        selectedAvatar === avatar && styles.avatarOptionSelected
                      ]}
                      onPress={() => handleAvatarSelect(avatar)}
                    >
                      <Text style={styles.avatarOptionText}>{avatar}</Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </ScrollView>
              <NintendoButton
                title="Cancel"
                onPress={handleHideAvatarPicker}
                variant="secondary"
                style={styles.avatarPickerCancelButton}
              />
            </NintendoCard>
          </Animated.View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  editorCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  avatarSection: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  avatarButton: {
    alignItems: 'center',
    paddingVertical: 20,
    paddingHorizontal: 15,
    backgroundColor: '#f0f0f0',
    borderRadius: 15,
    borderWidth: 2,
    borderColor: '#ddd',
  },
  avatarButtonText: {
    fontSize: 48,
    marginBottom: 5,
  },
  avatarButtonLabel: {
    fontSize: 14,
    color: '#666',
  },
  nameSection: {
    marginBottom: 20,
  },
  nameInput: {
    marginBottom: 0,
  },
  statsSection: {
    marginBottom: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    borderRadius: 15,
    padding: 15,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#667eea',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  advancedCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  advancedOption: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  optionInfo: {
    flex: 1,
    marginRight: 15,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  optionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  avatarPickerOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  avatarPickerBackdrop: {
    ...StyleSheet.absoluteFillObject,
  },
  avatarPickerContainer: {
    width: SCREEN_WIDTH - 40,
    maxHeight: 500,
  },
  avatarPickerCard: {
    backgroundColor: '#fff',
    padding: 20,
  },
  avatarPickerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 20,
  },
  avatarPickerScroll: {
    maxHeight: 300,
  },
  avatarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  avatarOption: {
    width: 50,
    height: 50,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 25,
    marginBottom: 15,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  avatarOptionSelected: {
    borderColor: '#667eea',
    backgroundColor: '#e8f0fe',
  },
  avatarOptionText: {
    fontSize: 24,
  },
  avatarPickerCancelButton: {
    marginTop: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default ChildProfileEditorScreen; 