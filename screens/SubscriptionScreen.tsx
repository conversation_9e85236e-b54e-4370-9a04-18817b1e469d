import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import {
  getUserSubscription,
  upgradeSubscription,
  checkFeatureAccess,
  getAllFeatures,
  CORE_FEATURES,
  ADVANCED_FEATURES,
  MonetizationFeature,
  UserSubscription,
} from '../utils/unifiedMonetization';
import NintendoButton from '../components/ui/NintendoButton';
import NintendoCard from '../components/ui/NintendoCard';

interface SubscriptionScreenProps {
  navigation: any;
}

export default function SubscriptionScreen({ navigation }: SubscriptionScreenProps) {
  const { user } = useAuth();
  const { playSound } = useAudio();
  const [subscription, setSubscription] = useState<UserSubscription | null>(null);
  const [loading, setLoading] = useState(true);
  const [upgrading, setUpgrading] = useState(false);
  const [featureAccess, setFeatureAccess] = useState<{ [key: string]: any }>({});

  useEffect(() => {
    loadSubscription();
  }, []);

  const loadSubscription = async () => {
    try {
      setLoading(true);
      if (!user) return;

      const userSub = await getUserSubscription(user.id);
      setSubscription(userSub);

      // Check access for all features
      const allFeatures = getAllFeatures();
      const accessChecks = await Promise.all(
        allFeatures.map(async (feature) => {
          const access = await checkFeatureAccess(user.id, feature.id);
          return { [feature.id]: access };
        })
      );

      const accessMap = accessChecks.reduce((acc, check) => ({ ...acc, ...check }), {});
      setFeatureAccess(accessMap);
    } catch (error) {
      console.error('Error loading subscription:', error);
      Alert.alert('Error', 'Failed to load subscription information');
    } finally {
      setLoading(false);
    }
  };

  const handleUpgrade = async (tier: 'basic' | 'premium' | 'enterprise') => {
    if (!user) return;

    const tierFeatures = {
      basic: ['custom_categories', 'custom_coin_names', 'unlimited_children'],
      premium: [
        'ai_task_generation',
        'ai_reward_generation',
        'ai_personalized_suggestions',
        'advanced_analytics',
        'learning_insights',
        'family_sharing',
        'export_data',
      ],
      enterprise: [
        'ai_task_generation',
        'ai_reward_generation',
        'ai_personalized_suggestions',
        'advanced_analytics',
        'learning_insights',
        'family_sharing',
        'export_data',
        'custom_categories',
        'custom_coin_names',
        'unlimited_children',
      ],
    };

    Alert.alert(
      'Upgrade Subscription',
      `Are you sure you want to upgrade to ${tier} tier?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Upgrade',
          onPress: async () => {
            try {
              setUpgrading(true);
                    const features = [...CORE_FEATURES.map(f => f.id), ...tierFeatures[tier]];
      await upgradeSubscription(user.id, tier, 'one_time');
              await loadSubscription();
              playSound('achievement_unlock');
              Alert.alert('Success', `Upgraded to ${tier} tier!`);
            } catch (error) {
              console.error('Error upgrading subscription:', error);
              Alert.alert('Error', 'Failed to upgrade subscription');
            } finally {
              setUpgrading(false);
            }
          },
        },
      ]
    );
  };

  const renderTierCard = (tier: 'free' | 'basic' | 'premium' | 'enterprise') => {
    const isCurrentTier = subscription?.tier === tier;
    const tierInfo = {
      free: {
        name: 'Free',
        price: '$0',
        color: '#4CAF50',
        features: CORE_FEATURES.map(f => f.name),
      },
      basic: {
        name: 'Basic',
        price: '$4.99/month',
        color: '#FF9800',
        features: [
          'Custom Categories',
          'Custom Coin Names',
          'Unlimited Children',
          ...CORE_FEATURES.map(f => f.name),
        ],
      },
      premium: {
        name: 'Premium',
        price: '$19.99/month',
        color: '#2196F3',
        features: [
          'AI Task Generation',
          'AI Reward Generation',
          'Personalized AI Suggestions',
          'Advanced Analytics',
          'Learning Insights',
          'Family Sharing',
          'Data Export',
          ...CORE_FEATURES.map(f => f.name),
        ],
      },
      enterprise: {
        name: 'Enterprise',
        price: '$49.99/month',
        color: '#9C27B0',
        features: [
          'All Premium Features',
          'Priority Support',
          'Custom Integrations',
          'Advanced Reporting',
          'Dedicated Account Manager',
        ],
      },
    };

    const info = tierInfo[tier];

    return (
      <NintendoCard
        key={tier}
        style={[
          styles.tierCard,
          isCurrentTier && styles.currentTierCard,
          { borderColor: info.color },
        ] as any}
      >
        <View style={styles.tierHeader}>
          <View style={styles.tierInfo}>
            <Text style={[styles.tierName, { color: info.color }]}>{info.name}</Text>
            <Text style={styles.tierPrice}>{info.price}</Text>
          </View>
          {isCurrentTier && (
            <View style={[styles.currentBadge, { backgroundColor: info.color }]}>
              <Text style={styles.currentBadgeText}>CURRENT</Text>
            </View>
          )}
        </View>

        <View style={styles.featureList}>
          {info.features.slice(0, 5).map((feature, index) => (
            <View key={index} style={styles.featureItem}>
              <Ionicons name="checkmark-circle" size={16} color={info.color} />
              <Text style={styles.featureText}>{feature}</Text>
            </View>
          ))}
          {info.features.length > 5 && (
            <Text style={styles.moreFeatures}>+{info.features.length - 5} more features</Text>
          )}
        </View>

        {!isCurrentTier && tier !== 'free' && (
          <NintendoButton
            title={`Upgrade to ${info.name}`}
            onPress={() => handleUpgrade(tier as any)}
            style={[styles.upgradeButton, { backgroundColor: info.color }] as any}
            disabled={upgrading}
          />
        )}
      </NintendoCard>
    );
  };

  const renderFeatureStatus = (feature: MonetizationFeature) => {
    const access = featureAccess[feature.id];
    const hasAccess = access?.hasAccess || false;
    const usage = access?.usage;

    return (
      <View key={feature.id} style={styles.featureStatusItem}>
        <View style={styles.featureStatusHeader}>
          <View style={styles.featureStatusInfo}>
            <Text style={styles.featureStatusName}>{feature.name}</Text>
            <Text style={styles.featureStatusDescription}>{feature.description}</Text>
          </View>
          <View style={[styles.statusIndicator, hasAccess ? styles.statusActive : styles.statusInactive]}>
            <Ionicons
              name={hasAccess ? 'checkmark-circle' : 'close-circle'}
              size={20}
              color={hasAccess ? '#4CAF50' : '#f44336'}
            />
          </View>
        </View>
        
        {hasAccess && usage && (
          <View style={styles.usageInfo}>
            <Text style={styles.usageText}>
              Usage: {usage.current || 0}
              {usage.limits?.daily && ` / ${usage.limits.daily} daily`}
              {usage.limits?.monthly && ` / ${usage.limits.monthly} monthly`}
            </Text>
          </View>
        )}
        
        {!hasAccess && access?.reason && (
          <Text style={styles.accessReason}>{access.reason}</Text>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading subscription...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Subscription</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Current Subscription */}
        {subscription && (
          <NintendoCard style={styles.currentSubscriptionCard}>
            <Text style={styles.sectionTitle}>Current Plan</Text>
            <View style={styles.currentPlanInfo}>
              <Text style={styles.currentPlanName}>
                {subscription.tier.charAt(0).toUpperCase() + subscription.tier.slice(1)} Plan
              </Text>
              <Text style={styles.currentPlanStatus}>
                Status: {subscription.status}
              </Text>
              <Text style={styles.currentPlanDate}>
                Started: {new Date(subscription.start_date).toLocaleDateString()}
              </Text>
            </View>
          </NintendoCard>
        )}

        {/* Available Tiers */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Available Plans</Text>
          <Text style={styles.sectionDescription}>
            Choose the plan that best fits your family's needs
          </Text>
        </View>

        {renderTierCard('free')}
        {renderTierCard('basic')}
        {renderTierCard('premium')}
        {renderTierCard('enterprise')}

        {/* Feature Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Feature Access</Text>
          <Text style={styles.sectionDescription}>
            See which features you have access to
          </Text>
        </View>

        <NintendoCard style={styles.featureStatusCard}>
          {getAllFeatures().map(renderFeatureStatus)}
        </NintendoCard>

        {/* Help Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Need Help?</Text>
          <NintendoCard style={styles.helpCard}>
            <Text style={styles.helpText}>
              If you have questions about your subscription or need to make changes, 
              please contact our support team.
            </Text>
            <NintendoButton
              title="Contact Support"
              onPress={() => {
                // TODO: Implement support contact
                Alert.alert('Support', 'Support contact feature coming soon!');
              }}
              style={styles.supportButton}
            />
          </NintendoCard>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#333',
  },
  headerSpacer: {
    width: 34,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  currentSubscriptionCard: {
    marginBottom: 30,
  },
  currentPlanInfo: {
    gap: 8,
  },
  currentPlanName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  currentPlanStatus: {
    fontSize: 16,
    color: '#666',
  },
  currentPlanDate: {
    fontSize: 14,
    color: '#999',
  },
  tierCard: {
    marginBottom: 20,
    borderWidth: 2,
    borderColor: '#e0e0e0',
  },
  currentTierCard: {
    borderWidth: 3,
  },
  tierHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  tierInfo: {
    flex: 1,
  },
  tierName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  tierPrice: {
    fontSize: 18,
    fontWeight: '600',
    color: '#666',
  },
  currentBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  currentBadgeText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  featureList: {
    marginBottom: 20,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  featureText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 8,
  },
  moreFeatures: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginTop: 5,
  },
  upgradeButton: {
    marginTop: 10,
  },
  featureStatusCard: {
    marginBottom: 30,
  },
  featureStatusItem: {
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  featureStatusHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  featureStatusInfo: {
    flex: 1,
  },
  featureStatusName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 4,
  },
  featureStatusDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  statusIndicator: {
    padding: 5,
  },
  statusActive: {
    // Green checkmark
  },
  statusInactive: {
    // Red X
  },
  usageInfo: {
    marginTop: 8,
  },
  usageText: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
  },
  accessReason: {
    fontSize: 12,
    color: '#f44336',
    marginTop: 8,
  },
  helpCard: {
    marginBottom: 30,
  },
  helpText: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 20,
  },
  supportButton: {
    alignSelf: 'flex-start',
  },
}); 