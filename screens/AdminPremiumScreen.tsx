import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Alert,
  Switch,
  TouchableOpacity,
  TextInput,
  Modal,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { supabase } from '../utils/supabase';
import {
  getAdminMonetizationConfig,
  updateAdminMonetizationConfig,
  resetFeatureToDefault,
  getMonetizationAnalytics,
  getAllFeatures,
  CORE_FEATURES,
  ADVANCED_FEATURES,
  MonetizationFeature,
  AdminMonetizationConfig,
} from '../utils/unifiedMonetization';
import NintendoButton from '../components/ui/NintendoButton';
import NintendoCard from '../components/ui/NintendoCard';

interface AdminPremiumScreenProps {
  navigation: any;
}

export default function AdminPremiumScreen({ navigation }: AdminPremiumScreenProps) {
  const [features, setFeatures] = useState<MonetizationFeature[]>([]);
  const [adminConfigs, setAdminConfigs] = useState<AdminMonetizationConfig[]>([]);
  const [analytics, setAnalytics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [editingFeature, setEditingFeature] = useState<MonetizationFeature | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editForm, setEditForm] = useState({
    is_enabled: true,
    is_premium: true,
    tier_requirement: 'premium',
    daily_limit: '',
    monthly_limit: '',
    total_limit: '',
    monthly_price: '',
    yearly_price: '',
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [configs, analyticsData] = await Promise.all([
        getAdminMonetizationConfig(),
        getMonetizationAnalytics(),
      ]);
      
      setAdminConfigs(configs);
      setAnalytics(analyticsData);
      setFeatures(getAllFeatures());
    } catch (error) {
      console.error('Error loading admin data:', error);
      Alert.alert('Error', 'Failed to load admin data');
    } finally {
      setLoading(false);
    }
  };

  const getFeatureConfig = (featureId: string): AdminMonetizationConfig | null => {
    return adminConfigs.find(config => config.feature_id === featureId) || null;
  };

  const handleToggleFeature = async (feature: MonetizationFeature, enabled: boolean) => {
    try {
      const config = getFeatureConfig(feature.id);
      const newConfig = {
        feature_id: feature.id,
        is_enabled: enabled,
        is_premium: config?.is_premium ?? feature.is_premium,
        tier_requirement: config?.tier_requirement ?? feature.tier,
        usage_limits: config?.usage_limits ?? feature.usage_limits,
        pricing: config?.pricing ?? feature.pricing,
      };

      await updateAdminMonetizationConfig(feature.id, newConfig);
      await loadData();
      Alert.alert('Success', `${feature.name} ${enabled ? 'enabled' : 'disabled'}`);
    } catch (error) {
      console.error('Error toggling feature:', error);
      Alert.alert('Error', 'Failed to update feature');
    }
  };

  const handleTogglePremium = async (feature: MonetizationFeature, premium: boolean) => {
    try {
      const config = getFeatureConfig(feature.id);
      const newConfig = {
        feature_id: feature.id,
        is_enabled: config?.is_enabled ?? true,
        is_premium: premium,
        tier_requirement: config?.tier_requirement ?? feature.tier,
        usage_limits: config?.usage_limits ?? feature.usage_limits,
        pricing: config?.pricing ?? feature.pricing,
      };

      await updateAdminMonetizationConfig(feature.id, newConfig);
      await loadData();
      Alert.alert('Success', `${feature.name} is now ${premium ? 'premium' : 'free'}`);
    } catch (error) {
      console.error('Error toggling premium:', error);
      Alert.alert('Error', 'Failed to update premium status');
    }
  };

  const handleResetFeature = async (feature: MonetizationFeature) => {
    Alert.alert(
      'Reset Feature',
      `Are you sure you want to reset ${feature.name} to default settings?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: async () => {
            try {
              await resetFeatureToDefault(feature.id);
              await loadData();
              Alert.alert('Success', `${feature.name} reset to default`);
            } catch (error) {
              console.error('Error resetting feature:', error);
              Alert.alert('Error', 'Failed to reset feature');
            }
          },
        },
      ]
    );
  };

  const openEditModal = (feature: MonetizationFeature) => {
    const config = getFeatureConfig(feature.id);
    setEditingFeature(feature);
    setEditForm({
      is_enabled: config?.is_enabled ?? true,
      is_premium: config?.is_premium ?? feature.is_premium,
      tier_requirement: config?.tier_requirement ?? feature.tier,
      daily_limit: config?.usage_limits?.daily?.toString() ?? '',
      monthly_limit: config?.usage_limits?.monthly?.toString() ?? '',
      total_limit: config?.usage_limits?.total?.toString() ?? '',
      monthly_price: config?.pricing?.monthly?.toString() ?? '',
      yearly_price: config?.pricing?.yearly?.toString() ?? '',
    });
    setEditModalVisible(true);
  };

  const handleSaveEdit = async () => {
    if (!editingFeature) return;

    try {
      const usage_limits: any = {};
      if (editForm.daily_limit) usage_limits.daily = parseInt(editForm.daily_limit);
      if (editForm.monthly_limit) usage_limits.monthly = parseInt(editForm.monthly_limit);
      if (editForm.total_limit) usage_limits.total = parseInt(editForm.total_limit);

      const pricing: any = {};
      if (editForm.monthly_price) pricing.monthly = parseFloat(editForm.monthly_price);
      if (editForm.yearly_price) pricing.yearly = parseFloat(editForm.yearly_price);

      const newConfig = {
        feature_id: editingFeature.id,
        is_enabled: editForm.is_enabled,
        is_premium: editForm.is_premium,
        tier_requirement: editForm.tier_requirement as any,
        usage_limits,
        pricing,
      };

      await updateAdminMonetizationConfig(editingFeature.id, newConfig);
      await loadData();
      setEditModalVisible(false);
      Alert.alert('Success', `${editingFeature.name} updated successfully`);
    } catch (error) {
      console.error('Error saving edit:', error);
      Alert.alert('Error', 'Failed to save changes');
    }
  };

  const renderFeatureCard = (feature: MonetizationFeature) => {
    const config = getFeatureConfig(feature.id);
    const isEnabled = config?.is_enabled ?? true;
    const isPremium = config?.is_premium ?? feature.is_premium;
    const tier = config?.tier_requirement ?? feature.tier;

    return (
      <NintendoCard key={feature.id} style={styles.featureCard}>
        <View style={styles.featureHeader}>
          <View style={styles.featureInfo}>
            <Text style={styles.featureName}>{feature.name}</Text>
            <Text style={styles.featureDescription}>{feature.description}</Text>
            <View style={styles.featureMeta}>
              <View style={[styles.tierBadge, styles[`tier${tier}`]]}>
                <Text style={styles.tierText}>{tier.toUpperCase()}</Text>
              </View>
              <View style={[styles.categoryBadge, styles[`category${feature.category}`]]}>
                <Text style={styles.categoryText}>{feature.category.replace('_', ' ')}</Text>
              </View>
            </View>
          </View>
          <TouchableOpacity
            style={styles.editButton}
            onPress={() => openEditModal(feature)}
          >
            <Ionicons name="settings-outline" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        <View style={styles.featureControls}>
          <View style={styles.controlRow}>
            <Text style={styles.controlLabel}>Enabled</Text>
            <Switch
              value={isEnabled}
              onValueChange={(value) => handleToggleFeature(feature, value)}
              trackColor={{ false: '#ddd', true: '#4CAF50' }}
              thumbColor={isEnabled ? '#fff' : '#f4f3f4'}
            />
          </View>

          <View style={styles.controlRow}>
            <Text style={styles.controlLabel}>Premium</Text>
            <Switch
              value={isPremium}
              onValueChange={(value) => handleTogglePremium(feature, value)}
              trackColor={{ false: '#ddd', true: '#FF9800' }}
              thumbColor={isPremium ? '#fff' : '#f4f3f4'}
            />
          </View>

          <TouchableOpacity
            style={styles.resetButton}
            onPress={() => handleResetFeature(feature)}
          >
            <Text style={styles.resetButtonText}>Reset to Default</Text>
          </TouchableOpacity>
        </View>
      </NintendoCard>
    );
  };

  const renderAnalytics = () => {
    if (!analytics) return null;

    return (
      <NintendoCard style={styles.analyticsCard}>
        <Text style={styles.sectionTitle}>Premium Analytics</Text>
        <View style={styles.analyticsGrid}>
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>{analytics.total_users}</Text>
            <Text style={styles.analyticsLabel}>Total Users</Text>
          </View>
          <View style={styles.analyticsItem}>
            <Text style={styles.analyticsValue}>${analytics.revenue_estimate?.toFixed(2)}</Text>
            <Text style={styles.analyticsLabel}>Monthly Revenue</Text>
          </View>
        </View>
        
        <Text style={styles.subsectionTitle}>Tier Distribution</Text>
        {Object.entries(analytics.tier_distribution || {}).map(([tier, count]) => (
          <View key={tier} style={styles.tierRow}>
            <Text style={styles.tierName}>{tier}</Text>
            <Text style={styles.tierCount}>{count as number} users</Text>
          </View>
        ))}
      </NintendoCard>
    );
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading admin panel...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Premium Management</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {renderAnalytics()}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Feature Management</Text>
          <Text style={styles.sectionDescription}>
            Control which features are available and their premium status
          </Text>
        </View>

        {features.map(renderFeatureCard)}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActions}>
            <NintendoButton
              title="Make All Free"
              onPress={() => {
                Alert.alert(
                  'Make All Features Free',
                  'This will make all premium features free for all users. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Continue',
                      style: 'destructive',
                      onPress: async () => {
                        try {
                                                  for (const feature of ADVANCED_FEATURES) {
                          await updateAdminMonetizationConfig(feature.id, {
                              is_premium: false,
                              tier_requirement: 'free',
                            });
                          }
                          await loadData();
                          Alert.alert('Success', 'All features are now free');
                        } catch (error) {
                          Alert.alert('Error', 'Failed to update features');
                        }
                      },
                    },
                  ]
                );
              }}
              style={styles.quickActionButton}
            />
            <NintendoButton
              title="Reset All to Default"
              onPress={() => {
                Alert.alert(
                  'Reset All Features',
                  'This will reset all features to their default settings. Continue?',
                  [
                    { text: 'Cancel', style: 'cancel' },
                    {
                      text: 'Continue',
                      style: 'destructive',
                      onPress: async () => {
                        try {
                          for (const feature of features) {
                            await resetFeatureToDefault(feature.id);
                          }
                          await loadData();
                          Alert.alert('Success', 'All features reset to default');
                        } catch (error) {
                          Alert.alert('Error', 'Failed to reset features');
                        }
                      },
                    },
                  ]
                );
              }}
              style={styles.quickActionButton}
            />
          </View>
        </View>
      </ScrollView>

      {/* Edit Modal */}
      <Modal
        visible={editModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity
              style={styles.modalCloseButton}
              onPress={() => setEditModalVisible(false)}
            >
              <Text style={styles.modalCloseText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>
              Edit {editingFeature?.name}
            </Text>
            <TouchableOpacity
              style={styles.modalSaveButton}
              onPress={handleSaveEdit}
            >
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Basic Settings</Text>
              
              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Enabled</Text>
                <Switch
                  value={editForm.is_enabled}
                  onValueChange={(value) => setEditForm({ ...editForm, is_enabled: value })}
                />
              </View>

              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Premium</Text>
                <Switch
                  value={editForm.is_premium}
                  onValueChange={(value) => setEditForm({ ...editForm, is_premium: value })}
                />
              </View>

              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Tier Requirement</Text>
                <View style={styles.pickerContainer}>
                  {['free', 'basic', 'premium', 'enterprise'].map((tier) => (
                    <TouchableOpacity
                      key={tier}
                      style={[
                        styles.tierOption,
                        editForm.tier_requirement === tier && styles.tierOptionSelected,
                      ]}
                      onPress={() => setEditForm({ ...editForm, tier_requirement: tier })}
                    >
                      <Text style={[
                        styles.tierOptionText,
                        editForm.tier_requirement === tier && styles.tierOptionTextSelected,
                      ]}>
                        {tier.toUpperCase()}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
            </View>

            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Usage Limits</Text>
              
              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Daily Limit</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.daily_limit}
                  onChangeText={(text) => setEditForm({ ...editForm, daily_limit: text })}
                  placeholder="Unlimited"
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Monthly Limit</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.monthly_limit}
                  onChangeText={(text) => setEditForm({ ...editForm, monthly_limit: text })}
                  placeholder="Unlimited"
                  keyboardType="numeric"
                />
              </View>

              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Total Limit</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.total_limit}
                  onChangeText={(text) => setEditForm({ ...editForm, total_limit: text })}
                  placeholder="Unlimited"
                  keyboardType="numeric"
                />
              </View>
            </View>

            <View style={styles.formSection}>
              <Text style={styles.formSectionTitle}>Pricing</Text>
              
              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Monthly Price ($)</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.monthly_price}
                  onChangeText={(text) => setEditForm({ ...editForm, monthly_price: text })}
                  placeholder="0.00"
                  keyboardType="decimal-pad"
                />
              </View>

              <View style={styles.formRow}>
                <Text style={styles.formLabel}>Yearly Price ($)</Text>
                <TextInput
                  style={styles.textInput}
                  value={editForm.yearly_price}
                  onChangeText={(text) => setEditForm({ ...editForm, yearly_price: text })}
                  placeholder="0.00"
                  keyboardType="decimal-pad"
                />
              </View>
            </View>
          </ScrollView>
        </SafeAreaView>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    flex: 1,
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#333',
  },
  headerSpacer: {
    width: 34,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  sectionDescription: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
  },
  analyticsCard: {
    marginBottom: 30,
  },
  analyticsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  analyticsItem: {
    alignItems: 'center',
  },
  analyticsValue: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  analyticsLabel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  subsectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  tierRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tierName: {
    fontSize: 16,
    color: '#333',
    textTransform: 'capitalize',
  },
  tierCount: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
  featureCard: {
    marginBottom: 15,
  },
  featureHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  featureInfo: {
    flex: 1,
  },
  featureName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  featureDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 20,
  },
  featureMeta: {
    flexDirection: 'row',
    gap: 10,
  },
  tierBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  tierfree: { backgroundColor: '#E8F5E8' },
  tierbasic: { backgroundColor: '#FFF3E0' },
  tierpremium: { backgroundColor: '#E3F2FD' },
  tierenterprise: { backgroundColor: '#F3E5F5' },
  tierText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  categoryBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  categoryai_generation: { backgroundColor: '#E1F5FE' },
  categoryanalytics: { backgroundColor: '#F3E5F5' },
  categorycustomization: { backgroundColor: '#E8F5E8' },
  categoryadvanced_features: { backgroundColor: '#FFF3E0' },
  categoryText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#333',
  },
  editButton: {
    padding: 8,
  },
  featureControls: {
    gap: 15,
  },
  controlRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  controlLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  resetButton: {
    alignSelf: 'flex-start',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f44336',
    borderRadius: 6,
  },
  resetButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  quickActions: {
    flexDirection: 'row',
    gap: 15,
  },
  quickActionButton: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#fff',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalCloseText: {
    fontSize: 16,
    color: '#666',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  modalSaveButton: {
    padding: 5,
  },
  modalSaveText: {
    fontSize: 16,
    color: '#4CAF50',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formSection: {
    marginBottom: 30,
  },
  formSectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginBottom: 15,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  formLabel: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  pickerContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  tierOption: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
  },
  tierOptionSelected: {
    backgroundColor: '#4CAF50',
  },
  tierOptionText: {
    fontSize: 12,
    color: '#666',
    fontWeight: '500',
  },
  tierOptionTextSelected: {
    color: '#fff',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    minWidth: 100,
    textAlign: 'right',
  },
}); 