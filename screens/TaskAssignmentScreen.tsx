import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar 
} from '../components/ui';
import { supabase } from '../utils/supabase';
import { useChildren, useTaskTemplates, useFamilySettings } from '../hooks';
import { 
  validateTaskAssignment, 
  TaskAssignmentData 
} from '../utils/taskValidation';
import TaskTemplateScreen from './TaskTemplateScreen';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface TaskAssignmentScreenProps {
  onBack: () => void;
  onTaskAssigned?: (assignedTasks: any[]) => void;
}

interface TaskTemplate {
  id: string;
  title: string;
  description: string;
  value: number;
  category: string;
  frequency: string;
  created_at: string;
}

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
}

const TaskAssignmentScreen: React.FC<TaskAssignmentScreenProps> = ({ onBack, onTaskAssigned }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // SIMPLIFIED HOOKS: Direct Supabase calls for optimal performance
  const { taskTemplates, loading: loadingTemplates } = useTaskTemplates();
  const { children, loading: loadingChildren } = useChildren();
  const { familySettings } = useFamilySettings();
  
  // State
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [selectedChildren, setSelectedChildren] = useState<string[]>([]);
  const loading = loadingTemplates || loadingChildren;
  const [assigning, setAssigning] = useState(false);
  const [showTaskTemplates, setShowTaskTemplates] = useState(false);
  const verificationMode = familySettings?.task_verification_mode || 'strict';
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;

  // Simple task assignment function
  const assignTask = async (assignmentData: TaskAssignmentData) => {
    try {
      const { data, error } = await supabase
        .from('tasks')
        .insert({
          parent_id: user?.id,
          ...assignmentData
        })
        .select();
      
      if (error) throw error;
      
      playSound('achievement_unlock');
      onTaskAssigned?.(data);
      
      return data;
    } catch (error) {
      console.error('Error assigning tasks:', error);
      Alert.alert('Error', 'Failed to assign tasks. Please try again.');
      throw error;
    }
  };

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // React Query handles all data loading automatically!

  const handleTemplateSelect = (template: TaskTemplate) => {
    playSound('card_flip');
    setSelectedTemplate(template);
    setSelectedChildren([]); // Reset child selection when changing template
  };

  const handleChildToggle = (childId: string) => {
    playSound('selection_change');
    setSelectedChildren(prev => {
      if (prev.includes(childId)) {
        return prev.filter(id => id !== childId);
      } else {
        return [...prev, childId];
      }
    });
  };

  const handleManageTemplates = () => {
    playSound('button_press');
    setShowTaskTemplates(true);
  };

  const handleAssignTasks = async () => {
    if (!selectedTemplate) {
      Alert.alert('No Template Selected', 'Please select a task template first.');
      return;
    }

    if (selectedChildren.length === 0) {
      Alert.alert('No Children Selected', 'Please select at least one child to assign the task to.');
      return;
    }

    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    setAssigning(true);
    playSound('button_press');

    try {
      // Validate task assignment data for all children
      for (const childId of selectedChildren) {
        const assignmentData: TaskAssignmentData = {
          child_id: childId,
          parent_id: user.id,
          template_id: selectedTemplate.id,
          title: selectedTemplate.title,
          description: selectedTemplate.description,
          value: selectedTemplate.value,
        };

        const validation = await validateTaskAssignment(assignmentData);
        if (!validation.isValid) {
          Alert.alert('Validation Error', validation.errors.join('\n'));
          setAssigning(false);
          return;
        }
      }

      // Assign tasks to all selected children using mutation
      for (const childId of selectedChildren) {
        const assignmentData: TaskAssignmentData = {
          child_id: childId,
          parent_id: user.id,
          template_id: selectedTemplate.id,
          title: selectedTemplate.title,
          description: selectedTemplate.description,
          value: selectedTemplate.value,
        };

        await assignTask(assignmentData);
      }
      
      const childNames = selectedChildren
        .map(id => children.find(c => c.id === id)?.name)
        .join(', ');

      Alert.alert(
        'Tasks Assigned! 🎉',
        `"${selectedTemplate.title}" has been assigned to ${childNames}.`,
        [
          {
            text: 'Assign Another',
            onPress: () => {
              setSelectedTemplate(null);
              setSelectedChildren([]);
            },
          },
          {
            text: 'Done',
            onPress: () => {
              onBack();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error assigning tasks:', error);
      Alert.alert('Error', 'Failed to assign tasks. Please try again.');
    } finally {
      setAssigning(false);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFill}
        />
        <Text style={styles.loadingText}>Loading Tasks...</Text>
      </View>
    );
  }

  // Show Task Templates Screen
  if (showTaskTemplates) {
    return (
      <TaskTemplateScreen
        onBack={() => setShowTaskTemplates(false)}
        onTemplateSelected={(template) => {
          setShowTaskTemplates(false);
          setSelectedTemplate(template);
        }}
        selectionMode={true}
      />
    );
  }

  return (
    <View style={styles.container}>
      {/* Background Gradient */}
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={StyleSheet.absoluteFill}
      />

      {/* Header */}
      <Animated.View 
        style={[
          styles.header,
          {
            opacity: fadeInAnim,
            transform: [{ translateY: slideInAnim }]
          }
        ]}
      >
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Assign Tasks</Text>
        <View style={styles.headerSpacer} />
      </Animated.View>

      {/* Main Content */}
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Verification Mode Info */}
        <Animated.View style={[styles.infoSection, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.infoCard}>
            <Text style={styles.infoTitle}>
              {verificationMode === 'strict' ? '🔒 Strict Mode' : '🤝 Trusting Mode'}
            </Text>
            <Text style={styles.infoText}>
              {verificationMode === 'strict' 
                ? 'Tasks require parent approval or verification code before completion.'
                : 'Children can mark tasks as complete independently.'}
            </Text>
          </NintendoCard>
        </Animated.View>

        {/* Step 1: Select Task Template */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>📋 Step 1: Choose Task Template</Text>
            <TouchableOpacity onPress={handleManageTemplates} style={styles.manageButton}>
              <Text style={styles.manageButtonText}>Manage</Text>
            </TouchableOpacity>
          </View>
          
          {taskTemplates.length === 0 ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <Text style={styles.emptyText}>
                No task templates found. Create some task templates first!
              </Text>
            </NintendoCard>
          ) : (
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.templatesScroll}>
              {taskTemplates.map((template) => (
                <TouchableOpacity
                  key={template.id}
                  onPress={() => handleTemplateSelect(template)}
                  style={styles.templateCard}
                >
                  <NintendoCard 
                    variant={selectedTemplate?.id === template.id ? "elevated" : "default"} 
                    withShadow={selectedTemplate?.id === template.id}
                                         style={selectedTemplate?.id === template.id 
                       ? { ...styles.templateCardInner, ...styles.templateCardSelected }
                       : styles.templateCardInner
                     }
                  >
                    <Text style={styles.templateTitle}>{template.title}</Text>
                    <Text style={styles.templateCoins}>{template.value} 🪙</Text>
                    <Text style={styles.templateCategory}>{template.category}</Text>
                    <Text style={styles.templateFrequency}>{template.frequency}</Text>
                    {selectedTemplate?.id === template.id && (
                      <Text style={styles.selectedIndicator}>✓ Selected</Text>
                    )}
                  </NintendoCard>
                </TouchableOpacity>
              ))}
            </ScrollView>
          )}
        </Animated.View>

        {/* Step 2: Select Children (only show if template is selected) */}
        {selectedTemplate && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>👶 Step 2: Choose Children</Text>
            
            {children.length === 0 ? (
              <NintendoCard variant="default" style={styles.emptyCard}>
                <Text style={styles.emptyText}>
                  No children found. Add children to your family first!
                </Text>
              </NintendoCard>
            ) : (
              <View style={styles.childrenGrid}>
                {children.map((child) => (
                  <TouchableOpacity
                    key={child.id}
                    onPress={() => handleChildToggle(child.id)}
                    style={styles.childCard}
                  >
                    <NintendoCard 
                      variant={selectedChildren.includes(child.id) ? "elevated" : "default"}
                      withShadow={selectedChildren.includes(child.id)}
                                             style={selectedChildren.includes(child.id)
                         ? { ...styles.childCardInner, ...styles.childCardSelected }
                         : styles.childCardInner
                       }
                    >
                      <Text style={styles.childAvatar}>{child.avatar}</Text>
                      <Text style={styles.childName}>{child.name}</Text>
                      <Text style={styles.childCoins}>{child.coin_balance} 🪙</Text>
                      <Text style={styles.childLevel}>Level {child.level}</Text>
                      {selectedChildren.includes(child.id) && (
                        <Text style={styles.selectedIndicator}>✓ Selected</Text>
                      )}
                    </NintendoCard>
                  </TouchableOpacity>
                ))}
              </View>
            )}
          </Animated.View>
        )}

        {/* Task Summary & Assign Button */}
        {selectedTemplate && selectedChildren.length > 0 && (
          <Animated.View style={[styles.summarySection, { opacity: fadeInAnim }]}>
            <NintendoCard variant="elevated" withShadow style={styles.summaryCard}>
              <Text style={styles.summaryTitle}>📝 Assignment Summary</Text>
              
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Task:</Text>
                <Text style={styles.summaryValue}>{selectedTemplate.title}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Coin Value:</Text>
                <Text style={styles.summaryValue}>{selectedTemplate.value} 🪙</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Frequency:</Text>
                <Text style={styles.summaryValue}>{selectedTemplate.frequency}</Text>
              </View>
              
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Assigned to:</Text>
                <Text style={styles.summaryValue}>
                  {selectedChildren.map(id => children.find(c => c.id === id)?.name).join(', ')}
                </Text>
              </View>
            </NintendoCard>

            <NintendoButton
              title={assigning ? 'Assigning...' : '🚀 Assign Tasks'}
              onPress={handleAssignTasks}
              variant="success"
              disabled={assigning}
              style={styles.assignButton}
            />
          </Animated.View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  infoSection: {
    marginBottom: 20,
  },
  infoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  manageButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  manageButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  emptyCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  templatesScroll: {
    flexDirection: 'row',
  },
  templateCard: {
    marginRight: 15,
    width: 160,
  },
  templateCardInner: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 15,
    minHeight: 140,
  },
  templateCardSelected: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  templateTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  templateCoins: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  templateCategory: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  templateFrequency: {
    fontSize: 12,
    color: '#888',
    marginBottom: 8,
  },
  selectedIndicator: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  childrenGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  childCard: {
    width: '48%',
    marginBottom: 15,
  },
  childCardInner: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 15,
    alignItems: 'center',
    minHeight: 120,
  },
  childCardSelected: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderColor: '#4CAF50',
    borderWidth: 2,
  },
  childAvatar: {
    fontSize: 30,
    marginBottom: 8,
  },
  childName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  childCoins: {
    fontSize: 12,
    color: '#666',
    marginBottom: 4,
  },
  childLevel: {
    fontSize: 12,
    color: '#888',
    marginBottom: 8,
  },
  summarySection: {
    marginBottom: 20,
  },
  summaryCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
    marginBottom: 20,
  },
  summaryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
    flex: 1,
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    flex: 2,
    textAlign: 'right',
  },
  assignButton: {
    minHeight: 50,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default TaskAssignmentScreen; 