import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { useTutorial } from '../contexts/TutorialContext';
import { GuidedTutorialOverlay } from '../components/GuidedTutorialOverlay';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoInput,
  SuggestionCard 
} from '../components/ui';
// Removed old API imports - using direct Supabase calls instead
import { useChildren } from '../hooks';
import { 
  validateReward, 
  RewardData,
  REWARD_CATEGORIES 
} from '../utils/rewardValidation';
import { 
  generateAIRewards,
  generatePersonalizedRewards,
  isOpenAIConfigured,
  withAIErrorHandling,
  REWARD_CATEGORIES as AI_REWARD_CATEGORIES,
  AGE_GROUPS
} from '../utils/openaiConfig';
import { 
  getRewardsByAgeGroup,
  getRewardsByAge,
  getRandomRewards
} from '../utils/rewardLibrary';
import { checkFeatureAccess, recordFeatureUsage } from '../utils/unifiedMonetization';
import { supabase } from '../utils/supabase';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface RewardCreationScreenProps {
  onBack: () => void;
  onRewardCreated?: (reward: any) => void;
}

interface RewardSuggestion {
  title: string;
  description: string;
  cost: number;
  category: string;
  ageRange: string;
}

// Pre-populated reward suggestions
const REWARD_SUGGESTIONS: RewardSuggestion[] = [
  // Small Treats (5-15 coins)
  { title: 'Extra Bedtime Story', description: 'Choose an extra bedtime story', cost: 8, category: 'Treats', ageRange: '3-8' },
  { title: 'Favorite Snack', description: 'Pick your favorite snack for today', cost: 10, category: 'Treats', ageRange: '3-12' },
  { title: 'Stay Up 15 Minutes', description: 'Stay up 15 minutes past bedtime', cost: 12, category: 'Privileges', ageRange: '5-15' },
  { title: 'Pick Family Movie', description: 'Choose tonight\'s family movie', cost: 15, category: 'Activities', ageRange: '6-18' },
  
  // Medium Rewards (20-50 coins)
  { title: 'Friend Play Date', description: 'Invite a friend over to play', cost: 25, category: 'Activities', ageRange: '5-15' },
  { title: '1 Hour Screen Time', description: 'Extra hour of screen time', cost: 30, category: 'Screen Time', ageRange: '6-18' },
  { title: 'Choose Family Dinner', description: 'Pick what the family has for dinner', cost: 35, category: 'Privileges', ageRange: '8-18' },
  { title: 'Small Toy/Game', description: 'Pick a small toy or game ($10-20)', cost: 40, category: 'Toys', ageRange: '3-12' },
  { title: 'Art Supplies', description: 'New art supplies for creative projects', cost: 45, category: 'Crafts', ageRange: '4-16' },
  
  // Big Rewards (60-150 coins)
  { title: 'Day Out Adventure', description: 'Special day out to chosen location', cost: 80, category: 'Outings', ageRange: '5-18' },
  { title: 'Video Game/App', description: 'New video game or app purchase', cost: 100, category: 'Games', ageRange: '8-18' },
  { title: 'New Book Series', description: 'Start a new book series', cost: 60, category: 'Books', ageRange: '6-18' },
  { title: 'Sports Equipment', description: 'New sports gear or equipment', cost: 120, category: 'Sports', ageRange: '8-18' },
  { title: 'Experience Day', description: 'Special experience like mini golf, bowling', cost: 150, category: 'Experiences', ageRange: '8-25' },
  
  // Premium Rewards (200+ coins)
  { title: 'Concert/Event Tickets', description: 'Tickets to a concert or special event', cost: 250, category: 'Experiences', ageRange: '12-25' },
  { title: 'Weekend Trip', description: 'Weekend getaway or camping trip', cost: 300, category: 'Outings', ageRange: '10-25' },
  { title: 'Big Purchase', description: 'Major item like bike, tablet, etc.', cost: 500, category: 'Toys', ageRange: '8-25' },
  { title: 'Cash Reward', description: 'Direct cash payment for older kids', cost: 200, category: 'Money', ageRange: '16-25' },
];

const RewardCreationScreen: React.FC<RewardCreationScreenProps> = ({ onBack, onRewardCreated }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  const { 
    tutorialState, 
    currentStep, 
    nextStep, 
    completeTutorial, 
    setPersisted,
    isPersisted 
  } = useTutorial();
  
  // Form state
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [coinCost, setCoinCost] = useState('25');
  const [category, setCategory] = useState('');
  const [loading, setLoading] = useState(false);
  
  // UI state
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  // Assignment state
  const [selectedChildren, setSelectedChildren] = useState<string[]>([]);
  const [isFamilyGoal, setIsFamilyGoal] = useState(false);
  const [familyGoalTarget, setFamilyGoalTarget] = useState('100');
  const [familyGoalTimeframe, setFamilyGoalTimeframe] = useState('daily');
  
  // AI Generation state
  const [showAIGeneration, setShowAIGeneration] = useState(false);
  const [aiGenerating, setAIGenerating] = useState(false);
  const [aiSuggestions, setAISuggestions] = useState<any[]>([]);
  const [selectedChild, setSelectedChild] = useState<any>(null);
  const [interests, setInterests] = useState('');
  const [budgetRange, setBudgetRange] = useState({ min: 10, max: 100 });
  // REACT QUERY: Fast direct children loading (3-5x faster!)
  const { children } = useChildren();
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  useEffect(() => {
    // If tutorial was persisted, continue showing it on this screen
    return () => {
      // When leaving this screen, turn off persistence
      if (isPersisted) {
        setPersisted(false);
      }
    };
  }, [isPersisted, setPersisted]);

  // Filter suggestions based on category
  const filteredSuggestions = REWARD_SUGGESTIONS.filter(suggestion => {
    if (selectedCategory === 'all') return true;
    return suggestion.category === selectedCategory;
  });

  const handleSuggestionPress = (suggestion: RewardSuggestion) => {
    handleUseSuggestion(suggestion);
  };

  const handleCreateReward = async () => {
    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    // Validate reward data
    const rewardData: RewardData = {
      parent_id: user.id,
      title: title.trim(),
      description: description.trim(),
      cost: parseInt(coinCost) || 25,
      category: category || 'General',
    };

    const validation = validateReward(rewardData);
    if (!validation.isValid) {
      Alert.alert('Validation Error', validation.errors.join('\n'));
      return;
    }

    setLoading(true);
    playSound('button_press');

    try {
      // Create the reward
      const { data: reward, error: rewardError } = await supabase
        .from('rewards')
        .insert({
          parent_id: user?.id,
          title: rewardData.title,
          description: rewardData.description,
          cost: rewardData.cost,
          category: rewardData.category,
          is_family_goal: isFamilyGoal,
          family_goal_target: isFamilyGoal ? parseInt(familyGoalTarget) : null,
          family_goal_timeframe: isFamilyGoal ? familyGoalTimeframe : null
        })
        .select()
        .single();
      
      if (rewardError) throw rewardError;

      // If individual reward with selected children, create assignments
      if (!isFamilyGoal && selectedChildren.length > 0) {
        const assignments = selectedChildren.map(childId => ({
          reward_id: reward.id,
          child_id: childId,
          parent_id: user.id,
          is_available: true
        }));

        const { error: assignError } = await supabase
          .from('reward_assignments')
          .insert(assignments);

        if (assignError) {
          console.error('Assignment error:', assignError);
          // Don't fail the whole operation if assignment fails
        }
      }

      playSound('achievement_unlock');
      
      const assignmentText = isFamilyGoal 
        ? ` as a family goal (${familyGoalTarget} coins in ${familyGoalTimeframe})`
        : selectedChildren.length > 0 
          ? ` and assigned to ${selectedChildren.length} child${selectedChildren.length > 1 ? 'ren' : ''}`
          : '';
          
      Alert.alert(
        'Reward Created! 🎉',
        `"${title}" has been created${assignmentText}!`,
        [
          {
            text: 'Create Another',
            onPress: () => {
              setTitle('');
              setDescription('');
              setCoinCost('25');
              setCategory('');
              setSelectedChildren([]);
              setIsFamilyGoal(false);
              setFamilyGoalTarget('100');
              setFamilyGoalTimeframe('daily');
              setShowSuggestions(true);
            },
          },
          {
            text: 'Done',
            onPress: () => {
              onRewardCreated?.(reward);
              onBack();
            },
          },
        ]
      );
    } catch (error) {
      console.error('Unexpected error:', error);
      Alert.alert('Error', 'An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Load children data
  // React Query automatically loads children!

  // AI Generation functions
  const handleAIGeneration = async () => {
    if (!user) return;

    // Check premium access
    const accessCheck = await checkFeatureAccess(user.id, 'ai_reward_generation');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        'Premium Feature',
        accessCheck.reason || 'AI reward generation is a premium feature. Please upgrade your subscription.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (!isOpenAIConfigured()) {
      Alert.alert(
        'AI Features Unavailable',
        'AI reward generation requires an OpenAI API key. Please configure your API key in the settings.',
        [{ text: 'OK' }]
      );
      return;
    }

    setAIGenerating(true);
    setShowAIGeneration(true);
    
    try {
      const childAge = selectedChild ? calculateAge(selectedChild.created_at) : 10;
      const interestsList = interests ? interests.split(',').map(i => i.trim()) : [];
      const categories = selectedCategory !== 'all' ? [selectedCategory] : [];
      
      const aiRewards = await withAIErrorHandling(
        () => generateAIRewards({
          age: childAge,
          child_name: selectedChild?.name,
          interests: interestsList,
          categories,
          budget_range: budgetRange,
          count: 5,
          family_context: `Family with ${children.length} children`,
        }),
        []
      );

      if (aiRewards && aiRewards.length > 0) {
        setAISuggestions(aiRewards);
        playSound('achievement_unlock');
        // Record feature usage
        await recordFeatureUsage(user.id, 'ai_reward_generation');
      } else {
        Alert.alert('No Suggestions', 'Unable to generate AI suggestions. Try adjusting your criteria.');
      }
    } catch (error) {
      console.error('AI Generation Error:', error);
      Alert.alert('Error', 'Failed to generate AI suggestions. Please try again.');
    } finally {
      setAIGenerating(false);
    }
  };

  const handlePersonalizedGeneration = async () => {
    if (!user) return;

    // Check premium access
    const accessCheck = await checkFeatureAccess(user.id, 'ai_personalized_suggestions');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        'Premium Feature',
        accessCheck.reason || 'Personalized AI suggestions is a premium feature. Please upgrade your subscription.',
        [{ text: 'OK' }]
      );
      return;
    }

    if (!selectedChild) {
      Alert.alert('Select Child', 'Please select a child for personalized suggestions.');
      return;
    }

    setAIGenerating(true);
    setShowAIGeneration(true);
    
    try {
      const personalizedRewards = await withAIErrorHandling(
        () => generatePersonalizedRewards(
          {
            name: selectedChild.name,
            age: calculateAge(selectedChild.created_at),
            coin_balance: selectedChild.coin_balance,
          },
          {
            interests: interests ? interests.split(',').map(i => i.trim()) : [],
            family_context: `Family with ${children.length} children`,
          }
        ),
        []
      );

      if (personalizedRewards && personalizedRewards.length > 0) {
        setAISuggestions(personalizedRewards);
        playSound('achievement_unlock');
        // Record feature usage
        await recordFeatureUsage(user.id, 'ai_personalized_suggestions');
      } else {
        Alert.alert('No Suggestions', 'Unable to generate personalized suggestions.');
      }
    } catch (error) {
      console.error('Personalized Generation Error:', error);
      Alert.alert('Error', 'Failed to generate personalized suggestions.');
    } finally {
      setAIGenerating(false);
    }
  };

  const handleLoadLibraryRewards = async () => {
    if (!user) return;

    // Check premium access for library access
    const accessCheck = await checkFeatureAccess(user.id, 'reward_library');
    if (!accessCheck.hasAccess) {
      Alert.alert(
        'Premium Feature',
        accessCheck.reason || 'Reward library access is limited. Please upgrade your subscription.',
        [{ text: 'OK' }]
      );
      return;
    }

    const childAge = selectedChild ? calculateAge(selectedChild.created_at) : 10;
    const libraryRewards = getRewardsByAge(childAge);
    
    // Convert library rewards to suggestion format
    const suggestions = libraryRewards.slice(0, 8).map(reward => ({
      id: reward.id,
      title: reward.title,
      description: reward.description,
      cost: reward.cost,
      category: reward.category,
      ageRange: reward.age_group,
      availability: reward.availability,
      notes: reward.notes,
      isLibraryReward: true,
    }));

    setAISuggestions(suggestions);
    setShowAIGeneration(true);
    playSound('button_press');
    // Record feature usage
    await recordFeatureUsage(user.id, 'reward_library');
  };

  const calculateAge = (createdAt: string): number => {
    const now = new Date();
    const created = new Date(createdAt);
    const ageInMs = now.getTime() - created.getTime();
    const ageInYears = Math.floor(ageInMs / (1000 * 60 * 60 * 24 * 365));
    return Math.max(3, ageInYears + 7); // Assume child was created when they were 7
  };

  const handleUseSuggestion = (suggestion: any) => {
    setTitle(suggestion.title);
    setDescription(suggestion.description);
    setCoinCost(suggestion.cost?.toString() || '25');
    setCategory(suggestion.category);
    setShowSuggestions(false);
    setShowAIGeneration(false);
    playSound('button_press');
  };

  // Tutorial navigation handler
  const handleTutorialNavigation = (targetScreen: string) => {
    // For reward creation screen, we don't need to navigate anywhere
    // Just mark the reward as created when the user creates a reward
    if (targetScreen === 'RewardCreation') {
      // The tutorial will be handled by the reward creation success
    }
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Create Reward</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Form Section */}
        <Animated.View style={[styles.formSection, { 
          opacity: fadeInAnim,
          transform: [{ translateY: slideInAnim }]
        }]}>
          <NintendoCard variant="elevated" withShadow style={styles.formCard}>
            <Text style={styles.sectionTitle}>🎁 Reward Details</Text>
            
            <NintendoInput
              label="Reward Title"
              value={title}
              onChangeText={setTitle}
              placeholder="Enter reward name"
              style={styles.input}
            />
            
            <NintendoInput
              label="Description"
              value={description}
              onChangeText={setDescription}
              placeholder="What does this reward include?"
              multiline
              numberOfLines={3}
              style={styles.input}
            />
            
            <View style={styles.formRow}>
              <View style={styles.formHalf}>
                <Text style={styles.inputLabel}>Cost (1-1000 coins)</Text>
                <NintendoInput
                  value={coinCost}
                  onChangeText={(text) => {
                    // Only allow numbers and limit to 4 digits
                    const numericValue = text.replace(/[^0-9]/g, '');
                    if (numericValue.length <= 4) {
                      setCoinCost(numericValue);
                    }
                  }}
                  placeholder="25"
                  keyboardType="numeric"
                  style={styles.input}
                />
              </View>
              
              <View style={styles.formHalf}>
                <Text style={styles.inputLabel}>Category</Text>
                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionScroll}>
                  {REWARD_CATEGORIES.map((cat) => (
                    <TouchableOpacity
                      key={cat}
                      style={[
                        styles.optionButton,
                        category === cat && styles.optionButtonActive
                      ]}
                      onPress={() => {
                        setCategory(cat);
                        playSound('selection_change');
                      }}
                    >
                      <Text style={[
                        styles.optionButtonText,
                        category === cat && styles.optionButtonTextActive
                      ]}>
                        {cat}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </ScrollView>
              </View>
            </View>

            {/* Assignment Section */}
            {children && children.length > 0 && (
              <View style={styles.formRow}>
                <Text style={styles.inputLabel}>🎯 Assignment</Text>
                
                {/* Family Goal Toggle */}
                <TouchableOpacity
                  style={[styles.toggleOption, isFamilyGoal && styles.toggleOptionActive]}
                  onPress={() => {
                    setIsFamilyGoal(!isFamilyGoal);
                    if (!isFamilyGoal) {
                      setSelectedChildren([]); // Clear individual selections when switching to family goal
                    }
                    playSound('selection_change');
                  }}
                >
                  <Text style={[styles.toggleOptionText, isFamilyGoal && styles.toggleOptionTextActive]}>
                    {isFamilyGoal ? '👨‍👩‍👧‍👦 Family Goal' : '👤 Individual Reward'}
                  </Text>
                </TouchableOpacity>

                {isFamilyGoal ? (
                  /* Family Goal Settings */
                  <View style={styles.familyGoalSection}>
                    <Text style={styles.inputHint}>
                      All children work together to reach the goal
                    </Text>
                    <View style={styles.formRow}>
                      <View style={styles.formHalf}>
                        <Text style={styles.inputLabel}>Target Coins</Text>
                        <NintendoInput
                          value={familyGoalTarget}
                          onChangeText={setFamilyGoalTarget}
                          placeholder="100"
                          keyboardType="numeric"
                          style={styles.input}
                        />
                      </View>
                      <View style={styles.formHalf}>
                        <Text style={styles.inputLabel}>Timeframe</Text>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.optionScroll}>
                          {['daily', 'weekly', 'monthly'].map((timeframe) => (
                            <TouchableOpacity
                              key={timeframe}
                              style={[
                                styles.optionButton,
                                familyGoalTimeframe === timeframe && styles.optionButtonActive
                              ]}
                              onPress={() => {
                                setFamilyGoalTimeframe(timeframe);
                                playSound('selection_change');
                              }}
                            >
                              <Text style={[
                                styles.optionButtonText,
                                familyGoalTimeframe === timeframe && styles.optionButtonTextActive
                              ]}>
                                {timeframe}
                              </Text>
                            </TouchableOpacity>
                          ))}
                        </ScrollView>
                      </View>
                    </View>
                  </View>
                ) : (
                  /* Individual Child Selection */
                  <View>
                    <Text style={styles.inputHint}>
                      {children.length === 1 
                        ? `Assign to ${children[0].name}` 
                        : `Optional: Select children (${selectedChildren.length} selected)`
                      }
                    </Text>
                    <View style={styles.childrenGrid}>
                      {children.map((child) => {
                        const isSelected = selectedChildren.includes(child.id);
                        return (
                          <TouchableOpacity
                            key={child.id}
                            style={[
                              styles.childButton,
                              isSelected && styles.childButtonActive
                            ]}
                            onPress={() => {
                              const newSelected = isSelected
                                ? selectedChildren.filter(id => id !== child.id)
                                : [...selectedChildren, child.id];
                              setSelectedChildren(newSelected);
                              playSound('selection_change');
                            }}
                          >
                            <Text style={styles.childButtonEmoji}>
                              {child.avatar || '👶'}
                            </Text>
                            <Text style={[
                              styles.childButtonText,
                              isSelected && styles.childButtonTextActive
                            ]}>
                              {child.name}
                            </Text>
                            <Text style={styles.childButtonCoins}>
                              {child.coin_balance || 0} 🪙
                            </Text>
                          </TouchableOpacity>
                        );
                      })}
                    </View>
                  </View>
                )}
              </View>
            )}

            <TouchableOpacity
              style={styles.toggleButton}
              onPress={() => {
                setShowSuggestions(!showSuggestions);
                playSound('selection_change');
              }}
            >
              <Text style={styles.toggleButtonText}>
                {showSuggestions ? '📝 Hide Suggestions' : '💡 Show Suggestions'}
              </Text>
            </TouchableOpacity>
          </NintendoCard>
        </Animated.View>

        {/* AI Generation Section */}
        <Animated.View style={[styles.aiSection, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.aiCard}>
            <Text style={styles.sectionTitle}>🤖 AI Reward Generation</Text>
            
            {/* Child Selection */}
            <View style={styles.childSelectionRow}>
              <Text style={styles.inputLabel}>Target Child (Optional):</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.childScroll}>
                <TouchableOpacity
                  style={[
                    styles.childButton,
                    !selectedChild && styles.childButtonActive
                  ]}
                  onPress={() => {
                    setSelectedChild(null);
                    playSound('selection_change');
                  }}
                >
                  <Text style={[
                    styles.childButtonText,
                    !selectedChild && styles.childButtonTextActive
                  ]}>
                    Any Child
                  </Text>
                </TouchableOpacity>
                {children.map((child) => (
                  <TouchableOpacity
                    key={child.id}
                    style={[
                      styles.childButton,
                      selectedChild?.id === child.id && styles.childButtonActive
                    ]}
                    onPress={() => {
                      setSelectedChild(child);
                      playSound('selection_change');
                    }}
                  >
                    <Text style={[
                      styles.childButtonText,
                      selectedChild?.id === child.id && styles.childButtonTextActive
                    ]}>
                      {child.avatar} {child.name}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Interests Input */}
            <NintendoInput
              value={interests}
              onChangeText={setInterests}
              placeholder="Child's interests (comma-separated, e.g., 'art, sports, reading')"
              style={styles.input}
            />

            {/* Budget Range */}
            <View style={styles.budgetRow}>
              <Text style={styles.inputLabel}>Budget Range:</Text>
              <View style={styles.budgetInputs}>
                <View style={styles.budgetInput}>
                  <Text style={styles.budgetLabel}>Min:</Text>
                  <NintendoInput
                    value={budgetRange.min.toString()}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 0;
                      setBudgetRange(prev => ({ ...prev, min: value }));
                    }}
                    placeholder="10"
                    keyboardType="numeric"
                    style={styles.budgetField}
                  />
                </View>
                <View style={styles.budgetInput}>
                  <Text style={styles.budgetLabel}>Max:</Text>
                  <NintendoInput
                    value={budgetRange.max.toString()}
                    onChangeText={(text) => {
                      const value = parseInt(text) || 0;
                      setBudgetRange(prev => ({ ...prev, max: value }));
                    }}
                    placeholder="100"
                    keyboardType="numeric"
                    style={styles.budgetField}
                  />
                </View>
              </View>
            </View>

            {/* AI Generation Buttons */}
            <View style={styles.aiButtonsRow}>
              <NintendoButton
                title={aiGenerating ? "Generating..." : "🤖 AI Generate"}
                onPress={handleAIGeneration}
                variant="primary"
                style={styles.aiButton}
                disabled={aiGenerating}
              />
              <NintendoButton
                title={aiGenerating ? "Generating..." : "✨ Personalized"}
                onPress={handlePersonalizedGeneration}
                variant="secondary"
                style={styles.aiButton}
                disabled={aiGenerating || !selectedChild}
              />
            </View>

            {/* Library Rewards Button */}
            <NintendoButton
              title="📚 Load Library Rewards"
              onPress={handleLoadLibraryRewards}
              variant="success"
              style={styles.libraryButton}
            />

            {/* OpenAI Status */}
            {!isOpenAIConfigured() && (
              <Text style={styles.aiStatusText}>
                💡 Add OpenAI API key in settings to enable AI generation
              </Text>
            )}
          </NintendoCard>
        </Animated.View>

        {/* AI Generated Suggestions */}
        {showAIGeneration && aiSuggestions.length > 0 && (
          <Animated.View style={[styles.aiSuggestionsSection, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>
              {aiSuggestions[0]?.isLibraryReward ? '📚 Library Rewards' : '🤖 AI Generated Rewards'}
            </Text>
            
            <View style={styles.aiSuggestionsGrid}>
              {aiSuggestions.map((suggestion, index) => (
                <TouchableOpacity
                  key={index}
                  style={styles.aiSuggestionCard}
                  onPress={() => handleUseSuggestion(suggestion)}
                >
                  <Text style={styles.aiSuggestionTitle}>{suggestion.title}</Text>
                  <Text style={styles.aiSuggestionDescription} numberOfLines={2}>
                    {suggestion.description}
                  </Text>
                  <View style={styles.aiSuggestionFooter}>
                    <Text style={styles.aiSuggestionCost}>
                      {suggestion.cost} 🪙
                    </Text>
                    <Text style={styles.aiSuggestionCategory}>
                      {suggestion.category}
                    </Text>
                  </View>
                  {suggestion.availability && (
                    <Text style={styles.aiSuggestionAvailability}>
                      {suggestion.availability}
                    </Text>
                  )}
                </TouchableOpacity>
              ))}
            </View>
            
            <TouchableOpacity
              style={styles.clearAIButton}
              onPress={() => {
                setShowAIGeneration(false);
                setAISuggestions([]);
                playSound('button_press');
              }}
            >
              <Text style={styles.clearAIButtonText}>Clear AI Suggestions</Text>
            </TouchableOpacity>
          </Animated.View>
        )}

        {/* Suggestions Section */}
        {showSuggestions && (
          <Animated.View style={[styles.suggestionsSection, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>💡 Reward Ideas</Text>
            
            {/* Category Filter */}
            <View style={styles.filterSection}>
              <Text style={styles.filterTitle}>Filter by Category:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterScroll}>
                {['all', ...REWARD_CATEGORIES].map((cat) => (
                  <TouchableOpacity
                    key={cat}
                    style={[
                      styles.filterButton,
                      selectedCategory === cat && styles.filterButtonActive
                    ]}
                    onPress={() => {
                      setSelectedCategory(cat);
                      playSound('selection_change');
                    }}
                  >
                    <Text style={[
                      styles.filterButtonText,
                      selectedCategory === cat && styles.filterButtonTextActive
                    ]}>
                      {cat === 'all' ? 'All Categories' : cat}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
            
            {/* Suggestions Grid */}
            <View style={styles.suggestionsGrid}>
              {filteredSuggestions.map((suggestion, index) => (
                <SuggestionCard
                  key={index}
                  title={suggestion.title}
                  coins={suggestion.cost}
                  category={suggestion.category}
                  onPress={() => handleSuggestionPress(suggestion)}
                  style={styles.suggestionCard}
                />
              ))}
            </View>
          </Animated.View>
        )}

        {/* Create Button */}
        <View style={styles.createButtonContainer}>
          <NintendoButton
            title={loading ? 'Creating...' : '🎁 Create Reward'}
            onPress={handleCreateReward}
            variant="success"
            disabled={loading || !title.trim()}
            style={styles.createButton}
          />
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Tutorial Overlay */}
      <GuidedTutorialOverlay
        visible={tutorialState.isActive}
        currentStep={currentStep}
        onNext={nextStep}
        onComplete={() => {
          completeTutorial();
          setPersisted(false);
        }}
        onNavigate={handleTutorialNavigation}
        totalSteps={11}
        currentStepIndex={tutorialState.currentStepIndex}
        scrollViewRef={null}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  formSection: {
    marginBottom: 20,
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
  },
  input: {
    marginBottom: 15,
  },
  formRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  formHalf: {
    flex: 1,
    marginRight: 10,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  optionScroll: {
    flexDirection: 'row',
    maxHeight: 40,
  },
  optionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginRight: 8,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: '#ddd',
  },
  optionButtonActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  optionButtonText: {
    fontSize: 12,
    color: '#666',
    fontWeight: 'bold',
  },
  optionButtonTextActive: {
    color: '#fff',
  },
  toggleButton: {
    alignSelf: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    marginTop: 10,
  },
  toggleButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  
  // Assignment styles
  inputHint: {
    fontSize: 12,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 10,
  },
  toggleOption: {
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    padding: 12,
    alignItems: 'center',
    marginBottom: 15,
    borderWidth: 2,
    borderColor: '#ddd',
  },
  toggleOptionActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  toggleOptionText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  toggleOptionTextActive: {
    color: '#fff',
  },
  familyGoalSection: {
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
    borderRadius: 15,
    padding: 15,
    marginTop: 10,
  },
  childrenGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 10,
  },
  childButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 15,
    padding: 12,
    alignItems: 'center',
    minWidth: 80,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  childButtonActive: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  childButtonEmoji: {
    fontSize: 24,
    marginBottom: 5,
  },
  childButtonText: {
    fontSize: 12,
    color: '#333',
    fontWeight: '600',
    textAlign: 'center',
  },
  childButtonTextActive: {
    color: '#fff',
  },
  childButtonCoins: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  suggestionsSection: {
    marginBottom: 20,
  },
  filterSection: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
  },
  filterScroll: {
    flexDirection: 'row',
  },
  filterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 20,
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  filterButtonActive: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderColor: '#fff',
  },
  filterButtonText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  filterButtonTextActive: {
    color: '#333',
  },
  suggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  suggestionCard: {
    width: (SCREEN_WIDTH - 50) / 2,
    marginBottom: 10,
  },
  createButtonContainer: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  createButton: {
    width: '100%',
  },
  bottomSpacing: {
    height: 40,
  },
  
  // AI Generation Styles
  aiSection: {
    marginBottom: 20,
  },
  aiCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  childSelectionRow: {
    marginBottom: 15,
  },
  childScroll: {
    marginTop: 10,
  },
  childButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 20,
    marginRight: 10,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  childButtonActive: {
    backgroundColor: '#667eea',
    borderColor: '#667eea',
  },
  childButtonText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '600',
  },
  childButtonTextActive: {
    color: '#fff',
  },
  budgetRow: {
    marginBottom: 15,
  },
  budgetInputs: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 10,
  },
  budgetInput: {
    flex: 1,
    marginHorizontal: 5,
  },
  budgetLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
    fontWeight: '600',
  },
  budgetField: {
    height: 40,
  },
  aiButtonsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  aiButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  libraryButton: {
    marginBottom: 15,
  },
  aiStatusText: {
    fontSize: 12,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  
  // AI Suggestions Display Styles
  aiSuggestionsSection: {
    marginBottom: 20,
  },
  aiSuggestionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  aiSuggestionCard: {
    width: '48%',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 15,
    padding: 15,
    marginBottom: 15,
    borderWidth: 1,
    borderColor: '#667eea',
    minHeight: 120,
  },
  aiSuggestionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  aiSuggestionDescription: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
    marginBottom: 10,
  },
  aiSuggestionFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 'auto',
  },
  aiSuggestionCost: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#667eea',
  },
  aiSuggestionCategory: {
    fontSize: 12,
    color: '#999',
    fontWeight: '600',
  },
  aiSuggestionAvailability: {
    fontSize: 12,
    color: '#f39c12',
    marginTop: 5,
    fontStyle: 'italic',
  },
  clearAIButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: 10,
    paddingVertical: 10,
    paddingHorizontal: 20,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
  },
  clearAIButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '600',
  },
});

export default RewardCreationScreen; 