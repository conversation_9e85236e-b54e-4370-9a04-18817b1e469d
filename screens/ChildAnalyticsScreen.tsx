import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar 
} from '../components/ui';
import { supabase } from '../utils/supabase';
import { analyticsApi } from '../utils/api';
import { useChildren } from '../hooks';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

interface ChildAnalyticsScreenProps {
  onBack: () => void;
  childId: string;
}

interface ChildProfile {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
  xp: number;
  daily_streak: number;
  last_activity_date: string;
  created_at: string;
}

interface AnalyticsData {
  // Task Analytics
  totalTasks: number;
  completedTasks: number;
  pendingTasks: number;
  averageCompletionTime: number; // in hours
  completionRate: number; // percentage
  
  // Coin Analytics  
  totalCoinsEarned: number;
  totalCoinsSpent: number;
  averageTaskValue: number;
  highestTaskValue: number;
  
  // Time Analytics
  mostActiveDay: string;
  mostActiveHour: number;
  averageTasksPerDay: number;
  
  // Streak Analytics
  currentStreak: number;
  longestStreak: number;
  streakBreaks: number;
  
  // Category Analytics
  categoryBreakdown: { [key: string]: number };
  
  // Achievement Analytics
  totalAchievements: number;
  recentAchievements: string[];
  
  // Weekly Progress
  weeklyProgress: {
    week: string;
    tasks: number;
    coins: number;
  }[];
  
  // Monthly Progress
  monthlyProgress: {
    month: string;
    tasks: number;
    coins: number;
  }[];
}

const ChildAnalyticsScreen: React.FC<ChildAnalyticsScreenProps> = ({ 
  onBack, 
  childId 
}) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // FAST REACT QUERY: Direct child data loading
  const { children, loading: loadingChildren } = useChildren();
  const childProfile = children.find(child => child.id === childId) || null;
  
  // State
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'all'>('week');
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const progressAnim = useRef(new Animated.Value(0)).current;

  // Load analytics data only (child profile loaded via React Query)
  useEffect(() => {
    if (childProfile) {
      loadAnalyticsData(childId);
    }
  }, [childId, childProfile]);

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  // Progress animation
  useEffect(() => {
    if (analyticsData) {
      Animated.timing(progressAnim, {
        toValue: 1,
        duration: 1000,
        useNativeDriver: false,
      }).start();
    }
  }, [analyticsData]);

  const loadChildAnalytics = async () => {
    try {
      if (!user || !childId) return;

      // REMOVED: child profile loading - using React Query instead

      // Load comprehensive analytics
      await loadAnalyticsData(childId);
      
    } catch (error) {
      console.error('Error loading child analytics:', error);
      Alert.alert('Error', 'Failed to load child analytics.');
    } finally {
      setLoading(false);
    }
  };

  const loadAnalyticsData = async (childId: string) => {
    try {
      // Load analytics data from Edge Function
      const analytics = await analyticsApi.getChildAnalytics(childId);
      setAnalyticsData(analytics);
    } catch (error) {
      console.error('Error loading analytics data:', error);
    }
  };

  const processAnalyticsData = (tasks: any[], achievements: any[], rewards: any[]): AnalyticsData => {
    const completedTasks = tasks.filter(task => task.status === 'completed');
    const pendingTasks = tasks.filter(task => task.status === 'pending');
    
    // Calculate task analytics
    const totalTasks = tasks.length;
    const completionRate = totalTasks > 0 ? (completedTasks.length / totalTasks) * 100 : 0;
    
    // Calculate coin analytics
    const totalCoinsEarned = completedTasks.reduce((sum, task) => sum + task.value, 0);
    const totalCoinsSpent = rewards.reduce((sum, reward) => sum + ((reward.rewards as any)?.cost || 0), 0);
    const averageTaskValue = completedTasks.length > 0 ? totalCoinsEarned / completedTasks.length : 0;
    const highestTaskValue = Math.max(...completedTasks.map(task => task.value), 0);
    
    // Calculate time analytics
    const tasksByDay = completedTasks.reduce((acc, task) => {
      const day = new Date(task.completed_at).toLocaleDateString('en-US', { weekday: 'long' });
      acc[day] = (acc[day] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });
    
    const mostActiveDay = Object.keys(tasksByDay).reduce((a, b) => 
      tasksByDay[a] > tasksByDay[b] ? a : b, 'Monday');
    
    // Calculate category breakdown
    const categoryBreakdown = completedTasks.reduce((acc, task) => {
      const category = (task.task_templates as any)?.category || 'Other';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as { [key: string]: number });
    
    // Calculate weekly/monthly progress
    const weeklyProgress = getWeeklyProgress(completedTasks);
    const monthlyProgress = getMonthlyProgress(completedTasks);
    
    // Calculate completion time (mock data for now)
    const averageCompletionTime = 2.5; // hours
    
    return {
      totalTasks,
      completedTasks: completedTasks.length,
      pendingTasks: pendingTasks.length,
      averageCompletionTime,
      completionRate,
      totalCoinsEarned,
      totalCoinsSpent,
      averageTaskValue: Math.round(averageTaskValue * 10) / 10,
      highestTaskValue,
      mostActiveDay,
      mostActiveHour: 15, // 3 PM - mock data
      averageTasksPerDay: completedTasks.length / 30, // Last 30 days
      currentStreak: 0, // This would need real streak calculation
      longestStreak: 0,
      streakBreaks: 0,
      categoryBreakdown,
      totalAchievements: achievements.length,
      recentAchievements: achievements.slice(0, 3).map(a => (a.achievements as any)?.title || 'Achievement'),
      weeklyProgress,
      monthlyProgress,
    };
  };

  const getWeeklyProgress = (tasks: any[]) => {
    const weeks = [];
    const now = new Date();
    
    for (let i = 0; i < 4; i++) {
      const weekStart = new Date(now);
      weekStart.setDate(now.getDate() - (i * 7));
      weekStart.setHours(0, 0, 0, 0);
      
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 7);
      
      const weekTasks = tasks.filter(task => {
        const taskDate = new Date(task.completed_at);
        return taskDate >= weekStart && taskDate < weekEnd;
      });
      
      const weekCoins = weekTasks.reduce((sum, task) => sum + task.value, 0);
      
      weeks.unshift({
        week: `Week ${i + 1}`,
        tasks: weekTasks.length,
        coins: weekCoins,
      });
    }
    
    return weeks;
  };

  const getMonthlyProgress = (tasks: any[]) => {
    const months = [];
    const now = new Date();
    
    for (let i = 0; i < 6; i++) {
      const month = new Date(now);
      month.setMonth(now.getMonth() - i);
      
      const monthStart = new Date(month.getFullYear(), month.getMonth(), 1);
      const monthEnd = new Date(month.getFullYear(), month.getMonth() + 1, 0);
      
      const monthTasks = tasks.filter(task => {
        const taskDate = new Date(task.completed_at);
        return taskDate >= monthStart && taskDate <= monthEnd;
      });
      
      const monthCoins = monthTasks.reduce((sum, task) => sum + task.value, 0);
      
      months.unshift({
        month: month.toLocaleDateString('en-US', { month: 'short' }),
        tasks: monthTasks.length,
        coins: monthCoins,
      });
    }
    
    return months;
  };

  const handlePeriodChange = (period: 'week' | 'month' | 'all') => {
    playSound('button_press');
    setSelectedPeriod(period);
  };

  const getCurrentProgress = () => {
    if (!analyticsData) return [];
    
    switch (selectedPeriod) {
      case 'week':
        return analyticsData.weeklyProgress;
      case 'month':
        return analyticsData.monthlyProgress;
      default:
        return analyticsData.weeklyProgress;
    }
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.loadingContainer}>
          <NintendoProgressBar progress={0.7} />
          <Text style={styles.loadingText}>Loading analytics...</Text>
        </View>
      </View>
    );
  }

  if (!childProfile || !analyticsData) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Analytics not available</Text>
          <NintendoButton
            title="Go Back"
            onPress={onBack}
            variant="secondary"
          />
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Analytics</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Child Info */}
        <Animated.View style={[styles.section, { 
          opacity: fadeInAnim,
          transform: [{ translateY: slideInAnim }]
        }]}>
          <NintendoCard variant="elevated" withShadow style={styles.childInfoCard}>
            <View style={styles.childInfoContent}>
              <Text style={styles.childAvatar}>{childProfile.avatar}</Text>
              <View style={styles.childInfo}>
                <Text style={styles.childName}>{childProfile.name}'s Analytics</Text>
                <Text style={styles.childLevel}>Level {childProfile.level} • {childProfile.coin_balance} 🪙</Text>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Quick Stats */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.statsCard}>
            <Text style={styles.sectionTitle}>📊 Quick Stats</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{analyticsData.completedTasks}</Text>
                <Text style={styles.statLabel}>Tasks Done</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{analyticsData.totalCoinsEarned}</Text>
                <Text style={styles.statLabel}>Coins Earned</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{analyticsData.completionRate.toFixed(0)}%</Text>
                <Text style={styles.statLabel}>Success Rate</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{analyticsData.totalAchievements}</Text>
                <Text style={styles.statLabel}>Achievements</Text>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Completion Rate */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.progressCard}>
            <Text style={styles.sectionTitle}>🎯 Task Completion</Text>
            <View style={styles.progressContainer}>
              <View style={styles.progressInfo}>
                <Text style={styles.progressLabel}>Completion Rate</Text>
                <Text style={styles.progressValue}>{analyticsData.completionRate.toFixed(1)}%</Text>
              </View>
              <Animated.View style={styles.progressBarContainer}>
                <NintendoProgressBar 
                  progress={analyticsData.completionRate / 100}
                  style={styles.progressBar}
                />
              </Animated.View>
            </View>
            <View style={styles.taskBreakdown}>
              <View style={styles.taskBreakdownItem}>
                <Text style={styles.taskBreakdownNumber}>{analyticsData.completedTasks}</Text>
                <Text style={styles.taskBreakdownLabel}>Completed</Text>
              </View>
              <View style={styles.taskBreakdownItem}>
                <Text style={styles.taskBreakdownNumber}>{analyticsData.pendingTasks}</Text>
                <Text style={styles.taskBreakdownLabel}>Pending</Text>
              </View>
              <View style={styles.taskBreakdownItem}>
                <Text style={styles.taskBreakdownNumber}>{analyticsData.totalTasks}</Text>
                <Text style={styles.taskBreakdownLabel}>Total</Text>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Performance Metrics */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.metricsCard}>
            <Text style={styles.sectionTitle}>⚡ Performance</Text>
            <View style={styles.metricsGrid}>
              <View style={styles.metricItem}>
                <Text style={styles.metricLabel}>Average Task Value</Text>
                <Text style={styles.metricValue}>{analyticsData.averageTaskValue} 🪙</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricLabel}>Highest Task Value</Text>
                <Text style={styles.metricValue}>{analyticsData.highestTaskValue} 🪙</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricLabel}>Most Active Day</Text>
                <Text style={styles.metricValue}>{analyticsData.mostActiveDay}</Text>
              </View>
              <View style={styles.metricItem}>
                <Text style={styles.metricLabel}>Coins Spent</Text>
                <Text style={styles.metricValue}>{analyticsData.totalCoinsSpent} 🪙</Text>
              </View>
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Category Breakdown */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.categoryCard}>
            <Text style={styles.sectionTitle}>📂 Task Categories</Text>
            {Object.keys(analyticsData.categoryBreakdown).length === 0 ? (
              <Text style={styles.emptyText}>No tasks completed yet!</Text>
            ) : (
              <View style={styles.categoryList}>
                {Object.entries(analyticsData.categoryBreakdown).map(([category, count]) => (
                  <View key={category} style={styles.categoryItem}>
                    <Text style={styles.categoryName}>{category}</Text>
                    <View style={styles.categoryProgressContainer}>
                      <View style={styles.categoryProgressBar}>
                        <View 
                          style={[
                            styles.categoryProgress,
                            { 
                              width: `${(count / analyticsData.completedTasks) * 100}%`
                            }
                          ]}
                        />
                      </View>
                      <Text style={styles.categoryCount}>{count}</Text>
                    </View>
                  </View>
                ))}
              </View>
            )}
          </NintendoCard>
        </Animated.View>

        {/* Period Selector */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <View style={styles.periodSelector}>
            {['week', 'month', 'all'].map((period) => (
              <TouchableOpacity
                key={period}
                style={[
                  styles.periodButton,
                  selectedPeriod === period && styles.periodButtonActive
                ]}
                onPress={() => handlePeriodChange(period as any)}
              >
                <Text style={[
                  styles.periodButtonText,
                  selectedPeriod === period && styles.periodButtonTextActive
                ]}>
                  {period === 'week' ? 'Weekly' : period === 'month' ? 'Monthly' : 'All Time'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </Animated.View>

        {/* Progress Chart */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.chartCard}>
            <Text style={styles.sectionTitle}>📈 Progress Over Time</Text>
            <View style={styles.chartContainer}>
              {getCurrentProgress().map((item, index) => (
                <View key={index} style={styles.chartItem}>
                  <View style={styles.chartBarContainer}>
                    <View 
                      style={[
                        styles.chartBar,
                        { 
                          height: `${Math.max((item.tasks / 10) * 100, 10)}%`,
                          backgroundColor: '#667eea'
                        }
                      ]}
                    />
                  </View>
                  <Text style={styles.chartLabel}>
                    {selectedPeriod === 'week' ? item.week : item.month}
                  </Text>
                  <Text style={styles.chartValue}>{item.tasks}</Text>
                </View>
              ))}
            </View>
          </NintendoCard>
        </Animated.View>

        {/* Recent Achievements */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <NintendoCard variant="elevated" withShadow style={styles.achievementsCard}>
            <Text style={styles.sectionTitle}>🏆 Recent Achievements</Text>
            {analyticsData.recentAchievements.length === 0 ? (
              <Text style={styles.emptyText}>No achievements yet!</Text>
            ) : (
              <View style={styles.achievementsList}>
                {analyticsData.recentAchievements.map((achievement, index) => (
                  <View key={index} style={styles.achievementItem}>
                    <Text style={styles.achievementIcon}>🏆</Text>
                    <Text style={styles.achievementTitle}>{achievement}</Text>
                  </View>
                ))}
              </View>
            )}
          </NintendoCard>
        </Animated.View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  errorText: {
    fontSize: 18,
    color: '#fff',
    marginBottom: 20,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  childInfoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  childInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  childAvatar: {
    fontSize: 40,
    marginRight: 15,
  },
  childInfo: {
    flex: 1,
  },
  childName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  childLevel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  statsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#667eea',
  },
  statLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
    textAlign: 'center',
  },
  progressCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  progressContainer: {
    marginBottom: 20,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  progressLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  progressValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#667eea',
  },
  progressBarContainer: {
    height: 20,
  },
  progressBar: {
    height: 20,
  },
  taskBreakdown: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
  },
  taskBreakdownItem: {
    alignItems: 'center',
  },
  taskBreakdownNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  taskBreakdownLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 5,
  },
  metricsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  metricsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  metricItem: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  metricLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  metricValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  categoryCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  categoryList: {
    marginTop: 10,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  categoryName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  categoryProgressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 15,
  },
  categoryProgressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginRight: 10,
  },
  categoryProgress: {
    height: '100%',
    backgroundColor: '#667eea',
    borderRadius: 4,
  },
  categoryCount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#667eea',
    minWidth: 30,
    textAlign: 'center',
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 25,
    padding: 5,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 10,
    paddingHorizontal: 15,
    borderRadius: 20,
    alignItems: 'center',
  },
  periodButtonActive: {
    backgroundColor: '#667eea',
  },
  periodButtonText: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  periodButtonTextActive: {
    color: '#fff',
  },
  chartCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 150,
    marginTop: 20,
  },
  chartItem: {
    alignItems: 'center',
    flex: 1,
  },
  chartBarContainer: {
    height: 100,
    justifyContent: 'flex-end',
    marginBottom: 10,
  },
  chartBar: {
    width: 20,
    borderRadius: 10,
    minHeight: 10,
  },
  chartLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  chartValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  achievementsCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 20,
  },
  achievementsList: {
    marginTop: 10,
  },
  achievementItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
  },
  achievementIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
    marginTop: 20,
  },
  bottomSpacing: {
    height: 40,
  },
});

export default ChildAnalyticsScreen; 