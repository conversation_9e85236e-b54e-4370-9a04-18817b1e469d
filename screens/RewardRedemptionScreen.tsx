import React, { useEffect, useRef, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { 
  NintendoButton, 
  NintendoCard, 
  NintendoProgressBar 
} from '../components/ui';
import { supabase } from '../utils/supabase';
// Removed old API imports - using smart hooks instead
import { 
  validateRewardRedemptionSync,
  redeemReward,
  checkAchievements,
  RewardRedemptionData 
} from '../utils/rewardValidation';
import { useSmartRewardRedemption, useChildren } from '../hooks';

const { children, loading, error } = useChildren(); const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface RewardRedemptionScreenProps {
  onBack: () => void;
  selectedChildId?: string;
}

interface Reward {
  id: string;
  title: string;
  description?: string;
  cost: number;
  category?: string;
  parent_id: string;
  created_at: string;
}

interface Child {
  id: string;
  name: string;
  avatar: string;
  coin_balance: number;
  level: number;
}

interface UnlockedReward {
  id: string;
  reward_id: string;
  unlocked_at: string;
  reward_title: string;
  reward_description: string;
  reward_cost: number;
}

const RewardRedemptionScreen: React.FC<RewardRedemptionScreenProps> = ({ onBack, selectedChildId }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // Simplified hooks
  const { children, loading, error } = useChildren();
  const { rewards, handleRedeemReward, handleRedeemConfirm } = useSmartRewardRedemption();
  
  // Local state for selected child and reward
  const [selectedChild, setSelectedChild] = useState<Child | null>(null);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [redeeming, setRedeeming] = useState(false);
  
  // Filter rewards for the selected child
  const availableRewards = rewards.filter(reward => reward.cost <= (selectedChild?.coin_balance || 0));
  const expensiveRewards = rewards.filter(reward => reward.cost > (selectedChild?.coin_balance || 0));
  
  // Modal states
  const [confirmModal, setConfirmModal] = useState({ isOpen: false, data: null });
  const [celebrationModal, setCelebrationModal] = useState({ isOpen: false, data: null });
  const [unlockedModal, setUnlockedModal] = useState({ isOpen: false, data: null });
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const celebrationScale = useRef(new Animated.Value(0)).current;
  const confettiAnim = useRef(new Animated.Value(0)).current;

  // Set selected child when children load
  useEffect(() => {
    if (children.length > 0) {
      const child = selectedChildId 
        ? children.find(c => c.id === selectedChildId) 
        : children[0];
      setSelectedChild(child || null);
    }
  }, [children, selectedChildId]);

  // Entrance animation
  useEffect(() => {
    playSound('button_press');
    
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);
  
  const handleChildSelect = (childId: string) => {
    playSound('button_press');
    const child = children.find(c => c.id === childId);
    setSelectedChild(child || null);
  };

  const handleRewardPress = (reward: Reward) => {
    setSelectedReward(reward);
    setConfirmModal({ isOpen: true, data: { reward, child: selectedChild } });
  };

  const handleCancelRedeem = () => {
    setConfirmModal({ isOpen: false, data: null });
    setSelectedReward(null);
  };

  const handleCloseCelebration = () => {
    setCelebrationModal({ isOpen: false, data: null });
  };

  const handleShowUnlockedRewards = () => {
    setUnlockedModal({ isOpen: true, data: null });
  };

  const handleCloseUnlockedRewards = () => {
    setUnlockedModal({ isOpen: false, data: null });
  };

  const hasEnoughCoins = (reward: Reward) => {
    return (selectedChild?.coin_balance || 0) >= reward.cost;
  };

  const getRemainingCoins = () => {
    return (selectedChild?.coin_balance || 0) - (selectedReward?.cost || 0);
  };
  
  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={['#667eea', '#764ba2', '#f093fb']}
          style={StyleSheet.absoluteFillObject}
        />
        <View style={styles.loadingContainer}>
          <NintendoProgressBar progress={0.7} />
          <Text style={styles.loadingText}>Loading rewards...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#667eea', '#764ba2', '#f093fb']}
        style={StyleSheet.absoluteFillObject}
      />
      
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Reward Store</Text>
        <View style={styles.headerSpacer} />
      </View>

      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {/* Child Selection */}
        {children.length > 1 && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>👦 Select Child</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.childrenScroll}>
              {children.map((child) => (
                <TouchableOpacity
                  key={child.id}
                  style={[
                    styles.childCard,
                    selectedChildId === child.id && styles.childCardSelected
                  ]}
                  onPress={() => handleChildSelect(child.id)}
                >
                  <Text style={styles.childAvatar}>{child.avatar}</Text>
                  <Text style={styles.childName}>{child.name}</Text>
                  <Text style={styles.childCoins}>{child.coin_balance} 🪙</Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </Animated.View>
        )}

        {/* Selected Child Info */}
        {selectedChild && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <NintendoCard variant="elevated" withShadow style={styles.childInfoCard}>
              <View style={styles.childInfoContent}>
                <Text style={styles.childInfoAvatar}>{selectedChild.avatar}</Text>
                <View style={styles.childInfoText}>
                  <Text style={styles.childInfoName}>{selectedChild.name}'s Coins</Text>
                  <Text style={styles.childInfoBalance}>{selectedChild.coin_balance} 🪙</Text>
                  <Text style={styles.childLevel}>Level {selectedChild.level}</Text>
                </View>
              </View>
            </NintendoCard>
          </Animated.View>
        )}

        {/* Available Rewards */}
        <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
          <Text style={styles.sectionTitle}>🎁 Available Rewards</Text>
          
          {availableRewards.length === 0 ? (
            <NintendoCard variant="default" style={styles.emptyCard}>
              <Text style={styles.emptyText}>
                🏆 Complete more tasks to earn coins for rewards!
              </Text>
            </NintendoCard>
          ) : (
            <View style={styles.rewardsGrid}>
              {availableRewards.map((reward) => (
                <NintendoCard 
                  key={reward.id} 
                  variant="elevated" 
                  withShadow 
                  style={styles.rewardCard}
                >
                  <TouchableOpacity
                    onPress={() => handleRewardPress(reward)}
                    style={styles.rewardContent}
                  >
                    <Text style={styles.rewardTitle}>{reward.title}</Text>
                    <Text style={styles.rewardDescription}>{reward.description}</Text>
                    <View style={styles.rewardFooter}>
                      <Text style={styles.rewardCost}>{reward.cost} 🪙</Text>
                      <Text style={styles.rewardCategory}>{reward.category}</Text>
                    </View>
                  </TouchableOpacity>
                </NintendoCard>
              ))}
            </View>
          )}
        </Animated.View>

        {/* Expensive Rewards (Coming Soon) */}
        {expensiveRewards.length > 0 && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>💰 Save Up For These!</Text>
            <View style={styles.rewardsGrid}>
              {expensiveRewards.map((reward) => (
                <NintendoCard 
                  key={reward.id} 
                  variant="default" 
                  style={[styles.rewardCard, styles.expensiveRewardCard] as any}
                >
                  <View style={styles.rewardContent}>
                    <Text style={[styles.rewardTitle, styles.expensiveRewardTitle]}>
                      {reward.title}
                    </Text>
                    <Text style={[styles.rewardDescription, styles.expensiveRewardDescription]}>
                      {reward.description}
                    </Text>
                    <View style={styles.rewardFooter}>
                      <Text style={[styles.rewardCost, styles.expensiveRewardCost]}>
                        {reward.cost} 🪙
                      </Text>
                      <Text style={styles.rewardCategory}>{reward.category}</Text>
                    </View>
                    <Text style={styles.needMoreCoins}>
                      Need {reward.cost - (selectedChild?.coin_balance || 0)} more coins
                    </Text>
                  </View>
                </NintendoCard>
              ))}
            </View>
          </Animated.View>
        )}

        {/* Unlocked Rewards History */}
        {unlockedModal.isOpen && (
          <Animated.View style={[styles.section, { opacity: fadeInAnim }]}>
            <Text style={styles.sectionTitle}>🏆 Your Rewards</Text>
            <View style={styles.unlockedList}>
              {(unlockedModal.data || []).map((unlocked) => (
                <NintendoCard key={unlocked.id} variant="default" style={styles.unlockedCard}>
                  <View style={styles.unlockedContent}>
                    <Text style={styles.unlockedTitle}>✅ {unlocked.reward_title}</Text>
                    <Text style={styles.unlockedDate}>
                      Unlocked {new Date(unlocked.unlocked_at).toLocaleDateString()}
                    </Text>
                  </View>
                </NintendoCard>
              ))}
              {(unlockedModal.data || []).length > 3 && (
                <TouchableOpacity 
                  style={styles.showMoreButton}
                  onPress={handleShowUnlockedRewards}
                >
                  <Text style={styles.showMoreText}>
                    View all {unlockedModal.data.length} rewards →
                  </Text>
                </TouchableOpacity>
              )}
            </View>
          </Animated.View>
        )}

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Confirmation Modal */}
      <Modal
        visible={confirmModal.isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCancelRedeem}
      >
        <View style={styles.modalOverlay}>
          <NintendoCard variant="elevated" withShadow style={styles.modalCard}>
            <Text style={styles.modalTitle}>🎁 Redeem Reward?</Text>
            {selectedReward && (
              <>
                <Text style={styles.modalRewardTitle}>{selectedReward.title}</Text>
                <Text style={styles.modalRewardDescription}>{selectedReward.description}</Text>
                <Text style={styles.modalRewardCost}>Cost: {selectedReward.cost} 🪙</Text>
                <Text style={styles.modalBalance}>
                  Your balance: {selectedChild?.coin_balance} 🪙
                </Text>
                <Text style={styles.modalAfterBalance}>
                  After purchase: {(selectedChild?.coin_balance || 0) - selectedReward.cost} 🪙
                </Text>
              </>
            )}
            
            <View style={styles.modalButtons}>
              <NintendoButton
                title="Cancel"
                onPress={handleCancelRedeem}
                variant="secondary"
                style={styles.modalButton}
              />
              <NintendoButton
                title={redeeming ? 'Redeeming...' : 'Redeem!'}
                onPress={handleRedeemConfirm}
                variant="success"
                disabled={redeeming}
                style={styles.modalButton}
              />
            </View>
          </NintendoCard>
        </View>
      </Modal>

      {/* Celebration Modal */}
      <Modal
        visible={celebrationModal.isOpen}
        transparent={true}
        animationType="none"
      >
        <View style={styles.celebrationOverlay}>
          <Animated.View
            style={[
              styles.celebrationContent,
              {
                transform: [{ scale: celebrationScale }],
                opacity: confettiAnim,
              },
            ]}
          >
            <Text style={styles.celebrationTitle}>🎉 Reward Unlocked! 🎉</Text>
            <Text style={styles.celebrationSubtitle}>
              {selectedReward?.title}
            </Text>
            <Text style={styles.celebrationText}>
              Enjoy your reward! 🎁
            </Text>
          </Animated.View>
        </View>
      </Modal>

      {/* All Unlocked Rewards Modal */}
      <Modal
        visible={unlockedModal.isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={handleCloseUnlockedRewards}
      >
        <View style={styles.modalOverlay}>
          <NintendoCard variant="elevated" withShadow style={styles.largeModalCard}>
            <Text style={styles.modalTitle}>🏆 All Your Rewards</Text>
            <ScrollView style={styles.unlockedModalScroll}>
              {(unlockedModal.data || []).map((unlocked) => (
                <View key={unlocked.id} style={styles.unlockedModalItem}>
                  <Text style={styles.unlockedModalTitle}>✅ {unlocked.reward_title}</Text>
                  <Text style={styles.unlockedModalDescription}>{unlocked.reward_description}</Text>
                  <Text style={styles.unlockedModalDate}>
                    Unlocked {new Date(unlocked.unlocked_at).toLocaleDateString()}
                  </Text>
                </View>
              ))}
            </ScrollView>
            <NintendoButton
              title="Close"
              onPress={handleCloseUnlockedRewards}
              variant="secondary"
              style={styles.modalButton}
            />
          </NintendoCard>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#fff',
    marginTop: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 60,
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  backButton: {
    padding: 10,
  },
  backButtonText: {
    fontSize: 16,
    color: '#fff',
    fontWeight: 'bold',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  headerSpacer: {
    width: 60,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 20,
  },
  section: {
    marginBottom: 30,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 15,
  },
  childrenScroll: {
    flexDirection: 'row',
  },
  childCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 15,
    padding: 15,
    marginRight: 15,
    minWidth: 100,
    alignItems: 'center',
    borderWidth: 3,
    borderColor: 'transparent',
  },
  childCardSelected: {
    borderColor: '#fff',
    backgroundColor: '#fff',
  },
  childAvatar: {
    fontSize: 30,
    marginBottom: 5,
  },
  childName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  childCoins: {
    fontSize: 14,
    color: '#666',
  },
  childInfoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    padding: 15,
  },
  childInfoContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  childInfoAvatar: {
    fontSize: 40,
    marginRight: 15,
  },
  childInfoText: {
    flex: 1,
  },
  childInfoName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  childInfoBalance: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#667eea',
    marginTop: 5,
  },
  childLevel: {
    fontSize: 14,
    color: '#666',
    marginTop: 5,
  },
  emptyCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    padding: 20,
    alignItems: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  rewardsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  rewardCard: {
    width: (SCREEN_WIDTH - 50) / 2,
    marginBottom: 15,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
  },
  expensiveRewardCard: {
    opacity: 0.7,
    backgroundColor: 'rgba(255, 255, 255, 0.6)',
  } as any,
  rewardContent: {
    padding: 15,
  },
  rewardTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  expensiveRewardTitle: {
    color: '#999',
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 10,
    lineHeight: 18,
  },
  expensiveRewardDescription: {
    color: '#999',
  },
  rewardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rewardCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#667eea',
  },
  expensiveRewardCost: {
    color: '#999',
  },
  rewardCategory: {
    fontSize: 12,
    color: '#999',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  needMoreCoins: {
    fontSize: 12,
    color: '#f39c12',
    fontWeight: 'bold',
    marginTop: 5,
    textAlign: 'center',
  },
  unlockedList: {
    marginTop: 10,
  },
  unlockedCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    marginBottom: 10,
    padding: 15,
  },
  unlockedContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  unlockedTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
    flex: 1,
  },
  unlockedDate: {
    fontSize: 12,
    color: '#666',
  },
  showMoreButton: {
    alignItems: 'center',
    paddingVertical: 10,
  },
  showMoreText: {
    fontSize: 14,
    color: '#fff',
    fontWeight: 'bold',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalCard: {
    backgroundColor: '#fff',
    padding: 20,
    width: '100%',
    maxWidth: 400,
  },
  largeModalCard: {
    backgroundColor: '#fff',
    padding: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: SCREEN_HEIGHT * 0.8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 15,
  },
  modalRewardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 5,
  },
  modalRewardDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 15,
  },
  modalRewardCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#667eea',
    textAlign: 'center',
    marginBottom: 5,
  },
  modalBalance: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    marginBottom: 5,
  },
  modalAfterBalance: {
    fontSize: 14,
    color: '#27ae60',
    textAlign: 'center',
    marginBottom: 20,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 5,
  },
  celebrationOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  celebrationContent: {
    backgroundColor: '#fff',
    padding: 40,
    borderRadius: 20,
    alignItems: 'center',
  },
  celebrationTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  celebrationSubtitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#667eea',
    marginBottom: 10,
  },
  celebrationText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
  },
  unlockedModalScroll: {
    maxHeight: SCREEN_HEIGHT * 0.6,
  },
  unlockedModalItem: {
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
    paddingVertical: 15,
  },
  unlockedModalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#27ae60',
    marginBottom: 5,
  },
  unlockedModalDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  unlockedModalDate: {
    fontSize: 12,
    color: '#999',
  },
  bottomSpacing: {
    height: 40,
  },
});

export default RewardRedemptionScreen; 