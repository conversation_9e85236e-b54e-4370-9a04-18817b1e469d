import React from 'react';
import { View, StyleSheet, SafeAreaView } from 'react-native';
import { TaskList } from '../components/organisms/TaskList';
import { useSmartTaskCompletion } from '../hooks';
import { useUIStore } from '../stores/useUIStore';

interface FamilyDashboardProps {
  onShowSettings: () => void;
}

export const FamilyDashboardNew: React.FC<FamilyDashboardProps> = ({ onShowSettings }) => {
  const { tasks, handleCompleteTask, loading } = useSmartTaskCompletion();
  const { modals } = useUIStore();
  
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <TaskList />
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
}); 