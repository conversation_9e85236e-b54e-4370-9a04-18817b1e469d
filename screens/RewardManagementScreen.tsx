import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Animated,
  Dimensions,
  Modal,
} from 'react-native';
import { useRewards, Reward } from '../hooks/useRewards';
import { useAuth } from '../contexts/AuthContext';
import { useAudio } from '../contexts/AudioContext';
import { NintendoButton } from '../components/ui';

const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

interface RewardManagementScreenProps {
  onBack: () => void;
}

const RewardManagementScreen: React.FC<RewardManagementScreenProps> = ({ onBack }) => {
  const { user } = useAuth();
  const { playSound } = useAudio();
  
  // Use simplified hooks
  const { rewards, loading, error, loadRewards, createReward, updateReward, deleteReward } = useRewards();
  
  // State
  const [refreshing, setRefreshing] = useState(false);
  const [selectedReward, setSelectedReward] = useState<Reward | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingReward, setEditingReward] = useState<Reward | null>(null);
  
  // Edit form state
  const [editTitle, setEditTitle] = useState('');
  const [editDescription, setEditDescription] = useState('');
  const [editCost, setEditCost] = useState('');
  const [editCategory, setEditCategory] = useState('');
  const [saving, setSaving] = useState(false);
  
  // Animation refs
  const fadeInAnim = useRef(new Animated.Value(0)).current;
  const slideInAnim = useRef(new Animated.Value(50)).current;
  const modalScale = useRef(new Animated.Value(0)).current;

  // Load data on mount
  useEffect(() => {
    loadRewards();
  }, []);

  // Entrance animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeInAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideInAnim, {
        toValue: 0,
        duration: 800,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadRewards();
    setRefreshing(false);
    playSound('button_press');
  };

  const handleRewardPress = (reward: Reward) => {
    playSound('card_flip');
    setSelectedReward(reward);
  };

  const handleEditReward = (reward: Reward) => {
    playSound('button_press');
    setEditingReward(reward);
    setEditTitle(reward.title);
    setEditDescription(reward.description || '');
    setEditCost(reward.cost.toString());
    setEditCategory(reward.category || '');
    setShowEditModal(true);
    
    // Modal animation
    Animated.spring(modalScale, {
      toValue: 1,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  const handleDuplicateReward = async (reward: Reward) => {
    playSound('button_press');
    
    if (!user) {
      Alert.alert('Error', 'User not found');
      return;
    }

    try {
      const duplicateData = {
        title: `${reward.title} (Copy)`,
        description: reward.description || '',
        cost: reward.cost,
        category: reward.category || '',
        parent_id: user.id,
      };

      await createReward(duplicateData);
      playSound('achievement_unlock');
      Alert.alert('Reward Duplicated! 🎉', `"${duplicateData.title}" has been created.`);
    } catch (error) {
      console.error('Error duplicating reward:', error);
      Alert.alert('Error', 'Failed to duplicate reward. Please try again.');
    }
  };

  const handleDeleteReward = (reward: Reward) => {
    playSound('button_press');
    setSelectedReward(reward);
    setShowDeleteModal(true);
  };

  const confirmDeleteReward = async () => {
    if (!selectedReward) return;

    try {
      await deleteReward(selectedReward.id);
      playSound('button_success');
      Alert.alert('Reward Deleted! ✅', `"${selectedReward.title}" has been removed.`);
      setShowDeleteModal(false);
      setSelectedReward(null);
    } catch (error) {
      console.error('Error deleting reward:', error);
      Alert.alert('Error', 'Failed to delete reward. Please try again.');
    }
  };

  const handleSaveReward = async () => {
    if (!editTitle.trim() || !editCost.trim()) {
      Alert.alert('Error', 'Please fill in all required fields.');
      return;
    }

    setSaving(true);

    try {
      const rewardData = {
        title: editTitle.trim(),
        description: editDescription.trim(),
        cost: parseInt(editCost),
        category: editCategory.trim(),
        parent_id: user?.id || '',
      };

      if (editingReward) {
        // Update existing reward
        await updateReward(editingReward.id, rewardData);
      } else {
        // Create new reward (duplication)
        await createReward(rewardData);
      }

      playSound('achievement_unlock');
      Alert.alert('Reward Saved! 🎉', `"${rewardData.title}" has been saved.`);
      setShowEditModal(false);
      setEditingReward(null);
    } catch (error) {
      console.error('Error saving reward:', error);
      Alert.alert('Error', 'Failed to save reward. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setEditingReward(null);
    setEditTitle('');
    setEditDescription('');
    setEditCost('');
    setEditCategory('');
  };

  const getCategoryIcon = (category: string) => {
    const icons: { [key: string]: string } = {
      'toys': '🎮',
      'activities': '🎯',
      'treats': '🍬',
      'privileges': '⭐',
      'default': '🎁',
    };
    return icons[category.toLowerCase()] || icons.default;
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading rewards...</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <NintendoButton
          title="← Back"
          onPress={onBack}
          variant="secondary"
          size="small"
        />
        <Text style={styles.headerTitle}>Reward Management</Text>
        <NintendoButton
          title="Refresh"
          onPress={handleRefresh}
          variant="secondary"
          size="small"
          disabled={refreshing}
        />
      </View>

      {/* Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Animated.View style={[styles.contentContainer, { opacity: fadeInAnim }]}>
          {rewards.length === 0 ? (
            <View style={styles.emptyCard}>
              <Text style={styles.emptyText}>
                No rewards created yet.{'\n'}
                Create your first reward to get started!
              </Text>
            </View>
          ) : (
            <View style={styles.rewardsList}>
              {rewards.map((reward) => (
                <View key={reward.id} style={styles.rewardCard}>
                  <View style={styles.rewardHeader}>
                    <Text style={styles.rewardTitle}>{reward.title}</Text>
                    <Text style={styles.rewardCost}>{reward.cost} 🪙</Text>
                  </View>
                  
                  {reward.description && (
                    <Text style={styles.rewardDescription}>{reward.description}</Text>
                  )}
                  
                  <View style={styles.rewardFooter}>
                    <Text style={styles.rewardCategory}>
                      {getCategoryIcon(reward.category || 'default')} {reward.category || 'General'}
                    </Text>
                    
                    <View style={styles.rewardActions}>
                      <NintendoButton
                        title="Edit"
                        onPress={() => handleEditReward(reward)}
                        variant="secondary"
                        size="small"
                      />
                      <NintendoButton
                        title="Duplicate"
                        onPress={() => handleDuplicateReward(reward)}
                        variant="success"
                        size="small"
                      />
                      <NintendoButton
                        title="Delete"
                        onPress={() => handleDeleteReward(reward)}
                        variant="danger"
                        size="small"
                      />
                    </View>
                  </View>
                </View>
              ))}
            </View>
          )}
        </Animated.View>
      </ScrollView>

      {/* Edit/Add Modal */}
      <Modal
        visible={showEditModal}
        transparent={true}
        animationType="none"
        onRequestClose={handleCloseEditModal}
      >
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.modalContainer, { transform: [{ scale: modalScale }] }]}>
            <View style={styles.modalCard}>
              <Text style={styles.modalTitle}>
                {editingReward ? '✏️ Edit Reward' : '📋 Duplicate Reward'}
              </Text>
              
              <Text style={styles.modalInput}>
                Reward title
              </Text>
              
              <Text style={styles.modalInput}>
                Description (optional)
              </Text>
              
              <Text style={styles.modalInput}>
                Cost in coins
              </Text>
              
              <Text style={styles.modalInput}>
                Category (optional)
              </Text>
              
              <View style={styles.modalActions}>
                <NintendoButton
                  title="Cancel"
                  onPress={handleCloseEditModal}
                  variant="secondary"
                  style={styles.modalButton}
                />
                <NintendoButton
                  title={saving ? 'Saving...' : 'Save'}
                  onPress={handleSaveReward}
                  variant="success"
                  disabled={saving}
                  style={styles.modalButton}
                />
              </View>
            </View>
          </Animated.View>
        </View>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        visible={showDeleteModal}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDeleteModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalCard}>
            <Text style={styles.modalTitle}>🗑️ Delete Reward?</Text>
            {selectedReward && (
              <Text style={styles.deleteText}>
                Are you sure you want to delete "{selectedReward.title}"?{'\n'}
                This action cannot be undone.
              </Text>
            )}
            
            <View style={styles.modalActions}>
              <NintendoButton
                title="Cancel"
                onPress={() => setShowDeleteModal(false)}
                variant="secondary"
                style={styles.modalButton}
              />
              <NintendoButton
                title="Delete"
                onPress={confirmDeleteReward}
                variant="danger"
                style={styles.modalButton}
              />
            </View>
          </View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  loadingText: {
    color: '#666',
    fontSize: 18,
    textAlign: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  emptyCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 40,
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
  },
  rewardsList: {
    gap: 16,
  },
  rewardCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  rewardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  rewardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  rewardCost: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#FF6B9D',
  },
  rewardDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  rewardFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  rewardCategory: {
    fontSize: 12,
    color: '#999',
  },
  rewardActions: {
    flexDirection: 'row',
    gap: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: SCREEN_WIDTH * 0.9,
    maxWidth: 400,
  },
  modalCard: {
    backgroundColor: '#fff',
    borderRadius: 16,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  modalInput: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
  },
  modalButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  deleteText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
});

export default RewardManagementScreen; 