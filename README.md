# KidsCoin

A digital platform designed to teach children financial literacy and responsibility through gamified task completion and reward systems.

## 🎯 Project Vision

KidsCoin transforms everyday chores and good habits into learning opportunities, bridging the gap between parents and children while making financial education fun and engaging.

### Core Loop
1. **Parent assigns tasks** → 2. **Child completes tasks to earn digital coins** → 3. **Child redeems coins for real-world rewards**

## 🏗️ Architecture

### Frontend: Expo (React Native)
- **Cross-platform**: Single codebase for iOS and Android
- **Latest Expo SDK**: Modern development experience with OTA updates
- **Kid Mode**: Secure kiosk mode with PIN protection

### Backend: Supabase
- **Database**: PostgreSQL with Row Level Security (RLS)
- **Auth**: Built-in user management with social providers
- **API Layer**: Clean REST endpoints via Edge Functions
- **Realtime**: Live updates for instant parent-child sync

### Key Features
- **Family Management**: Parent accounts with multiple children
- **Task System**: Templates, assignments, and completion tracking
- **Verification Modes**: "Strict" (requires approval) vs "Trusting" (instant completion)
- **Reward Shop**: Customizable rewards with coin redemption
- **Gamification**: XP, levels, daily streaks, and achievements
- **Security**: RLS policies ensure family data isolation

## 📁 Project Structure

```
kidscoin/
├── docs/                     # Documentation and planning
│   ├── PRD.md               # Product Requirements Document
│   └── TODO.md              # Development progress tracker
├── migrations/              # Database schema and functions
│   ├── 20250117_000001_create_initial_schema.sql
│   └── 20250117_000002_create_database_functions.sql
├── supabase/
│   └── functions/           # Edge Functions (API layer)
│       ├── family-settings/ # Family configuration management
│       ├── children/        # Child profile CRUD operations
│       ├── task-complete/   # Task completion with verification
│       └── README.md        # API documentation
├── App.tsx                  # Expo app entry point
└── package.json            # Dependencies and scripts
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- Expo CLI
- Supabase account

### Setup
1. **Clone and install dependencies:**
   ```bash
   git clone <repository>
   cd kidscoin
   npm install
   ```

2. **Database Setup:**
   - Copy SQL from `migrations/20250117_000001_create_initial_schema.sql`
   - Run in your Supabase SQL Editor
   - Copy SQL from `migrations/20250117_000002_create_database_functions.sql`
   - Run in your Supabase SQL Editor

3. **Environment Configuration:**
   ```bash
   # Create .env file with your Supabase credentials
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
   ```

4. **Start Development:**
   ```bash
   npm start
   ```

## 📊 Current Status

✅ **Phase 1 Complete: Project Scaffolding & Setup**
- Expo project initialized
- Supabase database schema deployed
- Edge Functions API layer created
- Security policies (RLS) implemented

🚧 **Phase 2 In Progress: Feature Implementation**
- Onboarding flow design
- UI components and screens
- Parent and child modes
- Task and reward systems

## 🎨 Design Principles

### Nintendo Switch-Style Onboarding
Our onboarding experience prioritizes:
- **Delight**: Smooth animations and celebratory moments
- **Clarity**: Clear value proposition and guided setup
- **Confidence**: Users feel empowered to continue
- **Completion**: Full task→reward loop demonstrated

### Kid-Friendly Interface
- **Playful**: Bright colors and engaging animations
- **Safe**: Secure kiosk mode prevents app exit
- **Intuitive**: Large buttons and clear visual hierarchy
- **Rewarding**: Immediate feedback for completed actions

## 🔐 Security Features

- **Row Level Security (RLS)**: Database-level family data isolation
- **Edge Functions**: Server-side business logic prevents client manipulation
- **Kiosk Mode**: PIN-protected child environment
- **Code Verification**: Optional parent codes for task completion

## 📝 API Endpoints

See [`supabase/functions/README.md`](supabase/functions/README.md) for complete API documentation.

Key endpoints:
- `POST /functions/v1/family-settings` - Configure verification mode
- `POST /functions/v1/children` - Manage child profiles
- `POST /functions/v1/task-complete` - Handle task completion

## 🤝 Contributing

1. Check [`docs/TODO.md`](docs/TODO.md) for current development priorities
2. Follow the established patterns in Edge Functions
3. Update documentation for any new features
4. Test with both "strict" and "trusting" verification modes

## 📚 Documentation

- [`docs/PRD.md`](docs/PRD.md) - Complete product requirements and user stories
- [`docs/TODO.md`](docs/TODO.md) - Development progress and task tracking
- [`supabase/functions/README.md`](supabase/functions/README.md) - API layer documentation

---

**Built with ❤️ to make financial education fun for families** 