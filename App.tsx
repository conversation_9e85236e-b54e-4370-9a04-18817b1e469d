import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { AuthProvider } from './contexts/AuthContext';
import { AudioProvider } from './contexts/AudioContext';
import { TutorialProvider } from './contexts/TutorialContext';
import { ErrorBoundary } from './components/ErrorBoundary';
import AppRouter from './components/AppRouter';

// Suppress useInsertionEffect warning from expo-linear-gradient
const originalWarn = console.warn;
console.warn = (...args) => {
  if (typeof args[0] === 'string' && args[0].includes('useInsertionEffect')) {
    return;
  }
  originalWarn(...args);
};

// Root app component
export default function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AudioProvider>
          <TutorialProvider>
            <StatusBar style="light" />
            <AppRouter />
          </TutorialProvider>
        </AudioProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}
