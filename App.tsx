import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { AuthProvider } from './contexts/AuthContext';
import { AudioProvider } from './contexts/AudioContext';
import { TutorialProvider } from './contexts/TutorialContext';
import { ErrorBoundary } from './components/ErrorBoundary';
import AppRouter from './components/AppRouter';

// Root app component with proper provider hierarchy
export default function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <AudioProvider>
          <TutorialProvider>
            <StatusBar style="light" />
            <AppRouter />
          </TutorialProvider>
        </AudioProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}
