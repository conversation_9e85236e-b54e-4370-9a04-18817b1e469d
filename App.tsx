import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { AuthProvider } from './contexts/AuthContext';
// import { AudioProvider } from './contexts/AudioContext'; // Temporarily removed
import { TutorialProvider } from './contexts/TutorialContext';
import { ErrorBoundary } from './components/ErrorBoundary';
import AppRouter from './components/AppRouter';

// Root app component - temporarily without AudioProvider
export default function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <TutorialProvider>
          <StatusBar style="light" />
          <AppRouter />
        </TutorialProvider>
      </AuthProvider>
    </ErrorBoundary>
  );
}
