# KidsCoin Edge Functions

This directory contains Supabase Edge Functions that implement the clean API layer for KidsCoin as defined in our PRD.

## Overview

Instead of using auto-generated Supabase APIs directly from the client, we use Edge Functions to create a secure, well-defined REST API. This approach provides:

- **Security**: All business logic runs on the server
- **Validation**: Centralized input validation and error handling  
- **Abstraction**: Frontend only needs to know API endpoints, not database schema
- **Flexibility**: Easy to refactor database without changing frontend code

## API Endpoints

### Family Settings
- `POST /functions/v1/family-settings` - GET/PUT family settings

### Children Management  
- `POST /functions/v1/children` - Create, update, delete children

### Task Completion
- `POST /functions/v1/task-complete` - Handle task completion with verification logic

## Functions Structure

Each function follows this pattern:

1. **CORS Handling** - Support for web client requests
2. **Authentication** - Verify user via Supabase Auth
3. **Authorization** - Check permissions via RLS policies
4. **Business Logic** - Handle the specific operation
5. **Error Handling** - Consistent error responses

## Development

### Testing Functions Locally

To test these functions, you would typically:

1. Start Supabase locally: `supabase start`
2. Deploy functions: `supabase functions deploy`
3. Test with curl or your frontend

### Example Request

```bash
curl -X POST 'https://your-project.supabase.co/functions/v1/family-settings' \
  -H 'Authorization: Bearer YOUR_JWT_TOKEN' \
  -H 'Content-Type: application/json' \
  -d '{"task_verification_mode": "trusting"}'
```

## Missing Functions

The following functions still need to be implemented:

- `task-templates` - CRUD for task templates
- `tasks` - Create and assign tasks  
- `task-approve` - Parent approval of tasks
- `task-generate-code` - Generate verification codes
- `task-complete-with-code` - Complete with parent code
- `rewards` - CRUD for rewards
- `reward-redeem` - Child reward redemption

## Database Functions Required

Some Edge Functions call PostgreSQL functions for complex operations:

- `complete_task(task_id, award_coins)` - Atomically complete task and award coins
- `check_achievement_progress(child_id)` - Check and unlock achievements
- `generate_verification_code(task_id)` - Create short-lived codes

These need to be created as database migrations. 