# 🎵 Audio Configuration Guide

This guide explains how to adjust all audio settings in your KidsCoin app from one centralized location.

## 📍 Location
All audio settings are configured in: `utils/audioConfig.ts` → `AUDIO_CONFIG` object

## 🎛️ Volume Controls

### Master Volume
```typescript
masterVolume: 0.7,  // 70% overall volume (affects everything)
```

### Background Music
```typescript
backgroundMusic: {
  defaultVolume: 0.15,  // 15% base volume for background music
  tracks: {
    onboarding: require('../assets/music/background.mp3'),
    main: require('../assets/music/background.mp3'),
    celebration: require('../assets/sounds/confetti.wav'),
  }
}
```

### Sound Effects
Each sound effect has its own volume level:
```typescript
soundEffects: {
  defaultVolume: 0.8,  // 80% base volume for all sound effects
  files: {
    'button_press': { file: require('../assets/sounds/button-click.wav'), volume: 1.0 },
    'achievement_unlock': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
    'coin_collect': { file: require('../assets/sounds/coin.wav'), volume: 0.8 },
    // ... more sounds
  }
}
```

## 🔢 Volume Calculation

Final volume = `Individual Sound Volume × Sound Effects Volume × Master Volume`

**Example:**
- Button press: `1.0 × 0.8 × 0.7 = 0.56` (56% final volume)
- Achievement: `1.0 × 0.8 × 0.7 = 0.56` (56% final volume)

## 🛠️ How to Adjust Volumes

### Quick Adjustments (Most Common)
1. **Make all audio quieter/louder**: Change `masterVolume`
2. **Adjust background music**: Change `backgroundMusic.defaultVolume`
3. **Adjust all sound effects**: Change `soundEffects.defaultVolume`

### Individual Sound Adjustments
To make specific sounds louder/quieter, change the `volume` property for that sound:

```typescript
// Make coin collection sounds louder
'coin_collect': { file: require('../assets/sounds/coin.wav'), volume: 1.0 },

// Make error sounds quieter  
'error': { file: require('../assets/sounds/button-click.wav'), volume: 0.2 },

// Make celebrations extra loud
'celebration': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
```

## 🎵 Adding New Audio Files

### Background Music
1. Add your music file to `assets/music/`
2. Add entry to `AUDIO_CONFIG.backgroundMusic.tracks`:
```typescript
tracks: {
  onboarding: require('../assets/music/your-new-music.mp3'),
  // ...
}
```

### Sound Effects
1. Add your sound file to `assets/sounds/`
2. Add entry to `AUDIO_CONFIG.soundEffects.files`:
```typescript
files: {
  'new_sound_type': { file: require('../assets/sounds/new-sound.wav'), volume: 0.8 },
  // ...
}
```
3. Add the new sound type to the `SoundType` union in the same file

## 🧪 Testing Audio

Use the **AudioSettings** component to test all your audio changes:
- Adjust master volume with +/- buttons
- Toggle background music and sound effects on/off
- Test individual sounds with the test buttons
- See current volume levels

## 📝 Volume Recommendations

| Sound Type | Recommended Volume | Reason |
|------------|-------------------|---------|
| Button clicks | 0.5-0.7 | Subtle feedback |
| Success/Achievement | 0.9-1.0 | Celebratory |
| Errors | 0.3-0.5 | Not harsh |
| Coin collection | 0.7-0.9 | Satisfying |
| Background music | 0.2-0.4 | Doesn't overpower |

## 🎯 Common Scenarios

**Making the app quieter overall:**
```typescript
masterVolume: 0.5,  // Reduce from 0.7 to 0.5
```

**Making background music less prominent:**
```typescript
backgroundMusic: {
  defaultVolume: 0.1,  // Reduce from 0.15 to 0.1
}
```

**Making button clicks more subtle:**
```typescript
'button_press': { file: require('../assets/sounds/button-click.wav'), volume: 0.4 },
```

**Making achievements more exciting:**
```typescript
'achievement_unlock': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
'celebration': { file: require('../assets/sounds/confetti.wav'), volume: 1.0 },
``` 